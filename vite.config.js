import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import {resolve} from 'node:path';
import {globSync} from 'fast-glob';
import {fileURLToPath} from 'node:url';
import {generatePrivacyFiles} from './scripts/generate-privacy.js';
import {generateLandingFiles} from "./scripts/generate-landing";

// 获取当前文件的目录路径 (ESM 模块中 __dirname 的替代方案)
const __dirname = fileURLToPath(new URL('.', import.meta.url));

// 在构建前生成隐私政策文件
generatePrivacyFiles();
// 生成落地页文件
generateLandingFiles();

const entryPatterns = [
  'landing/*/index.html',
  'shen/*/index.html',
  'privacy/*/*.html',
  'privacy/*.html',
  'views/*.html',
];

const htmlFiles = globSync(entryPatterns);


const rollupInput = Object.fromEntries(
  htmlFiles.map(file => [
    file.slice(0, file.length - '.html'.length),
    // 提供文件的绝对路径给 Rollup
    resolve(__dirname, file)
  ])
);

export default defineConfig({
  plugins: [
    vue(),
    {
      name: 'vite-plugin-js-confuser',
      enforce: 'post',
      async generateBundle(_, bundle) {
        const jsConfuser = require('js-confuser');
        const options = {
          target: 'browser',
          preset: 'low',
          identifierGenerator: 'mangled'
        };
        const obfuscationPromises = [];
        const path = require('path');
        for (const fileName in bundle) {
          const chunk = bundle[fileName];
          if (chunk.type === 'chunk' && fileName.endsWith('.js')) {
            // 通过 chunk.modules 判断是否包含 js/landing.js 或 js/appList.js
            const entryFiles = [
              path.resolve(__dirname, 'js/landing.js'),
              path.resolve(__dirname, 'js/landing-index.js'),
              path.resolve(__dirname, 'js/appList.js'),
            ];
            const modulePaths = Object.keys(chunk.modules || {});
            if (modulePaths.some(modulePath => entryFiles.includes(path.normalize(modulePath)))) {
              obfuscationPromises.push(
                (async () => {
                  try {
                    const result = await jsConfuser.obfuscate(chunk.code, options);
                    chunk.code = result.code;
                  } catch (error) {
                    console.error(`[JSConfuser] Error obfuscating chunk ${fileName}:`, error);
                  }
                })()
              );
            }
          }
        }
        await Promise.all(obfuscationPromises);
      },
    },
  ],
  build: {
    rollupOptions: {
        input: rollupInput,
      output: {
          manualChunks: (id) => {
            // console.log('id', id)
            if (
              id.includes('plugin-vue') ||
              id.includes('node_modules/@vue') ||
              id.includes('node_modules/vue')
            ) {
              return 'vue-vendor'
            }
          }
      }
    },
  }
})