<template>
  <div class="main">
    <div class="row">
      <label for="type-select">选择</label>
      <select v-model="type" id="type-select" class="select">
        <option :value="1">落地页跳转</option>
        <option :value="2">Hap跳转</option>
        <option :value="3">自定义Hap跳转</option>
      </select>
    </div>
    <div class="row">
      <label for="app-select">App</label>
      <select v-model="selectedApp" id="app-select" class="select">
        <option disabled value="">选择应用</option>
        <option v-for="app in apps" :key="app.id" :value="app.id">{{ app.name }}</option>
      </select>
    </div>
    <div class="row">
      <label for="brand-select">厂商</label>
      <select v-model="brand" id="brand-select" class="select">
        <option v-for="item in brands" :key="item" :value="item">{{ item }}</option>
      </select>
    </div>
    <div class="row">
      <label for="channel-select">投放渠道</label>
      <select v-model="channel" id="channel-select" class="select">
        <option v-for="item in channels" :key="item.value" :value="item.value">{{ item.name }}</option>
      </select>
    </div>
    <div class="row" v-if="type === 3">
      <label for="custom-link">自定义链接</label>
      <input type="text" id="custom-link" v-model="customLink" class="input">
    </div>
    <div class="row content-box">
      <p class="content">
        {{ ua }}
      </p>
    </div>
    <div class="row last">
      <button id="gen-click-id" class="but" @click="openApp">打开</button>
    </div>
  </div>
</template>
<script>
import appList from "../appList";

export default {
  data() {
    return {
      apps: appList,
      brands: [
        "oppo",
        "vivo",
        "xiaomi",
        "huawei",
        "honor",
      ],
      channels: [
        {
          "name": "默认",
          "value": "",
        },
        {
          "name": "oppo关键行为",
          "value": "oppo_key",
        },
        {
          "name": "oppo销售线索",
          "value": "oppo_h5",
        },
        {
          "name": "VIVO",
          "value": "vivo",
        },
        {
          "name": "荣耀",
          "value": "honor",
        },
        {
          "name": "快手站内",
          "value": "ksapp",
        },
        {
          "name": "快手联盟",
          "value": "ks",
        },
        {
          "name": "头条",
          "value": "tt_h5",
        }
      ],
      type: 1,
      selectedApp: null,
      ownerIds: '',
      url: '',
      channel: '',
      brand: 'oppo',
      customLink: '',
      ua: navigator.userAgent
    }
  },
  computed: {
  },
  methods: {
    openApp() {
      const app = this.apps.find(app => app.id === this.selectedApp)
      if (!app) return []
      if (this.type === 1) {
        if (this.channel === 'ksapp') {
          location.href = "kwai://webview?url=" + encodeURIComponent(`${location.origin}/landing/${this.selectedApp}/index.html?brand=${this.brand}&ownerId=**********&channel=${this.channel}&callback=1234`);
        } else if (this.channel === 'oppo_key') {
          location.href = "oaps://mk/web?u=" + encodeURIComponent(`${location.origin}/landing/${this.selectedApp}/index.html?brand=${this.brand}&channel=ks&ownerId=**********&clickid=111&callback=1234`);
        } else if (this.channel === 'oppo_h5') {
          location.href = `../landing/${this.selectedApp}/index.html?brand=${this.brand}&ownerId=**********&pageId=1000588997&lbid=1_0_6-08&tid=1-1WETeVh1dtTOxKyYUE8oc2MYNdSFVU6tr5KGvqLCHo8y3jjRdWgwOiciKFIWbNlEaWhlXkew9mv1ibUHqNZq0D2dLL7f5Wr0MLZ73Lktm5QPv+i1UNvRnjpVn3w9O6LOOOJOWovnqw5JhCV4gyL3A3cav+4Pq7oSoMzzBYNLvAs=&channel=${this.channel}`;
        } else {
          location.href = `../landing/${this.selectedApp}/index.html?brand=${this.brand}&ownerId=**********&channel=${this.channel}&clickid=111&callback=1234`;
        }
      } else if (this.type === 2) {
        location.href = `hap://app/${app.package}/pages/Splash?clickid=${this.brand}&click_type=oneJump&account_id=**********&channel=${this.channel}`
      }  else if (this.type === 3) {
        location.href = this.customLink
      }
    },
  },

}
</script>
<style scoped>
.main {
  display: flex;
  flex-direction: column;
  max-width: 96vw;
  height: calc(100vh - 4vw);
  margin: 2vw;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.07);
  padding: 16px 24px 16px 24px;
  min-height: 0;
}
label {
  display: inline-block;
  width: 100px;
  font-weight: 500;
  color: #3a3a3a;
  margin-right: 6px;
  line-height: 36px;
}
.row {
  margin: 16px 0 0 0;
  display: flex;
  align-items: flex-start;
}
.row.content-box {
  background: #f8fafc;
  border-radius: 12px;
  margin: 22px 0 0 0;
  min-height: 0;
  max-height: unset;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.03);
  padding: 12px 10px;
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-x: hidden;
}
.content {
  margin: 0 0 10px 0;
  word-break: break-all;
  font-size: 13px;
  color: #2d5a88;
  background: #ecf4ff;
  border-radius: 7px;
  padding: 7px 10px;
  line-height: 1.8;
  width: 100%;
  white-space: pre-line;
  overflow-wrap: break-word;
  box-sizing: border-box;
  overflow-x: hidden;
}
.last {
  margin: 32px 0 18px 0;
}
.but {
  width: 100%;
  height: 44px;
  background: linear-gradient(90deg, #4f8cff 0%, #62d0ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(79,140,255,0.13);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}
.but:hover {
  background: linear-gradient(90deg, #62d0ff 0%, #4f8cff 100%);
  box-shadow: 0 4px 16px 0 rgba(79,140,255,0.18);
}
.select, .input {
  padding: 8px 12px;
  border: 1px solid #d3e0ee;
  border-radius: 7px;
  font-size: 15px;
  outline: none;
  background: #fafdff;
  transition: border 0.2s;
  min-width: 0;
}
.select:focus, .input:focus {
  border: 1.5px solid #4f8cff;
  background: #f0f7ff;
}
.select {
  min-width: 120px;
  height: 36px;
  margin-right: 8px;
}
.input {
  width: 100%;
  min-height: 30px;
  resize: vertical;
}
@media (max-width: 600px) {
  .main {
    max-width: 96vw;
    min-height: unset;
    padding: 8px 2vw 8px 2vw;
    margin: 2vw;
    height: calc(100vh - 4vw);
  }
  label {
    width: 64px;
    font-size: 14px;
  }
  .but {
    font-size: 15px;
    height: 40px;
  }
  .row.content-box {
    min-height: 0;
    max-height: unset;
  }
}
/* 优化滚动条 */
.content-box::-webkit-scrollbar {
  width: 6px;
  background: #f4f7fa;
}
.content-box::-webkit-scrollbar-thumb {
  background: #dbeafe;
  border-radius: 4px;
}
</style>