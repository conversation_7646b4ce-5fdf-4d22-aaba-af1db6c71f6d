<script>
import appList from "../appList";

export default {
  data() {
    return {
      apps: appList,
      brands: [
        "oppo",
        "vivo",
        "xiaomi",
        "huawei",
        "honor",
      ],
      channels: [
        {
          "name": "默认",
          "value": "",
        },
        {
          "name": "oppo关键行为",
          "value": "oppo_key",
        },
        {
          "name": "oppo销售线索",
          "value": "oppo_h5",
        },
        {
          "name": "VIVO激活",
          "value": "vivo_a",
        },
        {
          "name": "VIVO加桌",
          "value": "vivo_jz",
        },
        {
          "name": "荣耀",
          "value": "honor",
        },
        {
          "name": "华为",
          "value": "huawei",
        },
        {
          "name": "快手",
          "value": "ks",
        },
        {
          "name": "头条激活",
          "value": "tt",
        },
        {
          "name": "头条表单",
          "value": "tt_h5",
        },
        {
          "name": "百度",
          "value": "baidu",
        },
        {
          "name": "UC",
          "value": "uc",
        }
      ],
      type: 1,
      selectedApp: null,
      ownerIds: '',
      url: '',
      channel: '',
      brand: 'oppo',
    }
  },
  computed: {
    showOwnerId() {
      if (this.type === 1) {
        for (let string of ['vivo']) {
          if (this.channel.startsWith(string)) {
            return true
          }
        }
        return false
      }
      return true
    },
    links() {
      if (!this.selectedApp) return []
      const app = this.apps.find(app => app.id === this.selectedApp)
      if (!app) return []
      if (this.type === 1) {
        switch (this.channel) {
          case 'oppo_key':
            return [`https://warehouse-api.springdance.cn/advertise/click?app_id=${this.selectedApp}&union_type=88&market_code=${this.brand}&aid=$ad$&cid=$planid$&advertise_id=$ownerid$&imei_md5=$im$&oaid_md5=__OAID__&ip=__IP__&model=$m$&user_agent=$ua$&pkg_name=${app.package}&callback_type=1&callback_url=$req$&campaign_id=$groupid$`]
          case 'oppo_h5':
            return [`https://warehouse-api.springdance.cn/advertise/click?app_id=${this.selectedApp}&union_type=8&market_code=${this.brand}&aid=$ad$&cid=$planid$&advertise_id=$ownerid$&imei_md5=$im$&oaid_md5=__OAID__&ip=__IP__&model=$m$&user_agent=$ua$&pkg_name=${app.package}&callback_type=1&callback_url=$req$&campaign_id=$groupid$`]
          case 'baidu':
            return [`https://warehouse-api.springdance.cn/advertise/click?advertise_id=__USER_ID__&cid=__PLAN_ID__&imei_md5=__IMEI__&oaid_md5=__OAID_MD5__&ip=__IP__&ts=__TS__&callback_type=1&callback_url=__CALLBACK_URL__&app_id=${this.selectedApp}&union_type=4&market_code=${this.brand}`]
          case "uc":
            return [`https://warehouse-api.springdance.cn/advertise/click?app_id=${this.selectedApp}&union_type=7&market_code=${this.brand}&aid={CID}&cid={AID}&advertise_id={ACID}&imei_md5={IMEI_SUM}&oaid_md5={OAID}&ip={IP}&model={MODEL1}&user_agent={UA}&callback_type=1&callback_url={CALLBACK_URL}&ts={UX_TS}&campaign_id={GID}`]
          case "ks":
            return [`https://warehouse-api.springdance.cn/advertise/click?aid=__DID__&cid=__CID__&csite=__CSITE__&campaign_id=__AID__&advertise_id=__ACCOUNTID__&os_type=__OS__&imei_md5=__IMEI2__&mac_md5=__MAC2__&oaid_md5=__OAID2__&android_md5=__ANDROIDID2__&idfa_md5=__IDFA2__&ip=__IP__&model=__MODEL__&user_agent=__UA__&ts=__TS__&callback_type=1&callback_url=__CALLBACK__&app_id=${this.selectedApp}&union_type=3&market_code=ks`]
          case "tt":
            return [`https://warehouse-api.springdance.cn/advertise/click?app_id=${this.selectedApp}&union_type=1&market_code=jrtt&cid=__AID__&advertise_id=__ADVERTISER_ID__&campaign_id=__CAMPAIGN_ID__&imei_md5=__IMEI__&oaid_md5=__OAID_MD5__&ip=__IP__&model=__MODEL__&user_agent=__UA__&callback_type=1&callback_url=__CALLBACK_PARAM__`]
          case "tt_h5":
            return [`https://warehouse-api.springdance.cn/advertise/click?app_id=${this.selectedApp}&union_type=101&market_code=jrtt&cid=__AID__&advertise_id=__ADVERTISER_ID__&campaign_id=__CAMPAIGN_ID__&ip=__IP__&model=__MODEL__&user_agent=__UA__&callback_type=1&callback_url=__CALLBACK_PARAM__`]
          case 'honor':
            return [`https://warehouse-api.springdance.cn/advertise/click?advertise_id=__ADVERTISER_ID__&cid=__CREATIVE_ID__&campaign_id=__CAMPAIGNID__&os_type=__OS__&oaid_md5=__OAID__&ip=__IP__&user_agent=__UA__&click_ua=&__REQUESTID__ts=__TIME__&callback_type=1&callback_url=__TRACK_ID__&app_id=${this.selectedApp}&union_type=10&market_code=honor`]
          case 'huawei':
            return [`https://warehouse-api.springdance.cn/advertise/feedback?apps_id=${this.selectedApp}&union_type=12&market_code=huawei&callback_type=1`]
        }
      }
      if (this.ownerIds) {
        if (this.type === 1) {
          return this.ownerIds.split("\n").map(it => {
            switch (this.channel) {
              case 'vivo_a':
                return `https://warehouse-api.springdance.cn/advertise/click?imei_md5=__IMEI__&oaid_md5=__OAID__&os_type=__OS__&ip=__IP__&ts=__TS__&callback_type=1&user_agent=__ADVERTISERID__&callback_url=__REQUESTID__&geo=__CREATIVEID__&idfa_md5=${it}&pkg_name=${app.package}&app_id=${this.selectedApp}&union_type=9&market_code=vivo`
              case 'vivo_jz':
                return `https://warehouse-api.springdance.cn/advertise/click?imei_md5=__IMEI__&oaid_md5=__OAID__&os_type=__OS__&ip=__IP__&ts=__TS__&callback_type=1&user_agent=__ADVERTISERID__&callback_url=__REQUESTID__&geo=__CREATIVEID__&idfa_md5=${it}&pkg_name=${app.package}&app_id=${this.selectedApp}&union_type=99&market_code=vivo`
            }
          })
        } else if (this.type === 2) {
          let domain = 'https://landing.serveclouds.com/landing'
          if (this.channel.includes("uc") && app.company === "上海间月网络科技有限公司") {
            domain = 'https://landing.shjysytech.cn/landing'
          }
          return this.ownerIds.split("\n").map(it => `${domain}/${this.selectedApp}/index.html?brand=${this.brand}&channel=${this.channel}&ownerId=${it}`)
        } else if (this.type === 3) {
          return this.ownerIds.split("\n").map(it => {
            let link = `hap://app/${app.package}/pages/Splash?clickid=${this.brand}&click_type=oneJump&channel=${this.channel}&account_id=${it}`
            if (this.channel === 'ks') {
              link += '&callback=__CALLBACK__'
            }
            return link
          })
        }
      }
      return []
    },
  },
  methods: {
    copy() {
      const text = this.links.join('\n')
      navigator.clipboard.writeText(text).then(() => {
        alert('复制成功')
      }).catch(() => {
        alert('复制失败，请手动复制')
      })
    },
  },
};
</script>
<template>
  <div class="main">
    <div class="row">
      <label for="type-select">选择</label>
      <select v-model="type" id="type-select" class="select">
        <option :value="1">监测链接生成</option>
        <option :value="2">落地页生成</option>
        <option :value="3">HAP链接生成</option>
      </select>
    </div>
    <div class="row">
      <label for="app-select">App</label>
      <select v-model="selectedApp" id="app-select" class="select">
        <option disabled value="">选择应用</option>
        <option v-for="app in apps" :key="app.id" :value="app.id">{{ app.name }}</option>
      </select>
    </div>
    <div class="row" v-if="showOwnerId">
      <label for="ownerId" v-if="type === 1 && channel.startsWith('vivo')">数据源ID</label>
      <label for="ownerId" v-else>账户ID</label>
      <textarea id="ownerId"
                v-model="ownerIds"
                class="textarea"
                cols="30"
                placeholder="请输入ID, 一行一个"
                rows="10"
      ></textarea>
    </div>
    <div class="row">
      <label for="brand-select">厂商</label>
      <select v-model="brand" id="brand-select" class="select">
        <option v-for="item in brands" :key="item" :value="item">{{ item }}</option>
      </select>
    </div>
    <div class="row">
      <label for="channel-select">投放渠道</label>
      <select v-model="channel" id="channel-select" class="select">
        <option v-for="item in channels" :key="item.value" :value="item.value">{{ item.name }}</option>
      </select>
    </div>
    <div class="row content-box">
      <p class="content" v-for="link in links" :key="link">
        {{ link }}
      </p>
    </div>
    <div class="row last">
      <button id="gen-click-id" class="but" @click="copy">一键复制</button>
    </div>
  </div>
</template>
<style scoped>
.main {
  display: flex;
  flex-direction: column;
  max-width: 96vw;
  height: calc(100vh - 4vw);
  margin: 2vw;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.07);
  padding: 16px 24px 16px 24px;
  min-height: 0;
  overflow: hidden; /* 防止整体容器溢出 */
}
label {
  display: inline-block;
  width: 80px;
  font-weight: 500;
  color: #3a3a3a;
  margin-right: 6px;
  line-height: 36px;
}
.row {
  margin: 16px 0 0 0;
  display: flex;
  align-items: flex-start;
}
.row.content-box {
  background: #f8fafc;
  border-radius: 12px;
  margin: 22px 0 0 0;
  min-height: 200px; /* 设置最小高度确保可见性 */
  max-height: none; /* 移除最大高度限制 */
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.03);
  padding: 12px 10px;
  flex: 1; /* 占用剩余空间 */
  overflow-y: auto; /* 垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  /* 确保在flex容器中正确收缩 */
  min-height: 0;
}
.content {
  margin: 0 0 10px 0;
  word-break: break-all;
  font-size: 13px;
  color: #2d5a88;
  background: #ecf4ff;
  border-radius: 7px;
  padding: 7px 10px;
  line-height: 1.8;
  width: 100%;
  white-space: pre-line;
  overflow-wrap: break-word;
  box-sizing: border-box;
  overflow-x: hidden;
  /* 确保最后一个元素没有底部边距 */
  flex-shrink: 0; /* 防止内容被压缩 */
}
.content:last-child {
  margin-bottom: 0;
}
.last {
  margin: 32px 0 18px 0;
}
.but {
  width: 100%;
  height: 44px;
  background: linear-gradient(90deg, #4f8cff 0%, #62d0ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(79,140,255,0.13);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}
.but:hover {
  background: linear-gradient(90deg, #62d0ff 0%, #4f8cff 100%);
  box-shadow: 0 4px 16px 0 rgba(79,140,255,0.18);
}
.select, .textarea {
  padding: 8px 12px;
  border: 1px solid #d3e0ee;
  border-radius: 7px;
  font-size: 15px;
  outline: none;
  background: #fafdff;
  transition: border 0.2s;
  min-width: 0;
}
.select:focus, .textarea:focus {
  border: 1.5px solid #4f8cff;
  background: #f0f7ff;
}
.select {
  min-width: 120px;
  height: 36px;
  margin-right: 8px;
}
.textarea {
  width: 100%;
  min-height: 90px;
  resize: vertical;
}
@media (max-width: 600px) {
  .main {
    max-width: 96vw;
    min-height: unset;
    padding: 8px 2vw 8px 2vw;
    margin: 2vw;
    height: calc(100vh - 4vw);
  }
  label {
    width: 64px;
    font-size: 14px;
  }
  .but {
    font-size: 15px;
    height: 40px;
  }
  .row.content-box {
    min-height: 150px; /* 移动端最小高度 */
    max-height: none;
    padding: 8px 6px; /* 减少移动端内边距 */
  }
  .content {
    font-size: 12px; /* 移动端字体稍小 */
    padding: 6px 8px; /* 减少移动端内边距 */
  }
}
/* 优化滚动条 */
.content-box::-webkit-scrollbar {
  width: 6px;
  background: #f4f7fa;
}
.content-box::-webkit-scrollbar-thumb {
  background: #dbeafe;
  border-radius: 4px;
}
.content-box::-webkit-scrollbar-thumb:hover {
  background: #bfdbfe;
}
/* 确保滚动条在移动端也能正常显示 */
.content-box {
  scrollbar-width: thin;
  scrollbar-color: #dbeafe #f4f7fa;
}
</style>