import appList from "/js/appList.js";

let appid = location.pathname.split('/')[2]
let app = appList.find(app => app.id === appid)

if (document.body) {
  initPage()
} else {
  document.addEventListener('DOMContentLoaded', function () {
    initPage()
  });
}

function initPage() {
  // 设置页面标题
  document.title = `${app.name}用户协议`;
  
  // 清空 body 内容
  document.body.innerHTML = '';
  
  // 创建主标题
  const title = document.createElement('h3');
  title.id = 'title';
  title.textContent = `${app.name}用户协议`;
  
  // 创建公司名称
  const companyTitle = document.createElement('h4');
  companyTitle.id = 'h4';
  companyTitle.textContent = `公司名称:${app.company}`;
  
  // 创建快应用名称
  const appTitle = document.createElement('h4');
  appTitle.id = 'kyy';
  appTitle.textContent = `快应用名称:${app.name}`;
  
  // 创建主要内容段落
  const mainContent = document.createElement('p');
  mainContent.innerHTML = `
    本公司按照下列条款与条件提供信息和产品，您在本协议中亦可被称为"用户"，以下所述条款和条件将构成您与本公司，就您使用提供的内容所达成的全部协议（以下称"本协议"）。<br />
    说明<br />
    本公司向您提供包括但不限于应用下载、使用、充值、客户服务、资讯、论坛交流等服务（以下称"本服务"）。本公司针对本服务所制定的相关规定，包括但不限于本公司在应用平台下运营的任何一款应用所包含的规则、用户处罚条例，客服条例等，以及本公司就账号使用及管理、应用充值等服务制定的相关服务协议、规则。本公司在此提示用户，请您在使用本服务前详细阅读本协议的所有内容，尤其是免除、限制本公司责任或者限制用户权利的条款，如您对本协议有任何疑问，请向本公司进行咨询。一旦您使用本服务，即表示您已阅读并完全同意接受本协议项下所述条款和条件的约束。如果您不同意本协议的任何条款，请您不要使用本服务。未成年人应经其监护人陪同阅读本服务协议并表示同意，方可接受本服务协议。监护人应加强对未成年人的监督和保护，因其未谨慎履行监护责任而损害未成年人利益或者本公司利益的，应由监护人承担责任。<br />
  `;
  
  // 添加换行
  const br = document.createElement('br');
  
  // 添加所有元素到 body
  document.body.appendChild(title);
  document.body.appendChild(companyTitle);
  document.body.appendChild(appTitle);
  document.body.appendChild(mainContent);
  document.body.appendChild(br);
  
  // 添加其他章节
  addSection1();
  addSection2();
  addSection3();
  addSection4();
  addSection5();
}

function addSection1() {
  const section = document.createElement('h4');
  section.textContent = '一、服务内容';
  
  const content = document.createElement('p');
  content.innerHTML = `
    1.1 本公司通过互联网向用户提供各种信息服务。<br />
    1.2 用户必须自行准备如下设备和承担如下开支：（1）上网设备，包括并不限于电脑或者其他上网终端、调制解调器及其他必备的上网装置；（2）上网开支，包括并不限于网络接入费、上网设备租用费、手机流量费等。<br />
  `;
  
  document.body.appendChild(section);
  document.body.appendChild(content);
}

function addSection2() {
  const section = document.createElement('h4');
  section.textContent = '二、用户账号';
  
  const content = document.createElement('p');
  content.innerHTML = `
    2.1 用户有责任保证密码和账号的安全，用户利用该密码和账号所进行的一切活动引起的任何损失或损害，由用户自行承担全部责任，本公司不承担任何责任。如用户发现账号遭到未授权的使用或发生其他任何安全问题，应立即修改账号密码并妥善保管，如有必要，请通知本公司。因黑客行为或用户的保管疏忽导致账号非法使用，本公司不承担任何责任。<br />
    2.2 用户应当通过本公司提供的方式充值，不得以任何形式私下交易，否则由此产生的任何后果均由用户自行承担。<br />
  `;
  
  document.body.appendChild(section);
  document.body.appendChild(content);
}

function addSection3() {
  const section = document.createElement('h4');
  section.textContent = '三、使用规则';
  
  const content = document.createElement('p');
  content.innerHTML = `
    3.1 用户在申请使用本服务时，必须向本公司提供准确的个人资料，如个人资料有任何变动，必须及时更新。<br />
    3.2 用户不应将其账号、密码转让或出借予他人使用。如用户发现其账号遭他人非法使用，应立即通知本公司。因黑客行为或用户的保管疏忽导致账号、密码遭他人非法使用，本公司不承担任何责任。<br />
    3.3 用户同意遵守《中华人民共和国保守国家秘密法》、《中华人民共和国计算机信息系统安全保护条例》、《计算机软件保护条例》、《互联网电子公告服务管理规定》、《信息网络传播权保护条例》等有关计算机及互联网规定的法律和法规、实施办法。<br />
  `;
  
  document.body.appendChild(section);
  document.body.appendChild(content);
}

function addSection4() {
  const section = document.createElement('h4');
  section.textContent = '四、服务条款的修改';
  
  const content = document.createElement('p');
  content.innerHTML = `
    本公司会不定时地修改服务条款，服务条款一旦发生变动，将会在相关页面上提示修改内容。如果您同意改动，则再一次点击"我同意"按钮。 如果您不接受，则及时取消您的用户使用服务资格。<br />
  `;
  
  document.body.appendChild(section);
  document.body.appendChild(content);
}

function addSection5() {
  const section = document.createElement('h4');
  section.textContent = '五、服务修订';
  
  const content = document.createElement('p');
  content.innerHTML = `
    本公司保留随时修改或中断服务而不需知照用户的权利。本公司行使修改或中断服务的权利，不需对用户或第三方负责。<br />
  `;
  
  document.body.appendChild(section);
  document.body.appendChild(content);
}
