import appList from "/js/appList.js";

let appid = location.pathname.split('/')[2].replaceAll("-ks", "")
let app = appList.find(app => app.id === appid)
if (document.body) {
  initPage()
} else {
  document.addEventListener('DOMContentLoaded', function () {
    initPage()
  });
}

function initPage() {
  const style = document.createElement('style');
  style.textContent = `
    body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #fff;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 500px;
            min-height: 100vh;
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .header-item {
            margin: 5px;
            font-size: 13px;
            color: #555;
        }

        .bg {
            width: 100%;
            flex-grow: 1;
        }
  `;
  document.head.appendChild(style);

  // 动态创建 container
  const container = document.createElement('div');
  container.className = 'container';

  // header
  const header = document.createElement('div');
  header.className = 'header';

  // header-item 1
  const headerItem1 = document.createElement('div');
  headerItem1.className = 'header-item';
  const span1 = document.createElement('span');
  span1.style.marginRight = '10px';
  span1.textContent = `快应用名称: ${app.name}`;
  const span2 = document.createElement('span');
  span2.textContent = '快应用版本: 1.0.0';
  headerItem1.appendChild(span1);
  headerItem1.appendChild(span2);

  // header-item 2
  const headerItem2 = document.createElement('div');
  headerItem2.className = 'header-item';
  headerItem2.textContent = `开发者: 上海从彧网络科技有限公司`;

  // header-item 3
  const headerItem3 = document.createElement('div');
  headerItem3.className = 'header-item';
  const a1 = document.createElement('a');
  a1.href = '/privacy/permission.html';
  a1.style.marginRight = '20px';
  a1.textContent = '权限说明>>';
  const a2 = document.createElement('a');
  a2.href = `/privacy/${appid}/PrivacyPolicy.html`;
  a2.style.marginRight = '20px';
  a2.textContent = '隐私政策>>';
  headerItem3.appendChild(a1);
  headerItem3.appendChild(a2);

  // header 组装
  header.appendChild(headerItem1);
  header.appendChild(headerItem2);
  header.appendChild(headerItem3);

  // bg 图片
  const bgImg = document.createElement('img');
  bgImg.className = 'bg';
  bgImg.src = `/images/landing/${appid}.webp`;
  bgImg.onerror = function () {
    bgImg.onerror = null
    bgImg.src = `/images/landing/10340.webp`;
  };
  bgImg.addEventListener('click', function () {
    window.location.href = `hap://app/${app.package}/pages/Flash`;
  });

  // 组装 container
  container.appendChild(header);
  container.appendChild(bgImg);

  // 挂载到 body
  document.body.appendChild(container);
}