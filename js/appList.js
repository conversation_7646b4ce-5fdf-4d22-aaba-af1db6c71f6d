export default [
  {
    "id": "10247",
    "name": "365天气",
    "package": "cn.three.weather",
    "company": "上海守耘网络科技有限公司"
  },
  {
    "id": "10227",
    "name": "守恒扫描OCR",
    "package": "shou.hen.oc.red",
    "company": "上海守耘网络科技有限公司"
  },
  {
    "id": "10304",
    "name": "幸运点击",
    "package": "com.fortunetap.luck.app",
    "company": "上海守耘网络科技有限公司"
  },
  {
    "id": "10308",
    "name": "云舟",
    "package": "com.zencloud.yunzhou.utility",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10311",
    "name": "星阙",
    "package": "com.novastar.xingque.toolsuite",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10310",
    "name": "光屿",
    "package": "com.luminary.guangyu.core",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10312",
    "name": "简行",
    "package": "com.streamline.jianxing.framework",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10313",
    "name": "霄隐",
    "package": "com.skyveil.xiaoyin.system",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10309",
    "name": "全能大师",
    "package": "com.probox.master",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10314",
    "name": "掌上工具王",
    "package": "com.pocketpro.utils",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10315",
    "name": "快用管家",
    "package": "com.fasthub.manager",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10316",
    "name": "万能百宝箱",
    "package": "com.utilmate.pack",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10317",
    "name": "随心工具集",
    "package": "com.swiftaid.helper",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10328",
    "name": "霄界",
    "package": "com.xiaoje.quantumflux",
    "company": "上海守耘网络科技有限公司"
  },
  {
    "id": "10329",
    "name": "云墟",
    "package": "io.yunxu.polarisdb",
    "company": "上海守耘网络科技有限公司"
  },
  {
    "id": "10330",
    "name": "雾栈",
    "package": "org.wuzhan.nebulaflow",
    "company": "上海守耘网络科技有限公司"
  },
  {
    "id": "10331",
    "name": "云珩",
    "package": "app.uvmb.ktrq.skyjade",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10332",
    "name": "烁隐",
    "package": "net.xwrd.fplk.shimmerhide",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10333",
    "name": "寂岚",
    "package": "io.jdkn.xaow.mistquiet",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10334",
    "name": "听宸",
    "package": "com.cnyx.qwep.soundrealm",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10335",
    "name": "澜墨",
    "package": "com.vlmq.yxdz.inkflow",
    "company": "上海间月网络科技有限公司"
  },
  {
    "id": "10336",
    "name": "知崖",
    "package": "com.yzqn.mypv.edgewise",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10337",
    "name": "镜屿",
    "package": "io.kylr.vtnc.islemirror",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10338",
    "name": "栖微",
    "package": "app.xypm.lroq.dwelllight",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10339",
    "name": "夙澈",
    "package": "com.gnke.xptz.dawnaura",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10340",
    "name": "澈霁",
    "package": "net.ytnc.mveo.skyclear",
    "company": "上海从彧网络科技有限公司"
  },
  {
    "id": "10341",
    "name": "Zino工具盒",
    "package": "com.zinoapps.toolbox.master",
    "company": "上海从均网络科技有限公司"
  },
  {
    "id": "10342",
    "name": "序岛",
    "package": "com.xudao.launch",
    "company": "上海飞秒引能网络科技有限公司"
  },
  {
    "id": "10343",
    "name": "Lumo多能助手",
    "package": "com.pikoworks.toolbox.prokit",
    "company": "上海从均网络科技有限公司"
  },
  {
    "id": "10344",
    "name": "Tidu优选工具箱",
    "package": "com.tidu.sysutils.toolbox.selector",
    "company": "上海从均网络科技有限公司"
  },
  {
    "id": "10345",
    "name": "Yora生活宝盒",
    "package": "com.yoralabs.life.tools.suite",
    "company": "上海从均网络科技有限公司"
  },
  {
    "id": "10346",
    "name": "Melu多宝助手",
    "package": "com.melu.apphub.multi.assistbox",
    "company": "上海从均网络科技有限公司"
  },
  {
    "id": "10347",
    "name": "星云集",
    "package": "app.xingyun.hub",
    "company": "上海千维矩阵网络科技有限公司"
  },
  {
    "id": "10348",
    "name": "云久行",
    "package": "net.yunjiuxing.core",
    "company": "上海千维矩阵网络科技有限公司"
  },
  {
    "id": "10349",
    "name": "青语者",
    "package": "ai.qingyu.talker",
    "company": "上海千维矩阵网络科技有限公司"
  },
  {
    "id": "10350",
    "name": "空知界",
    "package": "zone.kongzhi.realm",
    "company": "上海千维矩阵网络科技有限公司"
  },
  {
    "id": "10351",
    "name": "灵光志",
    "package": "app.lingguang.memo",
    "company": "上海千维矩阵网络科技有限公司"
  },
  {
    "id": "10352",
    "name": "云柚岛",
    "package": "net.yunyoudao.space",
    "company": "上海飞秒引能网络科技有限公司"
  },
  {
    "id": "10353",
    "name": "镜存",
    "package": "net.jingcun.vault",
    "company": "上海飞秒引能网络科技有限公司"
  },
  {
    "id": "10354",
    "name": "晓行图",
    "package": "app.xiaoxingtu.flow",
    "company": "上海飞秒引能网络科技有限公司"
  },
  {
    "id": "10355",
    "name": "零界语",
    "package": "ai.lingjieyu.text",
    "company": "上海飞秒引能网络科技有限公司"
  },
  {
    "id": "10356",
    "name": "青维",
    "package": "net.qingwei.core",
    "company": "上海飞秒引能网络科技有限公司"
  },
  {
    "id": "10357",
    "name": "零宙",
    "package": "com.lingzhou.root",
    "company": "上海图灵引擎网络科技有限公司"
  },
  {
    "id": "10358",
    "name": "白序",
    "package": "io.baixu.base",
    "company": "上海图灵引擎网络科技有限公司"
  },
  {
    "id": "10359",
    "name": "闪光狸",
    "package": "light.shanli.flashfox",
    "company": "上海图灵引擎网络科技有限公司"
  },
  {
    "id": "10360",
    "name": "哇粒粒",
    "package": "fun.walili.pop",
    "company": "上海图灵引擎网络科技有限公司"
  }
]