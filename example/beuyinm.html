<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title></title>
</head>
<body>
<script>const RTCWait = 1000
const POST_MSG_TYPE = {dual: 'dual', error: 'error',}

function postMsg(msgObj) {
  system.postMessage(JSON.stringify(msgObj))
}

const findIP = async () => new Promise((resolve, reject) => {
  const RTCC = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection
  if (!RTCC) {
    reject(new Error('not supported'))
    return
  }
  const urls = 'stun:stun.chat.bilibili.com:3478'
  const ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3})/g
  const localIPs = {}
  const handleIP = ip => {
    localIPs[ip] = true
  }
  const timeout = setTimeout(() => resolve([]), RTCWait) // 超时判断
  const createConnection = () => { const pc = new RTCC({ iceServers: [{ urls }] })
    pc.createDataChannel('') // 创建一个数据通道，仅用于触发 ICE 候选
    pc.createOffer() .then((sdp = {}) => { sdp.sdp.split('\n').forEach(line => { if (line.includes('candidate')) { const ips = line.match(ipRegex) ips &amp;&amp; ips.forEach(handleIP) } }) pc.setLocalDescription(sdp) }) .catch(e => reject(e)) pc.onicecandidate = (evt = {}) => { const { candidate } = evt.candidate || {} if (candidate) { const ips = candidate.match(ipRegex) ips &amp;&amp; ips.forEach(handleIP) } else { // 所有 ICE 候选解析完成，返回结果 clearTimeout(timeout) resolve(Object.keys(localIPs)) pc.close() } } } createConnection() }) ;(async () => { try { const ips = await findIP() postMsg({ type: POST_MSG_TYPE.dual, msg: ips.join(',') }) } catch (e) {} })()</script>
</body>
</html>