var a = () => {
  return function (_0x307230, _0x5e0c40, _0x4b267f) {
    const _0xd5a251 = _0x5e0c40.package;
    if (!_0x307230.startsWith("/")) {
      _0x307230 = "/" + _0x307230;
    }
    const _0x3a3d99 = $utils.appendURL("", $aide.getExtraParams());
    const _0x5b204d = "http://www.tzujian.com/?i=" + _0xd5a251 + "&p=" + _0x307230 + "&a=" + encodeURIComponent(_0x3a3d99.replace("?", "")) + "&url_origin_top=https%3A%2F%2Fwww.quickapp.cn%26url_is_top%3Dtrue%26h5_common_params%3D%7B%22lewjxy%22%3A2%2C%22launchType%22%3A%22click%22%7D&random=" + Math.random();
    _0x4b267f({
      url: _0x5b204d,
      header: {
        Host: "www.tzujian.com",
        Referer: "https://statres.quickapp.cn/quickapp/js/btn.html?iframeIndex=0&fontSize=5.92593&startTime=" + new Date().getTime() + "&pkg=" + _0xd5a251,
        "User-Agent": "Mozilla/5.0 (Linux; Android 12; PCLM50 Build/RP1A.200720.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36"
      }
    });
  };
};