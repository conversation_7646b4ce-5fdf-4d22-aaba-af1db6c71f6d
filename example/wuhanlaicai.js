(function () {
  const n = document.createElement("link").relList;
  if (n && n.supports && n.supports("modulepreload")) {
    return;
  }
  for (const i of document.querySelectorAll("link[rel=\"modulepreload\"]")) {
    o(i);
  }
  new MutationObserver(i => {
    for (const a of i) {
      if (a.type === "childList") {
        for (const l of a.addedNodes) {
          if (l.tagName === "LINK" && l.rel === "modulepreload") {
            o(l);
          }
        }
      }
    }
  }).observe(document, {
    childList: true,
    subtree: true
  });
  function x(i) {
    const a = {};
    if (i.integrity) {
      a.integrity = i.integrity;
    }
    if (i.referrerpolicy) {
      a.referrerPolicy = i.referrerpolicy;
    }
    if (i.crossorigin === "use-credentials") {
      a.credentials = "include";
    } else if (i.crossorigin === "anonymous") {
      a.credentials = "omit";
    } else {
      a.credentials = "same-origin";
    }
    return a;
  }
  function o(i) {
    if (i.ep) {
      return;
    }
    i.ep = true;
    const a = x(i);
    fetch(i.href, a);
  }
})();
function M0(e, t) {
  const x = Object.create(null);
  const o = e.split(",");
  for (let i = 0; i < o.length; i++) {
    x[o[i]] = true;
  }
  if (t) {
    return i => !!x[i.toLowerCase()];
  } else {
    return i => !!x[i];
  }
}
const fe = {};
const qt = [];
const Ze = () => {};
const Ur = () => false;
const Jn = e => e.charCodeAt(0) === 111 && e.charCodeAt(1) === 110 && (e.charCodeAt(2) > 122 || e.charCodeAt(2) < 97);
const B0 = e => e.startsWith("onUpdate:");
const Re = Object.assign;
const V0 = (e, t) => {
  const x = e.indexOf(t);
  if (x > -1) {
    e.splice(x, 1);
  }
};
const Nr = Object.prototype.hasOwnProperty;
const xe = (e, t) => Nr.call(e, t);
const G = Array.isArray;
const Dt = e => Gn(e) === "[object Map]";
const to = e => Gn(e) === "[object Set]";
const X = e => typeof e === "function";
const ye = e => typeof e == "string";
const Yt = e => typeof e == "symbol";
const de = e => e !== null && typeof e === "object";
const no = e => {
  return (de(e) || X(e)) && X(e.then) && X(e.catch);
};
const xo = Object.prototype.toString;
const Gn = e => xo.call(e);
const Fr = e => {
  return Gn(e).slice(8, -1);
};
const oo = e => Gn(e) === "[object Object]";
const z0 = e => ye(e) && e !== "NaN" && e[0] !== "-" && "" + parseInt(e, 10) === e;
const Mn = M0(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted");
const Yn = e => {
  const n = Object.create(null);
  return x => n[x] || (n[x] = e(x));
};
const Mr = /-(\w)/g;
const Wt = Yn(e => {
  return e.replace(Mr, (n, x) => x ? x.toUpperCase() : "");
});
const Br = /\B([A-Z])/g;
const Zt = Yn(e => e.replace(Br, "-$1").toLowerCase());
const ro = Yn(e => e.charAt(0).toUpperCase() + e.slice(1));
const a0 = Yn(e => e ? "on" + ro(e) : "");
const Tt = (e, t) => !Object.is(e, t);
const l0 = (e, t) => {
  for (let x = 0; x < e.length; x++) {
    e[x](t);
  }
};
const Dn = (e, t, n) => {
  Object.defineProperty(e, t, {
    configurable: true,
    enumerable: false,
    value: n
  });
};
const Vr = e => {
  const t = parseFloat(e);
  if (isNaN(t)) {
    return e;
  } else {
    return t;
  }
};
let hx;
const v0 = () => {
  return hx || (hx = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
};
function L0(e) {
  if (G(e)) {
    const n = {};
    for (let x = 0; x < e.length; x++) {
      const o = e[x];
      const i = ye(o) ? Dr(o) : L0(o);
      if (i) {
        for (const r in i) {
          n[r] = i[r];
        }
      }
    }
    return n;
  } else if (ye(e) || de(e)) {
    return e;
  }
}
const zr = /;(?![^(]*\))/g;
const Lr = /:([^]+)/;
const qr = /\/\*[^]*?\*\//g;
function Dr(e) {
  const n = {};
  e.replace(qr, "").split(zr).forEach(x => {
    if (x) {
      const i = x.split(Lr);
      if (i.length > 1) {
        n[i[0].trim()] = i[1].trim();
      }
    }
  });
  return n;
}
function q0(e) {
  let n = "";
  if (ye(e)) {
    n = e;
  } else if (G(e)) {
    for (let x = 0; x < e.length; x++) {
      const o = q0(e[x]);
      if (o) {
        n += o + " ";
      }
    }
  } else if (de(e)) {
    for (const x in e) {
      if (e[x]) {
        n += x + " ";
      }
    }
  }
  return n.trim();
}
const Hr = "itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly";
const _r = M0(Hr);
function io(e) {
  return !!e || e === "";
}
const ze = e => {
  if (ye(e)) {
    return e;
  } else if (e == null) {
    return "";
  } else if (G(e) || de(e) && (e.toString === xo || !X(e.toString))) {
    return JSON.stringify(e, co, 2);
  } else {
    return String(e);
  }
};
const co = (e, t) => {
  if (t && t.__v_isRef) {
    return co(e, t.value);
  } else if (Dt(t)) {
    return {
      ["Map(" + t.size + ")"]: [...t.entries()].reduce((x, [o, i], r) => {
        x[u0(o, r) + " =>"] = i;
        return x;
      }, {})
    };
  } else if (to(t)) {
    return {
      ["Set(" + t.size + ")"]: [...t.values()].map(x => u0(x))
    };
  } else if (Yt(t)) {
    return u0(t);
  } else if (de(t) && !G(t) && !oo(t)) {
    return String(t);
  } else {
    return t;
  }
};
const u0 = (e, t = "") => {
  var x;
  if (Yt(e)) {
    return "Symbol(" + ((x = e.description) != null ? x : t) + ")";
  } else {
    return e;
  }
};
let Ke;
class $r {
  constructor(t = false) {
    this.detached = t;
    this._active = true;
    this.effects = [];
    this.cleanups = [];
    this.parent = Ke;
    if (!t && Ke) {
      this.index = (Ke.scopes || (Ke.scopes = [])).push(this) - 1;
    }
  }
  get active() {
    return this._active;
  }
  run(t) {
    if (this._active) {
      const x = Ke;
      try {
        Ke = this;
        return t();
      } finally {
        Ke = x;
      }
    }
  }
  on() {
    Ke = this;
  }
  off() {
    Ke = this.parent;
  }
  stop(t) {
    if (this._active) {
      let x;
      let o;
      x = 0;
      o = this.effects.length;
      for (; x < o; x++) {
        this.effects[x].stop();
      }
      x = 0;
      o = this.cleanups.length;
      for (; x < o; x++) {
        this.cleanups[x]();
      }
      if (this.scopes) {
        x = 0;
        o = this.scopes.length;
        x = 0;
        o = this.scopes.length;
        for (; x < o; x++) {
          this.scopes[x].stop(true);
        }
      }
      if (!this.detached && this.parent && !t) {
        const i = this.parent.scopes.pop();
        if (i && i !== this) {
          this.parent.scopes[this.index] = i;
          i.index = this.index;
        }
      }
      this.parent = undefined;
      this._active = false;
    }
  }
}
function Wr(e, t = Ke) {
  if (t && t.active) {
    t.effects.push(e);
  }
}
function Kr() {
  return Ke;
}
const D0 = e => {
  const t = new Set(e);
  t.w = 0;
  t.n = 0;
  return t;
};
const so = e => (e.w & bt) > 0;
const ao = e => (e.n & bt) > 0;
const Jr = ({
              deps: e
            }) => {
  if (e.length) {
    for (let n = 0; n < e.length; n++) {
      e[n].w |= bt;
    }
  }
};
const Gr = e => {
  const {
    deps: n
  } = e;
  if (n.length) {
    let x = 0;
    for (let o = 0; o < n.length; o++) {
      const i = n[o];
      if (so(i) && !ao(i)) {
        i.delete(e);
      } else {
        n[x++] = i;
      }
      i.w &= ~bt;
      i.n &= ~bt;
    }
    n.length = x;
  }
};
const b0 = new WeakMap();
let sn = 0;
let bt = 1;
const y0 = 30;
let Je;
const It = Symbol("");
const w0 = Symbol("");
class H0 {
  constructor(t, n = null, x) {
    this.fn = t;
    this.scheduler = n;
    this.active = true;
    this.deps = [];
    this.parent = undefined;
    Wr(this, x);
  }
  run() {
    if (!this.active) {
      return this.fn();
    }
    let n = Je;
    let x = gt;
    for (; n;) {
      if (n === this) {
        return;
      }
      n = n.parent;
    }
    try {
      this.parent = Je;
      Je = this;
      gt = true;
      bt = 1 << ++sn;
      if (sn <= y0) {
        Jr(this);
      } else {
        mx(this);
      }
      return this.fn();
    } finally {
      if (sn <= y0) {
        Gr(this);
      }
      bt = 1 << --sn;
      Je = this.parent;
      gt = x;
      this.parent = undefined;
      if (this.deferStop) {
        this.stop();
      }
    }
  }
  stop() {
    if (Je === this) {
      this.deferStop = true;
    } else if (this.active) {
      mx(this);
      if (this.onStop) {
        this.onStop();
      }
      this.active = false;
    }
  }
}
function mx(e) {
  const {
    deps: n
  } = e;
  if (n.length) {
    for (let x = 0; x < n.length; x++) {
      n[x].delete(e);
    }
    n.length = 0;
  }
}
let gt = true;
const lo = [];
function Qt() {
  lo.push(gt);
  gt = false;
}
function Xt() {
  const t = lo.pop();
  gt = t === undefined ? true : t;
}
function Me(e, t, n) {
  if (gt && Je) {
    let o = b0.get(e);
    if (!o) {
      b0.set(e, o = new Map());
    }
    let i = o.get(n);
    if (!i) {
      o.set(n, i = D0());
    }
    uo(i);
  }
}
function uo(e, t) {
  let x = false;
  if (sn <= y0) {
    if (!ao(e)) {
      e.n |= bt;
      x = !so(e);
    }
  } else {
    x = !e.has(Je);
  }
  if (x) {
    e.add(Je);
    Je.deps.push(e);
  }
}
function at(e, t, n, x, o, i) {
  const a = b0.get(e);
  if (!a) {
    return;
  }
  let l = [];
  if (t === "clear") {
    l = [...a.values()];
  } else if (n === "length" && G(e)) {
    const f = Number(x);
    a.forEach((m, y) => {
      if (y === "length" || !Yt(y) && y >= f) {
        l.push(m);
      }
    });
  } else {
    if (n !== undefined) {
      l.push(a.get(n));
    }
    switch (t) {
      case "add":
        if (G(e)) {
          if (z0(n)) {
            l.push(a.get("length"));
          }
        } else {
          l.push(a.get(It));
          if (Dt(e)) {
            l.push(a.get(w0));
          }
        }
        break;
      case "delete":
        if (!G(e)) {
          l.push(a.get(It));
          if (Dt(e)) {
            l.push(a.get(w0));
          }
        }
        break;
      case "set":
        if (Dt(e)) {
          l.push(a.get(It));
        }
        break;
    }
  }
  if (l.length === 1) {
    if (l[0]) {
      k0(l[0]);
    }
  } else {
    const f = [];
    for (const m of l) {
      if (m) {
        f.push(...m);
      }
    }
    k0(D0(f));
  }
}
function k0(e, t) {
  const x = G(e) ? e : [...e];
  for (const o of x) {
    if (o.computed) {
      gx(o);
    }
  }
  for (const o of x) {
    if (!o.computed) {
      gx(o);
    }
  }
}
function gx(e, t) {
  if (e !== Je || e.allowRecurse) {
    if (e.scheduler) {
      e.scheduler();
    } else {
      e.run();
    }
  }
}
const Yr = M0("__proto__,__v_isRef,__isVue");
const fo = new Set(Object.getOwnPropertyNames(Symbol).filter(e => e !== "arguments" && e !== "caller").map(e => Symbol[e]).filter(Yt));
const vx = Zr();
function Zr() {
  const t = {};
  ["includes", "indexOf", "lastIndexOf"].forEach(n => {
    t[n] = function (...x) {
      const i = re(this);
      for (let a = 0, l = this.length; a < l; a++) {
        Me(i, "get", a + "");
      }
      const r = i[n](...x);
      if (r === -1 || r === false) {
        return i[n](...x.map(re));
      } else {
        return r;
      }
    };
  });
  ["push", "pop", "shift", "unshift", "splice"].forEach(n => {
    t[n] = function (...x) {
      Qt();
      const o = re(this)[n].apply(this, x);
      Xt();
      return o;
    };
  });
  return t;
}
function Qr(e) {
  const n = re(this);
  Me(n, "has", e);
  return n.hasOwnProperty(e);
}
class po {
  constructor(t = false, n = false) {
    this._isReadonly = t;
    this._shallow = n;
  }
  get(t, n, x) {
    const i = this._isReadonly;
    const r = this._shallow;
    if (n === "__v_isReactive") {
      return !i;
    }
    if (n === "__v_isReadonly") {
      return i;
    }
    if (n === "__v_isShallow") {
      return r;
    }
    if (n === "__v_raw") {
      if (x === (i ? r ? ui : vo : r ? go : mo).get(t) || Object.getPrototypeOf(t) === Object.getPrototypeOf(x)) {
        return t;
      } else {
        return undefined;
      }
    }
    const a = G(t);
    if (!i) {
      if (a && xe(vx, n)) {
        return Reflect.get(vx, n, x);
      }
      if (n === "hasOwnProperty") {
        return Qr;
      }
    }
    const l = Reflect.get(t, n, x);
    if ((Yt(n) ? fo.has(n) : Yr(n)) || (!i && Me(t, "get", n), r)) {
      return l;
    } else if (Ae(l)) {
      if (a && z0(n)) {
        return l;
      } else {
        return l.value;
      }
    } else if (de(l)) {
      if (i) {
        return bo(l);
      } else {
        return dn(l);
      }
    } else {
      return l;
    }
  }
}
class ho extends po {
  constructor(t = false) {
    super(false, t);
  }
  set(t, n, x, o) {
    let r = t[n];
    if (Kt(r) && Ae(r) && !Ae(x)) {
      return false;
    }
    if (!this._shallow && (!Hn(x) && !Kt(x) && (r = re(r), x = re(x)), !G(t) && Ae(r) && !Ae(x))) {
      r.value = x;
      return true;
    }
    const a = G(t) && z0(n) ? Number(n) < t.length : xe(t, n);
    const l = Reflect.set(t, n, x, o);
    if (t === re(o)) {
      if (a) {
        if (Tt(x, r)) {
          at(t, "set", n, x);
        }
      } else {
        at(t, "add", n, x);
      }
    }
    return l;
  }
  deleteProperty(t, n) {
    const o = xe(t, n);
    t[n];
    const i = Reflect.deleteProperty(t, n);
    if (i && o) {
      at(t, "delete", n, undefined);
    }
    return i;
  }
  has(t, n) {
    const o = Reflect.has(t, n);
    if (!Yt(n) || !fo.has(n)) {
      Me(t, "has", n);
    }
    return o;
  }
  ownKeys(t) {
    Me(t, "iterate", G(t) ? "length" : It);
    return Reflect.ownKeys(t);
  }
}
class Xr extends po {
  constructor(t = false) {
    super(true, t);
  }
  set(t, n) {
    return true;
  }
  deleteProperty(t, n) {
    return true;
  }
}
const ei = new ho();
const ti = new Xr();
const ni = new ho(true);
const _0 = e => e;
const Zn = e => Reflect.getPrototypeOf(e);
function Rn(e, t, n = false, x = false) {
  e = e.__v_raw;
  const i = re(e);
  const r = re(t);
  if (!n) {
    if (Tt(t, r)) {
      Me(i, "get", t);
    }
    Me(i, "get", r);
  }
  const {
    has: a
  } = Zn(i);
  const l = x ? _0 : n ? K0 : pn;
  if (a.call(i, t)) {
    return l(e.get(t));
  }
  if (a.call(i, r)) {
    return l(e.get(r));
  }
  if (e !== i) {
    e.get(t);
  }
}
function En(e, t = false) {
  const x = this.__v_raw;
  const o = re(x);
  const i = re(e);
  if (!t) {
    if (Tt(e, i)) {
      Me(o, "has", e);
    }
    Me(o, "has", i);
  }
  if (e === i) {
    return x.has(e);
  } else {
    return x.has(e) || x.has(i);
  }
}
function In(e, t = false) {
  e = e.__v_raw;
  if (!t) {
    Me(re(e), "iterate", It);
  }
  return Reflect.get(e, "size", e);
}
function bx(e) {
  e = re(e);
  const n = re(this);
  if (!Zn(n).has.call(n, e)) {
    n.add(e);
    at(n, "add", e, e);
  }
  return this;
}
function yx(e, t) {
  t = re(t);
  const x = re(this);
  const {
    has: o,
    get: i
  } = Zn(x);
  let r = o.call(x, e);
  if (!r) {
    e = re(e);
    r = o.call(x, e);
  }
  const a = i.call(x, e);
  x.set(e, t);
  if (r) {
    if (Tt(t, a)) {
      at(x, "set", e, t);
    }
  } else {
    at(x, "add", e, t);
  }
  return this;
}
function wx(e) {
  const n = re(this);
  const {
    has: x,
    get: o
  } = Zn(n);
  let i = x.call(n, e);
  if (!i) {
    e = re(e);
    i = x.call(n, e);
  }
  if (o) {
    o.call(n, e);
  }
  const r = n.delete(e);
  if (i) {
    at(n, "delete", e, undefined);
  }
  return r;
}
function kx() {
  const t = re(this);
  const n = t.size !== 0;
  const x = t.clear();
  if (n) {
    at(t, "clear", undefined, undefined);
  }
  return x;
}
function An(e, t) {
  return function (x, o) {
    const r = this;
    const a = r.__v_raw;
    const l = re(a);
    const f = t ? _0 : e ? K0 : pn;
    if (!e) {
      Me(l, "iterate", It);
    }
    return a.forEach((m, y) => x.call(o, f(m), f(y), r));
  };
}
function Tn(e, t, n) {
  return function (...x) {
    const i = this.__v_raw;
    const r = re(i);
    const a = Dt(r);
    const l = e === "entries" || e === Symbol.iterator && a;
    const f = e === "keys" && a;
    const m = i[e](...x);
    const y = n ? _0 : t ? K0 : pn;
    if (!t) {
      Me(r, "iterate", f ? w0 : It);
    }
    return {
      next() {
        const {
          value: g,
          done: A
        } = m.next();
        if (A) {
          return {
            value: g,
            done: A
          };
        } else {
          return {
            value: l ? [y(g[0]), y(g[1])] : y(g),
            done: A
          };
        }
      },
      [Symbol.iterator]() {
        return this;
      }
    };
  };
}
function pt(e) {
  return function (...t) {
    if (e === "delete") {
      return false;
    } else if (e === "clear") {
      return undefined;
    } else {
      return this;
    }
  };
}
function xi() {
  const t = {
    get(r) {
      return Rn(this, r);
    },
    get size() {
      return In(this);
    },
    has: En,
    add: bx,
    set: yx,
    delete: wx,
    clear: kx,
    forEach: An(false, false)
  };
  const n = {
    get(r) {
      return Rn(this, r, false, true);
    },
    get size() {
      return In(this);
    },
    has: En,
    add: bx,
    set: yx,
    delete: wx,
    clear: kx,
    forEach: An(false, true)
  };
  const x = {
    get(r) {
      return Rn(this, r, true);
    },
    get size() {
      return In(this, true);
    },
    has(r) {
      return En.call(this, r, true);
    },
    add: pt("add"),
    set: pt("set"),
    delete: pt("delete"),
    clear: pt("clear"),
    forEach: An(true, false)
  };
  const o = {
    get(r) {
      return Rn(this, r, true, true);
    },
    get size() {
      return In(this, true);
    },
    has(r) {
      return En.call(this, r, true);
    },
    add: pt("add"),
    set: pt("set"),
    delete: pt("delete"),
    clear: pt("clear"),
    forEach: An(true, true)
  };
  ["keys", "values", "entries", Symbol.iterator].forEach(r => {
    t[r] = Tn(r, false, false);
    x[r] = Tn(r, true, false);
    n[r] = Tn(r, false, true);
    o[r] = Tn(r, true, true);
  });
  return [t, x, n, o];
}
const [oi, ri, ii, ci] = xi();
function $0(e, t) {
  const n = t ? e ? ci : ii : e ? ri : oi;
  return (x, o, i) => {
    if (o === "__v_isReactive") {
      return !e;
    } else if (o === "__v_isReadonly") {
      return e;
    } else if (o === "__v_raw") {
      return x;
    } else {
      return Reflect.get(xe(n, o) && o in x ? n : x, o, i);
    }
  };
}
const si = {
  get: $0(false, false)
};
const ai = {
  get: $0(false, true)
};
const li = {
  get: $0(true, false)
};
const mo = new WeakMap();
const go = new WeakMap();
const vo = new WeakMap();
const ui = new WeakMap();
function fi(e) {
  switch (e) {
    case "Object":
    case "Array":
      return 1;
    case "Map":
    case "Set":
    case "WeakMap":
    case "WeakSet":
      return 2;
    default:
      return 0;
  }
}
function di(e) {
  if (e.__v_skip || !Object.isExtensible(e)) {
    return 0;
  } else {
    return fi(Fr(e));
  }
}
function dn(e) {
  if (Kt(e)) {
    return e;
  } else {
    return W0(e, false, ei, si, mo);
  }
}
function pi(e) {
  return W0(e, false, ni, ai, go);
}
function bo(e) {
  return W0(e, true, ti, li, vo);
}
function W0(e, t, n, x, o) {
  if (!de(e) || e.__v_raw && !(t && e.__v_isReactive)) {
    return e;
  }
  const r = o.get(e);
  if (r) {
    return r;
  }
  const a = di(e);
  if (a === 0) {
    return e;
  }
  const l = new Proxy(e, a === 2 ? x : n);
  o.set(e, l);
  return l;
}
function Ht(e) {
  if (Kt(e)) {
    return Ht(e.__v_raw);
  } else {
    return !!(e && e.__v_isReactive);
  }
}
function Kt(e) {
  return !!(e && e.__v_isReadonly);
}
function Hn(e) {
  return !!(e && e.__v_isShallow);
}
function yo(e) {
  return Ht(e) || Kt(e);
}
function re(e) {
  const n = e && e.__v_raw;
  if (n) {
    return re(n);
  } else {
    return e;
  }
}
function wo(e) {
  Dn(e, "__v_skip", true);
  return e;
}
const pn = e => de(e) ? dn(e) : e;
const K0 = e => de(e) ? bo(e) : e;
function ko(e) {
  if (gt && Je) {
    e = re(e);
    uo(e.dep || (e.dep = D0()));
  }
}
function Co(e, t) {
  e = re(e);
  const x = e.dep;
  if (x) {
    k0(x);
  }
}
function Ae(e) {
  return !!(e && e.__v_isRef === true);
}
function Y(e) {
  return hi(e, false);
}
function hi(e, t) {
  if (Ae(e)) {
    return e;
  } else {
    return new mi(e, t);
  }
}
class mi {
  constructor(t, n) {
    this.__v_isShallow = n;
    this.dep = undefined;
    this.__v_isRef = true;
    this._rawValue = n ? t : re(t);
    this._value = n ? t : pn(t);
  }
  get value() {
    ko(this);
    return this._value;
  }
  set value(t) {
    const x = this.__v_isShallow || Hn(t) || Kt(t);
    t = x ? t : re(t);
    if (Tt(t, this._rawValue)) {
      this._rawValue = t;
      this._value = x ? t : pn(t);
      Co(this);
    }
  }
}
function gi(e) {
  if (Ae(e)) {
    return e.value;
  } else {
    return e;
  }
}
const vi = {
  get: (e, t, n) => gi(Reflect.get(e, t, n)),
  set: (e, t, n, x) => {
    const i = e[t];
    if (Ae(i) && !Ae(n)) {
      i.value = n;
      return true;
    } else {
      return Reflect.set(e, t, n, x);
    }
  }
};
function So(e) {
  if (Ht(e)) {
    return e;
  } else {
    return new Proxy(e, vi);
  }
}
class bi {
  constructor(t, n, x, o) {
    this._setter = n;
    this.dep = undefined;
    this.__v_isRef = true;
    this.__v_isReadonly = false;
    this._dirty = true;
    this.effect = new H0(t, () => {
      if (!this._dirty) {
        this._dirty = true;
        Co(this);
      }
    });
    this.effect.computed = this;
    this.effect.active = this._cacheable = !o;
    this.__v_isReadonly = x;
  }
  get value() {
    const n = re(this);
    ko(n);
    if (n._dirty || !n._cacheable) {
      n._dirty = false;
      n._value = n.effect.run();
    }
    return n._value;
  }
  set value(t) {
    this._setter(t);
  }
}
function yi(e, t, n = false) {
  let o;
  let i;
  const r = X(e);
  if (r) {
    o = e;
    i = Ze;
  } else {
    o = e.get;
    i = e.set;
  }
  return new bi(o, i, r || !i, n);
}
function wi(e, ...t) {}
function vt(e, t, n, x) {
  let o;
  try {
    o = x ? e(...x) : e();
  } catch (i) {
    Qn(i, t, n);
  }
  return o;
}
function Qe(e, t, n, x) {
  if (X(e)) {
    const r = vt(e, t, n, x);
    if (r && no(r)) {
      r.catch(a => {
        Qn(a, t, n);
      });
    }
    return r;
  }
  const i = [];
  for (let r = 0; r < e.length; r++) {
    i.push(Qe(e[r], t, n, x));
  }
  return i;
}
function Qn(e, t, n, x = true) {
  const i = t ? t.vnode : null;
  if (t) {
    let r = t.parent;
    const a = t.proxy;
    const l = n;
    for (; r;) {
      const m = r.ec;
      if (m) {
        for (let y = 0; y < m.length; y++) {
          if (m[y](e, a, l) === false) {
            return;
          }
        }
      }
      r = r.parent;
    }
    const f = t.appContext.config.errorHandler;
    if (f) {
      vt(f, null, 10, [e, a, l]);
      return;
    }
  }
  ki(e, n, i, x);
}
function ki(e, t, n, x = true) {
  console.error(e);
}
let hn = false;
let C0 = false;
const Ie = [];
let ot = 0;
const _t = [];
let st = null;
let St = 0;
const Ro = Promise.resolve();
let J0 = null;
function Ci(e) {
  const n = J0 || Ro;
  if (e) {
    return n.then(this ? e.bind(this) : e);
  } else {
    return n;
  }
}
function Si(e) {
  let n = ot + 1;
  let x = Ie.length;
  for (; n < x;) {
    const o = n + x >>> 1;
    const i = Ie[o];
    const r = mn(i);
    if (r < e || r === e && i.pre) {
      n = o + 1;
    } else {
      x = o;
    }
  }
  return n;
}
function G0(e) {
  if (!Ie.length || !Ie.includes(e, hn && e.allowRecurse ? ot + 1 : ot)) {
    if (e.id == null) {
      Ie.push(e);
    } else {
      Ie.splice(Si(e.id), 0, e);
    }
    Eo();
  }
}
function Eo() {
  if (!hn && !C0) {
    C0 = true;
    J0 = Ro.then(Ao);
  }
}
function Ri(e) {
  const n = Ie.indexOf(e);
  if (n > ot) {
    Ie.splice(n, 1);
  }
}
function Ei(e) {
  if (G(e)) {
    _t.push(...e);
  } else if (!st || !st.includes(e, e.allowRecurse ? St + 1 : St)) {
    _t.push(e);
  }
  Eo();
}
function Cx(e, t, n = hn ? ot + 1 : 0) {
  for (; n < Ie.length; n++) {
    const o = Ie[n];
    if (o && o.pre) {
      if (e && o.id !== e.uid) {
        continue;
      }
      Ie.splice(n, 1);
      n--;
      o();
    }
  }
}
function Io(e) {
  if (_t.length) {
    const n = [...new Set(_t)];
    _t.length = 0;
    if (st) {
      st.push(...n);
      return;
    }
    st = n;
    st.sort((x, o) => mn(x) - mn(o));
    St = 0;
    for (; St < st.length; St++) {
      st[St]();
    }
    st = null;
    St = 0;
  }
}
const mn = e => e.id == null ? Infinity : e.id;
const Ii = (e, t) => {
  const x = mn(e) - mn(t);
  if (x === 0) {
    if (e.pre && !t.pre) {
      return -1;
    }
    if (t.pre && !e.pre) {
      return 1;
    }
  }
  return x;
};
function Ao(e) {
  C0 = false;
  hn = true;
  Ie.sort(Ii);
  const n = Ze;
  try {
    for (ot = 0; ot < Ie.length; ot++) {
      const x = Ie[ot];
      if (x && x.active !== false) {
        if ("production" !== "production") {
          n(x);
        }
        vt(x, null, 14);
      }
    }
  } finally {
    ot = 0;
    Ie.length = 0;
    Io();
    hn = false;
    J0 = null;
    if (Ie.length || _t.length) {
      Ao();
    }
  }
}
function Ai(e, t, ...n) {
  if (e.isUnmounted) {
    return;
  }
  const o = e.vnode.props || fe;
  let i = n;
  const r = t.startsWith("update:");
  const a = r && t.slice(7);
  if (a && a in o) {
    const y = (a === "modelValue" ? "model" : a) + "Modifiers";
    const {
      number: g,
      trim: A
    } = o[y] || fe;
    if (A) {
      i = n.map(U => ye(U) ? U.trim() : U);
    }
    if (g) {
      i = n.map(Vr);
    }
  }
  let l;
  let f = o[l = a0(t)] || o[l = a0(Wt(t))];
  if (!f && r) {
    f = o[l = a0(Zt(t))];
  }
  if (f) {
    Qe(f, e, 6, i);
  }
  const m = o[l + "Once"];
  if (m) {
    if (!e.emitted) {
      e.emitted = {};
    } else if (e.emitted[l]) {
      return;
    }
    e.emitted[l] = true;
    Qe(m, e, 6, i);
  }
}
function To(e, t, n = false) {
  const o = t.emitsCache;
  const i = o.get(e);
  if (i !== undefined) {
    return i;
  }
  const r = e.emits;
  let a = {};
  let l = false;
  if (!X(e)) {
    const f = m => {
      const y = To(m, t, true);
      if (y) {
        l = true;
        Re(a, y);
      }
    };
    if (!n && t.mixins.length) {
      t.mixins.forEach(f);
    }
    if (e.extends) {
      f(e.extends);
    }
    if (e.mixins) {
      e.mixins.forEach(f);
    }
  }
  if (!r && !l) {
    if (de(e)) {
      o.set(e, null);
    }
    return null;
  } else {
    if (G(r)) {
      r.forEach(f => a[f] = null);
    } else {
      Re(a, r);
    }
    if (de(e)) {
      o.set(e, a);
    }
    return a;
  }
}
function Xn(e, t) {
  if (!e || !Jn(t)) {
    return false;
  } else {
    t = t.slice(2).replace(/Once$/, "");
    return xe(e, t[0].toLowerCase() + t.slice(1)) || xe(e, Zt(t)) || xe(e, t);
  }
}
let Ge = null;
let Oo = null;
function _n(e) {
  const t = Ge;
  Ge = e;
  Oo = e && e.type.__scopeId || null;
  return t;
}
function Ti(e, t = Ge, n) {
  if (!t || e._n) {
    return e;
  }
  const x = (...o) => {
    if (x._d) {
      Ux(-1);
    }
    const i = _n(t);
    let r;
    try {
      r = e(...o);
    } finally {
      _n(i);
      if (x._d) {
        Ux(1);
      }
    }
    return r;
  };
  x._n = true;
  x._c = true;
  x._d = true;
  return x;
}
function f0(e) {
  const {
    type: n,
    vnode: x,
    proxy: o,
    withProxy: i,
    props: r,
    propsOptions: [a],
    slots: l,
    attrs: f,
    emit: m,
    render: y,
    renderCache: g,
    data: A,
    setupState: U,
    ctx: H,
    inheritAttrs: j
  } = e;
  let V;
  let Z;
  const oe = _n(e);
  try {
    if (x.shapeFlag & 4) {
      const $ = i || o;
      const he = "production" !== "production" && U.__isScriptSetup ? new Proxy($, {
        get(Le, be, Te) {
          wi("Property '" + String(be) + "' was accessed via 'this'. Avoid using 'this' in templates.");
          return Reflect.get(Le, be, Te);
        }
      }) : $;
      V = xt(y.call(he, $, g, r, U, A, H));
      Z = f;
    } else {
      const $ = n;
      "production";
      V = xt($.length > 1 ? $(r, {
        attrs: f,
        slots: l,
        emit: m
      }) : $(r, null));
      Z = n.props ? f : Oi(f);
    }
  } catch ($) {
    fn.length = 0;
    Qn($, e, 1);
    V = rt(Ot);
  }
  let le = V;
  if (Z && j !== false) {
    const $ = Object.keys(Z);
    const {
      shapeFlag: he
    } = le;
    if ($.length && he & 7) {
      if (a && $.some(B0)) {
        Z = Pi(Z, a);
      }
      le = Jt(le, Z);
    }
  }
  if (x.dirs) {
    le = Jt(le);
    le.dirs = le.dirs ? le.dirs.concat(x.dirs) : x.dirs;
  }
  if (x.transition) {
    le.transition = x.transition;
  }
  V = le;
  _n(oe);
  return V;
}
const Oi = e => {
  let n;
  for (const x in e) {
    if (x === "class" || x === "style" || Jn(x)) {
      (n || (n = {}))[x] = e[x];
    }
  }
  return n;
};
const Pi = (e, t) => {
  const x = {};
  for (const o in e) {
    if (!B0(o) || !(o.slice(9) in t)) {
      x[o] = e[o];
    }
  }
  return x;
};
function ji(e, t, n) {
  const {
    props: o,
    children: i,
    component: r
  } = e;
  const {
    props: a,
    children: l,
    patchFlag: f
  } = t;
  const m = r.emitsOptions;
  if (t.dirs || t.transition) {
    return true;
  }
  if (n && f >= 0) {
    if (f & 1024) {
      return true;
    }
    if (f & 16) {
      if (o) {
        return Sx(o, a, m);
      } else {
        return !!a;
      }
    }
    if (f & 8) {
      const y = t.dynamicProps;
      for (let g = 0; g < y.length; g++) {
        const A = y[g];
        if (a[A] !== o[A] && !Xn(m, A)) {
          return true;
        }
      }
    }
  } else if ((i || l) && (!l || !l.$stable)) {
    return true;
  } else if (o === a) {
    return false;
  } else if (o) {
    if (a) {
      return Sx(o, a, m);
    } else {
      return true;
    }
  } else {
    return !!a;
  }
  return false;
}
function Sx(e, t, n) {
  const o = Object.keys(t);
  if (o.length !== Object.keys(e).length) {
    return true;
  }
  for (let i = 0; i < o.length; i++) {
    const r = o[i];
    if (t[r] !== e[r] && !Xn(n, r)) {
      return true;
    }
  }
  return false;
}
function Ui({
              vnode: e,
              parent: t
            }, n) {
  for (; t && t.subTree === e;) {
    (e = t.vnode).el = n;
    t = t.parent;
  }
}
const Ni = Symbol.for("v-ndc");
const Fi = e => e.__isSuspense;
function Mi(e, t) {
  if (t && t.pendingBranch) {
    if (G(e)) {
      t.effects.push(...e);
    } else {
      t.effects.push(e);
    }
  } else {
    Ei(e);
  }
}
const On = {};
function d0(e, t, n) {
  return Po(e, t, n);
}
function Po(e, t, {
  immediate: n,
  deep: x,
  flush: o,
  onTrack: i,
  onTrigger: r
} = fe) {
  var l;
  const f = Kr() === ((l = Se) == null ? undefined : l.scope) ? Se : null;
  let m;
  let y = false;
  let g = false;
  if (Ae(e)) {
    m = () => e.value;
    y = Hn(e);
  } else if (Ht(e)) {
    m = () => e;
    x = true;
  } else if (G(e)) {
    g = true;
    y = e.some($ => Ht($) || Hn($));
    m = () => e.map($ => {
      if (Ae($)) {
        return $.value;
      }
      if (Ht($)) {
        return Et($);
      }
      if (X($)) {
        return vt($, f, 2);
      }
    });
  } else if (X(e)) {
    if (t) {
      m = () => vt(e, f, 2);
    } else {
      m = () => {
        if (!(f && f.isUnmounted)) {
          if (A) {
            A();
          }
          return Qe(e, f, 3, [U]);
        }
      };
    }
  } else {
    m = Ze;
  }
  if (t && x) {
    const $ = m;
    m = () => Et($());
  }
  let A;
  let U = $ => {
    A = oe.onStop = () => {
      vt($, f, 4);
      A = oe.onStop = undefined;
    };
  };
  let H;
  if (vn) {
    U = Ze;
    if (t) {
      if (n) {
        Qe(t, f, 3, [m(), g ? [] : undefined, U]);
      }
    } else {
      m();
    }
    if (o === "sync") {
      const $ = Uc();
      H = $.__watcherHandles || ($.__watcherHandles = []);
    } else {
      return Ze;
    }
  }
  let j = g ? new Array(e.length).fill(On) : On;
  const V = () => {
    if (oe.active) {
      if (t) {
        const he = oe.run();
        if (x || y || (g ? he.some((Le, be) => Tt(Le, j[be])) : Tt(he, j)) || false) {
          if (A) {
            A();
          }
          Qe(t, f, 3, [he, j === On ? undefined : g && j[0] === On ? [] : j, U]);
          j = he;
        }
      } else {
        oe.run();
      }
    }
  };
  V.allowRecurse = !!t;
  let Z;
  if (o === "sync") {
    Z = V;
  } else if (o === "post") {
    Z = () => Fe(V, f && f.suspense);
  } else {
    V.pre = true;
    if (f) {
      V.id = f.uid;
    }
    Z = () => G0(V);
  }
  const oe = new H0(m, Z);
  if (t) {
    if (n) {
      V();
    } else {
      j = oe.run();
    }
  } else if (o === "post") {
    Fe(oe.run.bind(oe), f && f.suspense);
  } else {
    oe.run();
  }
  const le = () => {
    oe.stop();
    if (f && f.scope) {
      V0(f.scope.effects, oe);
    }
  };
  if (H) {
    H.push(le);
  }
  return le;
}
function Bi(e, t, n) {
  const o = this.proxy;
  const i = ye(e) ? e.includes(".") ? jo(o, e) : () => o[e] : e.bind(o, o);
  let r;
  if (X(t)) {
    r = t;
  } else {
    r = t.handler;
    n = t;
  }
  const a = Se;
  Gt(this);
  const l = Po(i, r.bind(o), n);
  if (a) {
    Gt(a);
  } else {
    At();
  }
  return l;
}
function jo(e, t) {
  const n = t.split(".");
  return () => {
    let o = e;
    for (let i = 0; i < n.length && o; i++) {
      o = o[n[i]];
    }
    return o;
  };
}
function Et(e, t) {
  if (!de(e) || e.__v_skip || (t = t || new Set(), t.has(e))) {
    return e;
  }
  t.add(e);
  if (Ae(e)) {
    Et(e.value, t);
  } else if (G(e)) {
    for (let x = 0; x < e.length; x++) {
      Et(e[x], t);
    }
  } else if (to(e) || Dt(e)) {
    e.forEach(x => {
      Et(x, t);
    });
  } else if (oo(e)) {
    for (const x in e) {
      Et(e[x], t);
    }
  }
  return e;
}
function Pn(e, t) {
  const x = Ge;
  if (x === null) {
    return e;
  }
  const o = x0(x) || x.proxy;
  const i = e.dirs || (e.dirs = []);
  for (let r = 0; r < t.length; r++) {
    let [a, l, f, m = fe] = t[r];
    if (a) {
      if (X(a)) {
        a = {
          mounted: a,
          updated: a
        };
      }
      if (a.deep) {
        Et(l);
      }
      i.push({
        dir: a,
        instance: o,
        value: l,
        oldValue: undefined,
        arg: f,
        modifiers: m
      });
    }
  }
  return e;
}
function kt(e, t, n, x) {
  const i = e.dirs;
  const r = t && t.dirs;
  for (let a = 0; a < i.length; a++) {
    const l = i[a];
    if (r) {
      l.oldValue = r[a].value;
    }
    let f = l.dir[x];
    if (f) {
      Qt();
      Qe(f, n, 8, [e.el, l, e, t]);
      Xt();
    }
  }
}
const De = [Function, Array];
const Vi = {
  mode: String,
  appear: Boolean,
  persisted: Boolean,
  onBeforeEnter: De,
  onEnter: De,
  onAfterEnter: De,
  onEnterCancelled: De,
  onBeforeLeave: De,
  onLeave: De,
  onAfterLeave: De,
  onLeaveCancelled: De,
  onBeforeAppear: De,
  onAppear: De,
  onAfterAppear: De,
  onAppearCancelled: De
};
const Bn = e => !!e.type.__asyncLoader;
const Uo = e => e.type.__isKeepAlive;
function zi(e, t) {
  No(e, "a", t);
}
function Li(e, t) {
  No(e, "da", t);
}
function No(e, t, n = Se) {
  const o = e.__wdc || (e.__wdc = () => {
    let r = n;
    for (; r;) {
      if (r.isDeactivated) {
        return;
      }
      r = r.parent;
    }
    return e();
  });
  e0(t, o, n);
  if (n) {
    let i = n.parent;
    for (; i && i.parent;) {
      if (Uo(i.parent.vnode)) {
        qi(o, t, n, i);
      }
      i = i.parent;
    }
  }
}
function qi(e, t, n, x) {
  const o = e0(t, e, x, true);
  Mo(() => {
    V0(x[t], o);
  }, n);
}
function e0(e, t, n = Se, x = false) {
  if (n) {
    const i = n[e] || (n[e] = []);
    const r = t.__weh || (t.__weh = (...a) => {
      if (n.isUnmounted) {
        return;
      }
      Qt();
      Gt(n);
      const f = Qe(t, n, e, a);
      At();
      Xt();
      return f;
    });
    if (x) {
      i.unshift(r);
    } else {
      i.push(r);
    }
    return r;
  }
}
const lt = e => (t, n = Se) => (!vn || e === "sp") && e0(e, (...x) => t(...x), n);
const Di = lt("bm");
const Fo = lt("m");
const Hi = lt("bu");
const _i = lt("u");
const $i = lt("bum");
const Mo = lt("um");
const Wi = lt("sp");
const Ki = lt("rtg");
const Ji = lt("rtc");
function Gi(e, t = Se) {
  e0("ec", e, t);
}
const S0 = e => {
  if (e) {
    if (Ko(e)) {
      return x0(e) || e.proxy;
    } else {
      return S0(e.parent);
    }
  } else {
    return null;
  }
};
const un = Re(Object.create(null), {
  $: e => e,
  $el: e => e.vnode.el,
  $data: e => e.data,
  $props: e => e.props,
  $attrs: e => e.attrs,
  $slots: e => e.slots,
  $refs: e => e.refs,
  $parent: e => S0(e.parent),
  $root: e => S0(e.root),
  $emit: e => e.emit,
  $options: e => Y0(e),
  $forceUpdate: e => e.f || (e.f = () => G0(e.update)),
  $nextTick: e => e.n || (e.n = Ci.bind(e.proxy)),
  $watch: e => Bi.bind(e)
});
const p0 = (e, t) => e !== fe && !e.__isScriptSetup && xe(e, t);
const Yi = {
  get({
        _: e
      }, t) {
    const {
      ctx: x,
      setupState: o,
      data: i,
      props: r,
      accessCache: a,
      type: l,
      appContext: f
    } = e;
    let m;
    if (t[0] !== "$") {
      const U = a[t];
      if (U !== undefined) {
        switch (U) {
          case 1:
            return o[t];
          case 2:
            return i[t];
          case 4:
            return x[t];
          case 3:
            return r[t];
        }
      } else {
        if (p0(o, t)) {
          a[t] = 1;
          return o[t];
        }
        if (i !== fe && xe(i, t)) {
          a[t] = 2;
          return i[t];
        }
        if ((m = e.propsOptions[0]) && xe(m, t)) {
          a[t] = 3;
          return r[t];
        }
        if (x !== fe && xe(x, t)) {
          a[t] = 4;
          return x[t];
        }
        if (R0) {
          a[t] = 0;
        }
      }
    }
    const y = un[t];
    let g;
    let A;
    if (y) {
      if (t === "$attrs") {
        Me(e, "get", t);
      }
      return y(e);
    }
    if ((g = l.__cssModules) && (g = g[t])) {
      return g;
    }
    if (x !== fe && xe(x, t)) {
      a[t] = 4;
      return x[t];
    }
    A = f.config.globalProperties;
    if (xe(A, t)) {
      return A[t];
    }
  },
  set({
        _: e
      }, t, n) {
    const {
      data: o,
      setupState: i,
      ctx: r
    } = e;
    if (p0(i, t)) {
      i[t] = n;
      return true;
    } else if (o !== fe && xe(o, t)) {
      o[t] = n;
      return true;
    } else if (xe(e.props, t)) {
      return false;
    } else if (t[0] === "$" && t.slice(1) in e) {
      return false;
    } else {
      r[t] = n;
      return true;
    }
  },
  has({
        _: {
          data: e,
          setupState: t,
          accessCache: n,
          ctx: x,
          appContext: o,
          propsOptions: i
        }
      }, r) {
    let l;
    return !!n[r] || e !== fe && xe(e, r) || p0(t, r) || (l = i[0]) && xe(l, r) || xe(x, r) || xe(un, r) || xe(o.config.globalProperties, r);
  },
  defineProperty(e, t, n) {
    if (n.get != null) {
      e._.accessCache[t] = 0;
    } else if (xe(n, "value")) {
      this.set(e, t, n.value, null);
    }
    return Reflect.defineProperty(e, t, n);
  }
};
function Rx(e) {
  if (G(e)) {
    return e.reduce((t, n) => {
      t[n] = null;
      return t;
    }, {});
  } else {
    return e;
  }
}
let R0 = true;
function Zi(e) {
  const n = Y0(e);
  const x = e.proxy;
  const o = e.ctx;
  R0 = false;
  if (n.beforeCreate) {
    Ex(n.beforeCreate, e, "bc");
  }
  const {
    data: i,
    computed: r,
    methods: a,
    watch: l,
    provide: f,
    inject: m,
    created: y,
    beforeMount: g,
    mounted: A,
    beforeUpdate: U,
    updated: H,
    activated: j,
    deactivated: V,
    beforeDestroy: Z,
    beforeUnmount: oe,
    destroyed: le,
    unmounted: $,
    render: he,
    renderTracked: Le,
    renderTriggered: be,
    errorCaptured: Te,
    serverPrefetch: He,
    expose: Be,
    inheritAttrs: Xe,
    components: _e,
    directives: it,
    filters: ut
  } = n;
  if (m) {
    Qi(m, o, null);
  }
  if (a) {
    for (const ie in a) {
      const ne = a[ie];
      if (X(ne)) {
        o[ie] = ne.bind(x);
      }
    }
  }
  if (i) {
    const ie = i.call(x, x);
    if (de(ie)) {
      e.data = dn(ie);
    }
  }
  R0 = true;
  if (r) {
    for (const ie in r) {
      const ne = r[ie];
      const qe = X(ne) ? ne.bind(x, x) : X(ne.get) ? ne.get.bind(x, x) : Ze;
      const Oe = !X(ne) && X(ne.set) ? ne.set.bind(x) : Ze;
      const z = Pc({
        get: qe,
        set: Oe
      });
      Object.defineProperty(o, ie, {
        enumerable: true,
        configurable: true,
        get: () => z.value,
        set: v => z.value = v
      });
    }
  }
  if (l) {
    for (const ie in l) {
      Bo(l[ie], o, x, ie);
    }
  }
  if (f) {
    const ie = X(f) ? f.call(x) : f;
    Reflect.ownKeys(ie).forEach(ne => {
      oc(ne, ie[ne]);
    });
  }
  if (y) {
    Ex(y, e, "c");
  }
  function me(ie, ne) {
    if (G(ne)) {
      ne.forEach(Oe => ie(Oe.bind(x)));
    } else if (ne) {
      ie(ne.bind(x));
    }
  }
  me(Di, g);
  me(Fo, A);
  me(Hi, U);
  me(_i, H);
  me(zi, j);
  me(Li, V);
  me(Gi, Te);
  me(Ji, Le);
  me(Ki, be);
  me($i, oe);
  me(Mo, $);
  me(Wi, He);
  if (G(Be)) {
    if (Be.length) {
      const ie = e.exposed || (e.exposed = {});
      Be.forEach(ne => {
        Object.defineProperty(ie, ne, {
          get: () => x[ne],
          set: Oe => x[ne] = Oe
        });
      });
    } else if (!e.exposed) {
      e.exposed = {};
    }
  }
  if (he && e.render === Ze) {
    e.render = he;
  }
  if (Xe != null) {
    e.inheritAttrs = Xe;
  }
  if (_e) {
    e.components = _e;
  }
  if (it) {
    e.directives = it;
  }
}
function Qi(e, t, n = Ze) {
  if (G(e)) {
    e = E0(e);
  }
  for (const o in e) {
    const i = e[o];
    let r;
    if (de(i)) {
      if ("default" in i) {
        r = Vn(i.from || o, i.default, true);
      } else {
        r = Vn(i.from || o);
      }
    } else {
      r = Vn(i);
    }
    if (Ae(r)) {
      Object.defineProperty(t, o, {
        enumerable: true,
        configurable: true,
        get: () => r.value,
        set: a => r.value = a
      });
    } else {
      t[o] = r;
    }
  }
}
function Ex(e, t, n) {
  Qe(G(e) ? e.map(o => o.bind(t.proxy)) : e.bind(t.proxy), t, n);
}
function Bo(e, t, n, x) {
  const i = x.includes(".") ? jo(n, x) : () => n[x];
  if (ye(e)) {
    const r = t[e];
    if (X(r)) {
      d0(i, r);
    }
  } else if (X(e)) {
    d0(i, e.bind(n));
  } else if (de(e)) {
    if (G(e)) {
      e.forEach(r => Bo(r, t, n, x));
    } else {
      const r = X(e.handler) ? e.handler.bind(n) : t[e.handler];
      if (X(r)) {
        d0(i, r, e);
      }
    }
  }
}
function Y0(e) {
  const n = e.type;
  const {
    mixins: x,
    extends: o
  } = n;
  const {
    mixins: i,
    optionsCache: r,
    config: {
      optionMergeStrategies: a
    }
  } = e.appContext;
  const l = r.get(n);
  let f;
  if (l) {
    f = l;
  } else if (!i.length && !x && !o) {
    f = n;
  } else {
    f = {};
    if (i.length) {
      i.forEach(m => $n(f, m, a, true));
    }
    $n(f, n, a);
  }
  if (de(n)) {
    r.set(n, f);
  }
  return f;
}
function $n(e, t, n, x = false) {
  const {
    mixins: i,
    extends: r
  } = t;
  if (r) {
    $n(e, r, n, true);
  }
  if (i) {
    i.forEach(a => $n(e, a, n, true));
  }
  for (const a in t) {
    if (!(x && a === "expose")) {
      const l = Xi[a] || n && n[a];
      e[a] = l ? l(e[a], t[a]) : t[a];
    }
  }
  return e;
}
const Xi = {
  data: Ix,
  props: Ax,
  emits: Ax,
  methods: an,
  computed: an,
  beforeCreate: je,
  created: je,
  beforeMount: je,
  mounted: je,
  beforeUpdate: je,
  updated: je,
  beforeDestroy: je,
  beforeUnmount: je,
  destroyed: je,
  unmounted: je,
  activated: je,
  deactivated: je,
  errorCaptured: je,
  serverPrefetch: je,
  components: an,
  directives: an,
  watch: tc,
  provide: Ix,
  inject: ec
};
function Ix(e, t) {
  if (t) {
    if (e) {
      return function () {
        return Re(X(e) ? e.call(this, this) : e, X(t) ? t.call(this, this) : t);
      };
    } else {
      return t;
    }
  } else {
    return e;
  }
}
function ec(e, t) {
  return an(E0(e), E0(t));
}
function E0(e) {
  if (G(e)) {
    const n = {};
    for (let x = 0; x < e.length; x++) {
      n[e[x]] = e[x];
    }
    return n;
  }
  return e;
}
function je(e, t) {
  if (e) {
    return [...new Set([].concat(e, t))];
  } else {
    return t;
  }
}
function an(e, t) {
  if (e) {
    return Re(Object.create(null), e, t);
  } else {
    return t;
  }
}
function Ax(e, t) {
  if (e) {
    if (G(e) && G(t)) {
      return [...new Set([...e, ...t])];
    } else {
      return Re(Object.create(null), Rx(e), Rx(t != null ? t : {}));
    }
  } else {
    return t;
  }
}
function tc(e, t) {
  if (!e) {
    return t;
  }
  if (!t) {
    return e;
  }
  const x = Re(Object.create(null), e);
  for (const o in t) {
    x[o] = je(e[o], t[o]);
  }
  return x;
}
function Vo() {
  return {
    app: null,
    config: {
      isNativeTag: Ur,
      performance: false,
      globalProperties: {},
      optionMergeStrategies: {},
      errorHandler: undefined,
      warnHandler: undefined,
      compilerOptions: {}
    },
    mixins: [],
    components: {},
    directives: {},
    provides: Object.create(null),
    optionsCache: new WeakMap(),
    propsCache: new WeakMap(),
    emitsCache: new WeakMap()
  };
}
let nc = 0;
function xc(e, t) {
  return function (x, o = null) {
    if (!X(x)) {
      x = Re({}, x);
    }
    if (o != null && !de(o)) {
      o = null;
    }
    const r = Vo();
    const a = new WeakSet();
    let l = false;
    const f = r.app = {
      _uid: nc++,
      _component: x,
      _props: o,
      _container: null,
      _context: r,
      _instance: null,
      version: Nc,
      get config() {
        return r.config;
      },
      set config(m) {},
      use(m, ...y) {
        if (!a.has(m)) {
          if (m && X(m.install)) {
            a.add(m);
            m.install(f, ...y);
          } else if (X(m)) {
            a.add(m);
            m(f, ...y);
          }
        }
        return f;
      },
      mixin(m) {
        if (!r.mixins.includes(m)) {
          r.mixins.push(m);
        }
        return f;
      },
      component(m, y) {
        if (y) {
          r.components[m] = y;
          return f;
        } else {
          return r.components[m];
        }
      },
      directive(m, y) {
        if (y) {
          r.directives[m] = y;
          return f;
        } else {
          return r.directives[m];
        }
      },
      mount(m, y, g) {
        if (!l) {
          const U = rt(x, o);
          U.appContext = r;
          if (y && t) {
            t(U, m);
          } else {
            e(U, m, g);
          }
          l = true;
          f._container = m;
          m.__vue_app__ = f;
          return x0(U.component) || U.component.proxy;
        }
      },
      unmount() {
        if (l) {
          e(null, f._container);
          delete f._container.__vue_app__;
        }
      },
      provide(m, y) {
        r.provides[m] = y;
        return f;
      },
      runWithContext(m) {
        Wn = f;
        try {
          return m();
        } finally {
          Wn = null;
        }
      }
    };
    return f;
  };
}
let Wn = null;
function oc(e, t) {
  if (Se) {
    let x = Se.provides;
    const o = Se.parent && Se.parent.provides;
    if (o === x) {
      x = Se.provides = Object.create(o);
    }
    x[e] = t;
  }
}
function Vn(e, t, n = false) {
  const o = Se || Ge;
  if (o || Wn) {
    const i = o ? o.parent == null ? o.vnode.appContext && o.vnode.appContext.provides : o.parent.provides : Wn._context.provides;
    if (i && e in i) {
      return i[e];
    }
    if (arguments.length > 1) {
      if (n && X(t)) {
        return t.call(o && o.proxy);
      } else {
        return t;
      }
    }
  }
}
function rc(e, t, n, x = false) {
  const i = {};
  const r = {};
  Dn(r, n0, 1);
  e.propsDefaults = Object.create(null);
  zo(e, t, i, r);
  for (const a in e.propsOptions[0]) {
    if (!(a in i)) {
      i[a] = undefined;
    }
  }
  if (n) {
    e.props = x ? i : pi(i);
  } else if (e.type.props) {
    e.props = i;
  } else {
    e.props = r;
  }
  e.attrs = r;
}
function ic(e, t, n, x) {
  const {
    props: i,
    attrs: r,
    vnode: {
      patchFlag: a
    }
  } = e;
  const l = re(i);
  const [f] = e.propsOptions;
  let m = false;
  if ((x || a > 0) && !(a & 16)) {
    if (a & 8) {
      const y = e.vnode.dynamicProps;
      for (let g = 0; g < y.length; g++) {
        let A = y[g];
        if (Xn(e.emitsOptions, A)) {
          continue;
        }
        const U = t[A];
        if (f) {
          if (xe(r, A)) {
            if (U !== r[A]) {
              r[A] = U;
              m = true;
            }
          } else {
            const H = Wt(A);
            i[H] = I0(f, l, H, U, e, false);
          }
        } else if (U !== r[A]) {
          r[A] = U;
          m = true;
        }
      }
    }
  } else {
    if (zo(e, t, i, r)) {
      m = true;
    }
    let y;
    for (const g in l) {
      if (!t || !xe(t, g) && ((y = Zt(g)) === g || !xe(t, y))) {
        if (f) {
          if (n && (n[g] !== undefined || n[y] !== undefined)) {
            i[g] = I0(f, l, g, undefined, e, true);
          }
        } else {
          delete i[g];
        }
      }
    }
    if (r !== l) {
      for (const g in r) {
        if (!t || !xe(t, g) && true) {
          delete r[g];
          m = true;
        }
      }
    }
  }
  if (m) {
    at(e, "set", "$attrs");
  }
}
function zo(e, t, n, x) {
  const [i, r] = e.propsOptions;
  let a = false;
  let l;
  if (t) {
    for (let f in t) {
      if (Mn(f)) {
        continue;
      }
      const m = t[f];
      let y;
      if (i && xe(i, y = Wt(f))) {
        if (!r || !r.includes(y)) {
          n[y] = m;
        } else {
          (l || (l = {}))[y] = m;
        }
      } else if (!Xn(e.emitsOptions, f) && (!(f in x) || m !== x[f])) {
        x[f] = m;
        a = true;
      }
    }
  }
  if (r) {
    const f = re(n);
    const m = l || fe;
    for (let y = 0; y < r.length; y++) {
      const g = r[y];
      n[g] = I0(i, f, g, m[g], e, !xe(m, g));
    }
  }
  return a;
}
function I0(e, t, n, x, o, i) {
  const a = e[n];
  if (a != null) {
    const l = xe(a, "default");
    if (l && x === undefined) {
      const f = a.default;
      if (a.type !== Function && !a.skipFactory && X(f)) {
        const {
          propsDefaults: m
        } = o;
        if (n in m) {
          x = m[n];
        } else {
          Gt(o);
          x = m[n] = f.call(null, t);
          At();
        }
      } else {
        x = f;
      }
    }
    if (a[0]) {
      if (i && !l) {
        x = false;
      } else if (a[1] && (x === "" || x === Zt(n))) {
        x = true;
      }
    }
  }
  return x;
}
function Lo(e, t, n = false) {
  const o = t.propsCache;
  const i = o.get(e);
  if (i) {
    return i;
  }
  const r = e.props;
  const a = {};
  const l = [];
  let f = false;
  if (!X(e)) {
    const y = g => {
      f = true;
      const [U, H] = Lo(g, t, true);
      Re(a, U);
      if (H) {
        l.push(...H);
      }
    };
    if (!n && t.mixins.length) {
      t.mixins.forEach(y);
    }
    if (e.extends) {
      y(e.extends);
    }
    if (e.mixins) {
      e.mixins.forEach(y);
    }
  }
  if (!r && !f) {
    if (de(e)) {
      o.set(e, qt);
    }
    return qt;
  }
  if (G(r)) {
    for (let y = 0; y < r.length; y++) {
      const g = Wt(r[y]);
      if (Tx(g)) {
        a[g] = fe;
      }
    }
  } else if (r) {
    for (const y in r) {
      const g = Wt(y);
      if (Tx(g)) {
        const A = r[y];
        const U = a[g] = G(A) || X(A) ? {
          type: A
        } : Re({}, A);
        if (U) {
          const H = jx(Boolean, U.type);
          const j = jx(String, U.type);
          U[0] = H > -1;
          U[1] = j < 0 || H < j;
          if (H > -1 || xe(U, "default")) {
            l.push(g);
          }
        }
      }
    }
  }
  const m = [a, l];
  if (de(e)) {
    o.set(e, m);
  }
  return m;
}
function Tx(e) {
  if (e[0] !== "$") {
    return true;
  } else {
    return false;
  }
}
function Ox(e) {
  const n = e && e.toString().match(/^\s*(function|class) (\w+)/);
  if (n) {
    return n[2];
  } else if (e === null) {
    return "null";
  } else {
    return "";
  }
}
function Px(e, t) {
  return Ox(e) === Ox(t);
}
function jx(e, t) {
  if (G(t)) {
    return t.findIndex(x => Px(x, e));
  } else if (X(t) && Px(t, e)) {
    return 0;
  } else {
    return -1;
  }
}
const qo = e => e[0] === "_" || e === "$stable";
const Z0 = e => G(e) ? e.map(xt) : [xt(e)];
const cc = (e, t, n) => {
  if (t._n) {
    return t;
  }
  const x = Ti((...o) => {
    "production";
    "production";

    return Z0(t(...o));
  }, n);
  x._c = false;
  return x;
};
const Do = (e, t, n) => {
  const o = e._ctx;
  for (const i in e) {
    if (qo(i)) {
      continue;
    }
    const r = e[i];
    if (X(r)) {
      t[i] = cc(i, r, o);
    } else if (r != null) {
      const a = Z0(r);
      t[i] = () => a;
    }
  }
};
const Ho = (e, t) => {
  const x = Z0(t);
  e.slots.default = () => x;
};
const sc = (e, t) => {
  if (e.vnode.shapeFlag & 32) {
    const x = t._;
    if (x) {
      e.slots = re(t);
      Dn(t, "_", x);
    } else {
      Do(t, e.slots = {});
    }
  } else {
    e.slots = {};
    if (t) {
      Ho(e, t);
    }
  }
  Dn(e.slots, n0, 1);
};
const ac = (e, t, n) => {
  const {
    vnode: o,
    slots: i
  } = e;
  let r = true;
  let a = fe;
  if (o.shapeFlag & 32) {
    const l = t._;
    if (l) {
      if (n && l === 1) {
        r = false;
      } else {
        Re(i, t);
        if (!n && l === 1) {
          delete i._;
        }
      }
    } else {
      r = !t.$stable;
      Do(t, i);
    }
    a = t;
  } else if (t) {
    Ho(e, t);
    a = {
      default: 1
    };
  }
  if (r) {
    for (const l in i) {
      if (!qo(l) && a[l] == null) {
        delete i[l];
      }
    }
  }
};
function A0(e, t, n, x, o = false) {
  if (G(e)) {
    e.forEach((A, U) => A0(A, t && (G(t) ? t[U] : t), n, x, o));
    return;
  }
  if (Bn(x) && !o) {
    return;
  }
  const r = x.shapeFlag & 4 ? x0(x.component) || x.component.proxy : x.el;
  const a = o ? null : r;
  const {
    i: l,
    r: f
  } = e;
  const m = t && t.r;
  const y = l.refs === fe ? l.refs = {} : l.refs;
  const g = l.setupState;
  if (m != null && m !== f) {
    if (ye(m)) {
      y[m] = null;
      if (xe(g, m)) {
        g[m] = null;
      }
    } else if (Ae(m)) {
      m.value = null;
    }
  }
  if (X(f)) {
    vt(f, l, 12, [a, y]);
  } else {
    const A = ye(f);
    const U = Ae(f);
    if (A || U) {
      const H = () => {
        if (e.f) {
          const V = A ? xe(g, f) ? g[f] : y[f] : f.value;
          if (o) {
            if (G(V)) {
              V0(V, r);
            }
          } else if (G(V)) {
            if (!V.includes(r)) {
              V.push(r);
            }
          } else if (A) {
            y[f] = [r];
            if (xe(g, f)) {
              g[f] = y[f];
            }
          } else {
            f.value = [r];
            if (e.k) {
              y[e.k] = f.value;
            }
          }
        } else if (A) {
          y[f] = a;
          if (xe(g, f)) {
            g[f] = a;
          }
        } else if (U) {
          f.value = a;
          if (e.k) {
            y[e.k] = a;
          }
        }
      };
      if (a) {
        H.id = -1;
        Fe(H, n);
      } else {
        H();
      }
    }
  }
}
const Fe = Mi;
function lc(e) {
  return uc(e);
}
function uc(e, t) {
  const x = v0();
  x.__VUE__ = true;
  const {
    insert: o,
    remove: i,
    patchProp: r,
    createElement: a,
    createText: l,
    createComment: f,
    setText: m,
    setElementText: y,
    parentNode: g,
    nextSibling: A,
    setScopeId: U = Ze,
    insertStaticContent: H
  } = e;
  const j = (p, h, b, w = null, k = null, C = null, I = false, S = null, R = !!h.dynamicChildren) => {
    if (p === h) {
      return;
    }
    if (p && !rn(p, h)) {
      w = q(p);
      v(p, k, C, true);
      p = null;
    }
    if (h.patchFlag === -2) {
      R = false;
      h.dynamicChildren = null;
    }
    const {
      type: E,
      ref: D,
      shapeFlag: O
    } = h;
    switch (E) {
      case t0:
        V(p, h, b, w);
        break;
      case Ot:
        Z(p, h, b, w);
        break;
      case zn:
        if (p == null) {
          oe(h, b, w, I);
        }
        break;
      case nt:
        _e(p, h, b, w, k, C, I, S, R);
        break;
      default:
        if (O & 1) {
          he(p, h, b, w, k, C, I, S, R);
        } else if (O & 6) {
          it(p, h, b, w, k, C, I, S, R);
        } else if (O & 64 || O & 128) {
          E.process(p, h, b, w, k, C, I, S, R, ve);
        }
    }
    if (D != null && k) {
      A0(D, p && p.ref, C, h || p, !h);
    }
  };
  const V = (p, h, b, w) => {
    if (p == null) {
      o(h.el = l(h.children), b, w);
    } else {
      const C = h.el = p.el;
      if (h.children !== p.children) {
        m(C, h.children);
      }
    }
  };
  const Z = (p, h, b, w) => {
    if (p == null) {
      o(h.el = f(h.children || ""), b, w);
    } else {
      h.el = p.el;
    }
  };
  const oe = (p, h, b, w) => {
    [p.el, p.anchor] = H(p.children, h, b, w, p.el, p.anchor);
  };
  const le = ({
                el: p,
                anchor: h
              }, b, w) => {
    let k;
    for (; p && p !== h;) {
      k = A(p);
      o(p, b, w);
      p = k;
    }
    o(h, b, w);
  };
  const $ = ({
               el: p,
               anchor: h
             }) => {
    let b;
    for (; p && p !== h;) {
      b = A(p);
      i(p);
      p = b;
    }
    i(h);
  };
  const he = (p, h, b, w, k, C, I, S, R) => {
    I = I || h.type === "svg";
    if (p == null) {
      Le(h, b, w, k, C, I, S, R);
    } else {
      He(p, h, k, C, I, S, R);
    }
  };
  const Le = (p, h, b, w, k, C, I, S) => {
    let T;
    let E;
    const {
      type: D,
      props: O,
      shapeFlag: _,
      transition: W,
      dirs: ee
    } = p;
    T = p.el = a(p.type, C, O && O.is, O);
    if (_ & 8) {
      y(T, p.children);
    } else if (_ & 16) {
      Te(p.children, T, null, w, k, C && D !== "foreignObject", I, S);
    }
    if (ee) {
      kt(p, null, w, "created");
    }
    be(T, p, p.scopeId, I, w);
    if (O) {
      for (const te in O) {
        if (te !== "value" && !Mn(te)) {
          r(T, te, null, O[te], C, p.children, w, k, M);
        }
      }
      if ("value" in O) {
        r(T, "value", null, O.value);
      }
      if (E = O.onVnodeBeforeMount) {
        et(E, w, p);
      }
    }
    if (ee) {
      kt(p, null, w, "beforeMount");
    }
    const se = fc(k, W);
    if (se) {
      W.beforeEnter(T);
    }
    o(T, h, b);
    if ((E = O && O.onVnodeMounted) || se || ee) {
      Fe(() => {
        if (E) {
          et(E, w, p);
        }
        if (se) {
          W.enter(T);
        }
        if (ee) {
          kt(p, null, w, "mounted");
        }
      }, k);
    }
  };
  const be = (p, h, b, w, k) => {
    if (b) {
      U(p, b);
    }
    if (w) {
      for (let I = 0; I < w.length; I++) {
        U(p, w[I]);
      }
    }
    if (k) {
      let I = k.subTree;
      if (h === I) {
        const S = k.vnode;
        be(p, S, S.scopeId, S.slotScopeIds, k.parent);
      }
    }
  };
  const Te = (p, h, b, w, k, C, I, S, R = 0) => {
    for (let E = R; E < p.length; E++) {
      const D = p[E] = S ? ht(p[E]) : xt(p[E]);
      j(null, D, h, b, w, k, C, I, S);
    }
  };
  const He = (p, h, b, w, k, C, I) => {
    const R = h.el = p.el;
    let {
      patchFlag: T,
      dynamicChildren: E,
      dirs: D
    } = h;
    T |= p.patchFlag & 16;
    const O = p.props || fe;
    const _ = h.props || fe;
    let W;
    if (b) {
      Ct(b, false);
    }
    if (W = _.onVnodeBeforeUpdate) {
      et(W, b, h, p);
    }
    if (D) {
      kt(h, p, b, "beforeUpdate");
    }
    if (b) {
      Ct(b, true);
    }
    const ee = k && h.type !== "foreignObject";
    if (E) {
      Be(p.dynamicChildren, E, R, b, w, ee, C);
    } else if (!I) {
      ne(p, h, R, null, b, w, ee, C, false);
    }
    if (T > 0) {
      if (T & 16) {
        Xe(R, h, O, _, b, w, k);
      } else {
        if (T & 2 && O.class !== _.class) {
          r(R, "class", null, _.class, k);
        }
        if (T & 4) {
          r(R, "style", O.style, _.style, k);
        }
        if (T & 8) {
          const se = h.dynamicProps;
          for (let te = 0; te < se.length; te++) {
            const ge = se[te];
            const K = O[ge];
            const we = _[ge];
            if (we !== K || ge === "value") {
              r(R, ge, K, we, k, p.children, b, w, M);
            }
          }
        }
      }
      if (T & 1 && p.children !== h.children) {
        y(R, h.children);
      }
    } else if (!I && E == null) {
      Xe(R, h, O, _, b, w, k);
    }
    if ((W = _.onVnodeUpdated) || D) {
      Fe(() => {
        if (W) {
          et(W, b, h, p);
        }
        if (D) {
          kt(h, p, b, "updated");
        }
      }, w);
    }
  };
  const Be = (p, h, b, w, k, C, I) => {
    for (let R = 0; R < h.length; R++) {
      const T = p[R];
      const E = h[R];
      const D = T.el && (T.type === nt || !rn(T, E) || T.shapeFlag & 70) ? g(T.el) : b;
      j(T, E, D, null, w, k, C, I, true);
    }
  };
  const Xe = (p, h, b, w, k, C, I) => {
    if (b !== w) {
      if (b !== fe) {
        for (const R in b) {
          if (!Mn(R) && !(R in w)) {
            r(p, R, b[R], null, I, h.children, k, C, M);
          }
        }
      }
      for (const R in w) {
        if (Mn(R)) {
          continue;
        }
        const T = w[R];
        const E = b[R];
        if (T !== E && R !== "value") {
          r(p, R, E, T, I, h.children, k, C, M);
        }
      }
      if ("value" in w) {
        r(p, "value", b.value, w.value);
      }
    }
  };
  const _e = (p, h, b, w, k, C, I, S, R) => {
    const E = h.el = p ? p.el : l("");
    const D = h.anchor = p ? p.anchor : l("");
    let {
      patchFlag: O,
      dynamicChildren: _,
      slotScopeIds: W
    } = h;
    if (W) {
      S = S ? S.concat(W) : W;
    }
    if (p == null) {
      o(E, b, w);
      o(D, b, w);
      Te(h.children, b, D, k, C, I, S, R);
    } else if (O > 0 && O & 64 && _ && p.dynamicChildren) {
      Be(p.dynamicChildren, _, b, k, C, I, S);
      if (h.key != null || k && h === k.subTree) {
        _o(p, h, true);
      }
    } else {
      ne(p, h, b, D, k, C, I, S, R);
    }
  };
  const it = (p, h, b, w, k, C, I, S, R) => {
    h.slotScopeIds = S;
    if (p == null) {
      if (h.shapeFlag & 512) {
        k.ctx.activate(h, b, w, I, R);
      } else {
        ut(h, b, w, k, C, I, R);
      }
    } else {
      ft(p, h, R);
    }
  };
  const ut = (p, h, b, w, k, C, I) => {
    const R = p.component = Rc(p, w, k);
    if (Uo(p)) {
      R.ctx.renderer = ve;
    }
    Ec(R);
    if (R.asyncDep) {
      if (k) {
        k.registerDep(R, me);
      }
      if (!p.el) {
        const T = R.subTree = rt(Ot);
        Z(null, T, h, b);
      }
      return;
    }
    me(R, p, h, b, k, C, I);
  };
  const ft = (p, h, b) => {
    const k = h.component = p.component;
    if (ji(p, h, b)) {
      if (k.asyncDep && !k.asyncResolved) {
        ie(k, h, b);
        return;
      } else {
        k.next = h;
        Ri(k.update);
        k.update();
      }
    } else {
      h.el = p.el;
      k.vnode = h;
    }
  };
  const me = (p, h, b, w, k, C, I) => {
    const R = () => {
      if (p.isMounted) {
        let {
          next: O,
          bu: _,
          u: W,
          parent: ee,
          vnode: se
        } = p;
        let te = O;
        let ge;
        Ct(p, false);
        if (O) {
          O.el = se.el;
          ie(p, O, I);
        } else {
          O = se;
        }
        if (_) {
          l0(_);
        }
        if (ge = O.props && O.props.onVnodeBeforeUpdate) {
          et(ge, ee, O, se);
        }
        Ct(p, true);
        const K = f0(p);
        const we = p.subTree;
        p.subTree = K;
        j(we, K, g(we.el), q(we), p, k, C);
        O.el = K.el;
        if (te === null) {
          Ui(p, K.el);
        }
        if (W) {
          Fe(W, k);
        }
        if (ge = O.props && O.props.onVnodeUpdated) {
          Fe(() => et(ge, ee, O, se), k);
        }
      } else {
        let O;
        const {
          el: _,
          props: W
        } = h;
        const {
          bm: ee,
          m: se,
          parent: te
        } = p;
        const ge = Bn(h);
        Ct(p, false);
        if (ee) {
          l0(ee);
        }
        if (!ge && (O = W && W.onVnodeBeforeMount)) {
          et(O, te, h);
        }
        Ct(p, true);
        if (_ && dt) {
          const K = () => {
            p.subTree = f0(p);
            dt(_, p.subTree, p, k, null);
          };
          if (ge) {
            h.type.__asyncLoader().then(() => !p.isUnmounted && K());
          } else {
            K();
          }
        } else {
          const K = p.subTree = f0(p);
          j(null, K, b, w, p, k, C);
          h.el = K.el;
        }
        if (se) {
          Fe(se, k);
        }
        if (!ge && (O = W && W.onVnodeMounted)) {
          const K = h;
          Fe(() => et(O, te, K), k);
        }
        if ((h.shapeFlag & 256 || te && Bn(te.vnode) && te.vnode.shapeFlag & 256) && p.a) {
          Fe(p.a, k);
        }
        p.isMounted = true;
        h = b = w = null;
      }
    };
    const T = p.effect = new H0(R, () => G0(E), p.scope);
    const E = p.update = () => T.run();
    E.id = p.uid;
    Ct(p, true);
    E();
  };
  const ie = (p, h, b) => {
    h.component = p;
    const k = p.vnode.props;
    p.vnode = h;
    p.next = null;
    ic(p, h.props, k, b);
    ac(p, h.children, b);
    Qt();
    Cx(p);
    Xt();
  };
  const ne = (p, h, b, w, k, C, I, S, R = false) => {
    const E = p && p.children;
    const D = p ? p.shapeFlag : 0;
    const O = h.children;
    const {
      patchFlag: _,
      shapeFlag: W
    } = h;
    if (_ > 0) {
      if (_ & 128) {
        Oe(E, O, b, w, k, C, I, S, R);
        return;
      } else if (_ & 256) {
        qe(E, O, b, w, k, C, I, S, R);
        return;
      }
    }
    if (W & 8) {
      if (D & 16) {
        M(E, k, C);
      }
      if (O !== E) {
        y(b, O);
      }
    } else if (D & 16) {
      if (W & 16) {
        Oe(E, O, b, w, k, C, I, S, R);
      } else {
        M(E, k, C, true);
      }
    } else {
      if (D & 8) {
        y(b, "");
      }
      if (W & 16) {
        Te(O, b, w, k, C, I, S, R);
      }
    }
  };
  const qe = (p, h, b, w, k, C, I, S, R) => {
    p = p || qt;
    h = h || qt;
    const E = p.length;
    const D = h.length;
    const O = Math.min(E, D);
    let _;
    for (_ = 0; _ < O; _++) {
      const W = h[_] = R ? ht(h[_]) : xt(h[_]);
      j(p[_], W, b, null, k, C, I, S, R);
    }
    if (E > D) {
      M(p, k, C, true, false, O);
    } else {
      Te(h, b, w, k, C, I, S, R, O);
    }
  };
  const Oe = (p, h, b, w, k, C, I, S, R) => {
    let E = 0;
    const D = h.length;
    let O = p.length - 1;
    let _ = D - 1;
    for (; E <= O && E <= _;) {
      const W = p[E];
      const ee = h[E] = R ? ht(h[E]) : xt(h[E]);
      if (rn(W, ee)) {
        j(W, ee, b, null, k, C, I, S, R);
      } else {
        break;
      }
      E++;
    }
    for (; E <= O && E <= _;) {
      const W = p[O];
      const ee = h[_] = R ? ht(h[_]) : xt(h[_]);
      if (rn(W, ee)) {
        j(W, ee, b, null, k, C, I, S, R);
      } else {
        break;
      }
      O--;
      _--;
    }
    if (E > O) {
      if (E <= _) {
        const W = _ + 1;
        const ee = W < D ? h[W].el : w;
        for (; E <= _;) {
          j(null, h[E] = R ? ht(h[E]) : xt(h[E]), b, ee, k, C, I, S, R);
          E++;
        }
      }
    } else if (E > _) {
      for (; E <= O;) {
        v(p[E], k, C, true);
        E++;
      }
    } else {
      const W = E;
      const ee = E;
      const se = new Map();
      for (E = ee; E <= _; E++) {
        const Pe = h[E] = R ? ht(h[E]) : xt(h[E]);
        if (Pe.key != null) {
          se.set(Pe.key, E);
        }
      }
      let te;
      let ge = 0;
      const K = _ - ee + 1;
      let we = false;
      let Pt = 0;
      const $e = new Array(K);
      for (E = 0; E < K; E++) {
        $e[E] = 0;
      }
      for (E = W; E <= O; E++) {
        const Pe = p[E];
        if (ge >= K) {
          v(Pe, k, C, true);
          continue;
        }
        let Ue;
        if (Pe.key != null) {
          Ue = se.get(Pe.key);
        } else {
          for (te = ee; te <= _; te++) {
            if ($e[te - ee] === 0 && rn(Pe, h[te])) {
              Ue = te;
              break;
            }
          }
        }
        if (Ue === undefined) {
          v(Pe, k, C, true);
        } else {
          $e[Ue - ee] = E + 1;
          if (Ue >= Pt) {
            Pt = Ue;
          } else {
            we = true;
          }
          j(Pe, h[Ue], b, null, k, C, I, S, R);
          ge++;
        }
      }
      const jt = we ? dc($e) : qt;
      te = jt.length - 1;
      E = K - 1;
      for (; E >= 0; E--) {
        const Pe = ee + E;
        const Ue = h[Pe];
        const Ut = Pe + 1 < D ? h[Pe + 1].el : w;
        if ($e[E] === 0) {
          j(null, Ue, b, Ut, k, C, I, S, R);
        } else if (we) {
          if (te < 0 || E !== jt[te]) {
            z(Ue, b, Ut, 2);
          } else {
            te--;
          }
        }
      }
    }
  };
  const z = (p, h, b, w, k = null) => {
    const {
      el: I,
      type: S,
      transition: R,
      children: T,
      shapeFlag: E
    } = p;
    if (E & 6) {
      z(p.component.subTree, h, b, w);
      return;
    }
    if (E & 128) {
      p.suspense.move(h, b, w);
      return;
    }
    if (E & 64) {
      S.move(p, h, b, ve);
      return;
    }
    if (S === nt) {
      o(I, h, b);
      for (let O = 0; O < T.length; O++) {
        z(T[O], h, b, w);
      }
      o(p.anchor, h, b);
      return;
    }
    if (S === zn) {
      le(p, h, b);
      return;
    }
    if (w !== 2 && E & 1 && R) {
      if (w === 0) {
        R.beforeEnter(I);
        o(I, h, b);
        Fe(() => R.enter(I), k);
      } else {
        const {
          leave: O,
          delayLeave: _,
          afterLeave: W
        } = R;
        const ee = () => o(I, h, b);
        const se = () => {
          O(I, () => {
            ee();
            if (W) {
              W();
            }
          });
        };
        if (_) {
          _(I, ee, se);
        } else {
          se();
        }
      }
    } else {
      o(I, h, b);
    }
  };
  const v = (p, h, b, w = false, k = false) => {
    const {
      type: I,
      props: S,
      ref: R,
      children: T,
      dynamicChildren: E,
      shapeFlag: D,
      patchFlag: O,
      dirs: _
    } = p;
    if (R != null) {
      A0(R, null, b, p, true);
    }
    if (D & 256) {
      h.ctx.deactivate(p);
      return;
    }
    const W = D & 1 && _;
    const ee = !Bn(p);
    let se;
    if (ee && (se = S && S.onVnodeBeforeUnmount)) {
      et(se, h, p);
    }
    if (D & 6) {
      L(p.component, b, w);
    } else {
      if (D & 128) {
        p.suspense.unmount(b, w);
        return;
      }
      if (W) {
        kt(p, null, h, "beforeUnmount");
      }
      if (D & 64) {
        p.type.remove(p, h, b, k, ve, w);
      } else if (E && (I !== nt || O > 0 && O & 64)) {
        M(E, h, b, false, true);
      } else if (I === nt && O & 384 || !k && D & 16) {
        M(T, h, b);
      }
      if (w) {
        N(p);
      }
    }
    if (ee && (se = S && S.onVnodeUnmounted) || W) {
      Fe(() => {
        if (se) {
          et(se, h, p);
        }
        if (W) {
          kt(p, null, h, "unmounted");
        }
      }, b);
    }
  };
  const N = p => {
    const {
      type: b,
      el: w,
      anchor: k,
      transition: C
    } = p;
    if (b === nt) {
      F(w, k);
      return;
    }
    if (b === zn) {
      $(p);
      return;
    }
    const I = () => {
      i(w);
      if (C && !C.persisted && C.afterLeave) {
        C.afterLeave();
      }
    };
    if (p.shapeFlag & 1 && C && !C.persisted) {
      const {
        leave: S,
        delayLeave: R
      } = C;
      const T = () => S(w, I);
      if (R) {
        R(p.el, I, T);
      } else {
        T();
      }
    } else {
      I();
    }
  };
  const F = (p, h) => {
    let b;
    for (; p !== h;) {
      b = A(p);
      i(p);
      p = b;
    }
    i(h);
  };
  const L = (p, h, b) => {
    const {
      bum: k,
      scope: C,
      update: I,
      subTree: S,
      um: R
    } = p;
    if (k) {
      l0(k);
    }
    C.stop();
    if (I) {
      I.active = false;
      v(S, p, h, b);
    }
    if (R) {
      Fe(R, h);
    }
    Fe(() => {
      p.isUnmounted = true;
    }, h);
    if (h && h.pendingBranch && !h.isUnmounted && p.asyncDep && !p.asyncResolved && p.suspenseId === h.pendingId) {
      h.deps--;
      if (h.deps === 0) {
        h.resolve();
      }
    }
  };
  const M = (p, h, b, w = false, k = false, C = 0) => {
    for (let S = C; S < p.length; S++) {
      v(p[S], h, b, w, k);
    }
  };
  const q = p => {
    if (p.shapeFlag & 6) {
      return q(p.component.subTree);
    } else if (p.shapeFlag & 128) {
      return p.suspense.next();
    } else {
      return A(p.anchor || p.el);
    }
  };
  const ce = (p, h, b) => {
    if (p == null) {
      if (h._vnode) {
        v(h._vnode, null, null, true);
      }
    } else {
      j(h._vnode || null, p, h, null, null, null, b);
    }
    Cx();
    Io();
    h._vnode = p;
  };
  const ve = {
    p: j,
    um: v,
    m: z,
    r: N,
    mt: ut,
    mc: Te,
    pc: ne,
    pbc: Be,
    n: q,
    o: e
  };
  let Ve;
  let dt;
  if (t) {
    [Ve, dt] = t(ve);
  }
  return {
    render: ce,
    hydrate: Ve,
    createApp: xc(ce, Ve)
  };
}
function Ct({
              effect: e,
              update: t
            }, n) {
  e.allowRecurse = t.allowRecurse = n;
}
function fc(e, t) {
  return (!e || e && !e.pendingBranch) && t && !t.persisted;
}
function _o(e, t, n = false) {
  const o = e.children;
  const i = t.children;
  if (G(o) && G(i)) {
    for (let r = 0; r < o.length; r++) {
      const a = o[r];
      let l = i[r];
      if (l.shapeFlag & 1 && !l.dynamicChildren) {
        if (l.patchFlag <= 0 || l.patchFlag === 32) {
          l = i[r] = ht(i[r]);
          l.el = a.el;
        }
        if (!n) {
          _o(a, l);
        }
      }
      if (l.type === t0) {
        l.el = a.el;
      }
    }
  }
}
function dc(e) {
  const n = e.slice();
  const x = [0];
  let o;
  let i;
  let r;
  let a;
  let l;
  const f = e.length;
  for (o = 0; o < f; o++) {
    const m = e[o];
    if (m !== 0) {
      i = x[x.length - 1];
      if (e[i] < m) {
        n[o] = i;
        x.push(o);
        continue;
      }
      r = 0;
      a = x.length - 1;
      for (; r < a;) {
        l = r + a >> 1;
        if (e[x[l]] < m) {
          r = l + 1;
        } else {
          a = l;
        }
      }
      if (m < e[x[r]]) {
        if (r > 0) {
          n[o] = x[r - 1];
        }
        x[r] = o;
      }
    }
  }
  r = x.length;
  a = x[r - 1];
  for (; r-- > 0;) {
    x[r] = a;
    a = n[a];
  }
  return x;
}
const pc = e => e.__isTeleport;
const nt = Symbol.for("v-fgt");
const t0 = Symbol.for("v-txt");
const Ot = Symbol.for("v-cmt");
const zn = Symbol.for("v-stc");
const fn = [];
let Ye = null;
function tt(e = false) {
  fn.push(Ye = e ? null : []);
}
function hc() {
  fn.pop();
  Ye = fn[fn.length - 1] || null;
}
let gn = 1;
function Ux(e) {
  gn += e;
}
function $o(e) {
  e.dynamicChildren = gn > 0 ? Ye || qt : null;
  hc();
  if (gn > 0 && Ye) {
    Ye.push(e);
  }
  return e;
}
function ct(e, t, n, x, o, i) {
  return $o(J(e, t, n, x, o, i, true));
}
function mc(e, t, n, x, o) {
  return $o(rt(e, t, n, x, o, true));
}
function gc(e) {
  if (e) {
    return e.__v_isVNode === true;
  } else {
    return false;
  }
}
function rn(e, t) {
  return e.type === t.type && e.key === t.key;
}
const n0 = "__vInternal";
const Wo = ({
              key: e
            }) => e != null ? e : null;
const Ln = ({
              ref: e,
              ref_key: t,
              ref_for: n
            }) => {
  if (typeof e == "number") {
    e = "" + e;
  }
  if (e != null) {
    if (ye(e) || Ae(e) || X(e)) {
      return {
        i: Ge,
        r: e,
        k: t,
        f: !!n
      };
    } else {
      return e;
    }
  } else {
    return null;
  }
};
function J(e, t = null, n = null, x = 0, o = null, i = e === nt ? 0 : 1, r = false, a = false) {
  const f = {
    __v_isVNode: true,
    __v_skip: true,
    type: e,
    props: t,
    key: t && Wo(t),
    ref: t && Ln(t),
    scopeId: Oo,
    slotScopeIds: null,
    children: n,
    component: null,
    suspense: null,
    ssContent: null,
    ssFallback: null,
    dirs: null,
    transition: null,
    el: null,
    anchor: null,
    target: null,
    targetAnchor: null,
    staticCount: 0,
    shapeFlag: i,
    patchFlag: x,
    dynamicProps: o,
    dynamicChildren: null,
    appContext: null,
    ctx: Ge
  };
  if (a) {
    Q0(f, n);
    if (i & 128) {
      e.normalize(f);
    }
  } else if (n) {
    f.shapeFlag |= ye(n) ? 8 : 16;
  }
  if (gn > 0 && !r && Ye && (f.patchFlag > 0 || i & 6) && f.patchFlag !== 32) {
    Ye.push(f);
  }
  return f;
}
const rt = vc;
function vc(e, t = null, n = null, x = 0, o = null, i = false) {
  if (!e || e === Ni) {
    e = Ot;
  }
  if (gc(e)) {
    const l = Jt(e, t, true);
    if (n) {
      Q0(l, n);
    }
    if (gn > 0 && !i && Ye) {
      if (l.shapeFlag & 6) {
        Ye[Ye.indexOf(e)] = l;
      } else {
        Ye.push(l);
      }
    }
    l.patchFlag |= -2;
    return l;
  }
  if (Oc(e)) {
    e = e.__vccOpts;
  }
  if (t) {
    t = bc(t);
    let {
      class: l,
      style: f
    } = t;
    if (l && !ye(l)) {
      t.class = q0(l);
    }
    if (de(f)) {
      if (yo(f) && !G(f)) {
        f = Re({}, f);
      }
      t.style = L0(f);
    }
  }
  const a = ye(e) ? 1 : Fi(e) ? 128 : pc(e) ? 64 : de(e) ? 4 : X(e) ? 2 : 0;
  return J(e, t, n, x, o, a, i, true);
}
function bc(e) {
  if (e) {
    if (yo(e) || n0 in e) {
      return Re({}, e);
    } else {
      return e;
    }
  } else {
    return null;
  }
}
function Jt(e, t, n = false) {
  const {
    props: o,
    ref: i,
    patchFlag: r,
    children: a
  } = e;
  const l = t ? kc(o || {}, t) : o;
  return {
    __v_isVNode: true,
    __v_skip: true,
    type: e.type,
    props: l,
    key: l && Wo(l),
    ref: t && t.ref ? n && i ? G(i) ? i.concat(Ln(t)) : [i, Ln(t)] : Ln(t) : i,
    scopeId: e.scopeId,
    slotScopeIds: e.slotScopeIds,
    children: a,
    target: e.target,
    targetAnchor: e.targetAnchor,
    staticCount: e.staticCount,
    shapeFlag: e.shapeFlag,
    patchFlag: t && e.type !== nt ? r === -1 ? 16 : r | 16 : r,
    dynamicProps: e.dynamicProps,
    dynamicChildren: e.dynamicChildren,
    appContext: e.appContext,
    dirs: e.dirs,
    transition: e.transition,
    component: e.component,
    suspense: e.suspense,
    ssContent: e.ssContent && Jt(e.ssContent),
    ssFallback: e.ssFallback && Jt(e.ssFallback),
    el: e.el,
    anchor: e.anchor,
    ctx: e.ctx,
    ce: e.ce
  };
}
function yc(e = " ", t = 0) {
  return rt(t0, null, e, t);
}
function wc(e, t) {
  const x = rt(zn, null, e);
  x.staticCount = t;
  return x;
}
function Bt(e = "", t = false) {
  if (t) {
    tt();
    return mc(Ot, null, e);
  } else {
    return rt(Ot, null, e);
  }
}
function xt(e) {
  if (e == null || typeof e === "boolean") {
    return rt(Ot);
  } else if (G(e)) {
    return rt(nt, null, e.slice());
  } else if (typeof e === "object") {
    return ht(e);
  } else {
    return rt(t0, null, String(e));
  }
}
function ht(e) {
  if (e.el === null && e.patchFlag !== -1 || e.memo) {
    return e;
  } else {
    return Jt(e);
  }
}
function Q0(e, t) {
  let x = 0;
  const {
    shapeFlag: o
  } = e;
  if (t == null) {
    t = null;
  } else if (G(t)) {
    x = 16;
  } else if (typeof t == "object") {
    if (o & 65) {
      const i = t.default;
      if (i) {
        if (i._c) {
          i._d = false;
        }
        Q0(e, i());
        if (i._c) {
          i._d = true;
        }
      }
      return;
    } else {
      x = 32;
      const i = t._;
      if (!i && !(n0 in t)) {
        t._ctx = Ge;
      } else if (i === 3 && Ge) {
        if (Ge.slots._ === 1) {
          t._ = 1;
        } else {
          t._ = 2;
          e.patchFlag |= 1024;
        }
      }
    }
  } else if (X(t)) {
    t = {
      default: t,
      _ctx: Ge
    };
    x = 32;
  } else {
    t = String(t);
    if (o & 64) {
      x = 16;
      t = [yc(t)];
    } else {
      x = 8;
    }
  }
  e.children = t;
  e.shapeFlag |= x;
}
function kc(...e) {
  const n = {};
  for (let x = 0; x < e.length; x++) {
    const o = e[x];
    for (const i in o) {
      if (i === "class") {
        if (n.class !== o.class) {
          n.class = q0([n.class, o.class]);
        }
      } else if (i === "style") {
        n.style = L0([n.style, o.style]);
      } else if (Jn(i)) {
        const r = n[i];
        const a = o[i];
        if (a && r !== a && !(G(r) && r.includes(a))) {
          n[i] = r ? [].concat(r, a) : a;
        }
      } else if (i !== "") {
        n[i] = o[i];
      }
    }
  }
  return n;
}
function et(e, t, n, x = null) {
  Qe(e, t, 7, [n, x]);
}
const Cc = Vo();
let Sc = 0;
function Rc(e, t, n) {
  const o = e.type;
  const i = (t ? t.appContext : e.appContext) || Cc;
  const r = {
    uid: Sc++,
    vnode: e,
    type: o,
    parent: t,
    appContext: i,
    root: null,
    next: null,
    subTree: null,
    effect: null,
    update: null,
    scope: new $r(true),
    render: null,
    proxy: null,
    exposed: null,
    exposeProxy: null,
    withProxy: null,
    provides: t ? t.provides : Object.create(i.provides),
    accessCache: null,
    renderCache: [],
    components: null,
    directives: null,
    propsOptions: Lo(o, i),
    emitsOptions: To(o, i),
    emit: null,
    emitted: null,
    propsDefaults: fe,
    inheritAttrs: o.inheritAttrs,
    ctx: fe,
    data: fe,
    props: fe,
    attrs: fe,
    slots: fe,
    refs: fe,
    setupState: fe,
    setupContext: null,
    attrsProxy: null,
    slotsProxy: null,
    suspense: n,
    suspenseId: n ? n.pendingId : 0,
    asyncDep: null,
    asyncResolved: false,
    isMounted: false,
    isUnmounted: false,
    isDeactivated: false,
    bc: null,
    c: null,
    bm: null,
    m: null,
    bu: null,
    u: null,
    um: null,
    bum: null,
    da: null,
    a: null,
    rtg: null,
    rtc: null,
    ec: null,
    sp: null
  };
  r.ctx = {
    _: r
  };
  r.root = t ? t.root : r;
  r.emit = Ai.bind(null, r);
  if (e.ce) {
    e.ce(r);
  }
  return r;
}
let Se = null;
let X0;
let Vt;
let Nx = "__VUE_INSTANCE_SETTERS__";
if (!(Vt = v0()[Nx])) {
  Vt = v0()[Nx] = [];
}
Vt.push(e => Se = e);
X0 = e => {
  if (Vt.length > 1) {
    Vt.forEach(n => n(e));
  } else {
    Vt[0](e);
  }
};
const Gt = e => {
  X0(e);
  e.scope.on();
};
const At = () => {
  if (Se) {
    Se.scope.off();
  }
  X0(null);
};
function Ko(e) {
  return e.vnode.shapeFlag & 4;
}
let vn = false;
function Ec(e, t = false) {
  vn = t;
  const {
    props: n,
    children: x
  } = e.vnode;
  const o = Ko(e);
  rc(e, n, o, t);
  sc(e, x);
  const i = o ? Ic(e, t) : undefined;
  vn = false;
  return i;
}
function Ic(e, t) {
  const x = e.type;
  e.accessCache = Object.create(null);
  e.proxy = wo(new Proxy(e.ctx, Yi));
  const {
    setup: o
  } = x;
  if (o) {
    const i = e.setupContext = o.length > 1 ? Tc(e) : null;
    Gt(e);
    Qt();
    const r = vt(o, e, 0, [e.props, i]);
    Xt();
    At();
    if (no(r)) {
      r.then(At, At);
      if (t) {
        return r.then(a => {
          Fx(e, a, t);
        }).catch(a => {
          Qn(a, e, 0);
        });
      }
      e.asyncDep = r;
    } else {
      Fx(e, r, t);
    }
  } else {
    Jo(e, t);
  }
}
function Fx(e, t, n) {
  if (X(t)) {
    if (e.type.__ssrInlineRender) {
      e.ssrRender = t;
    } else {
      e.render = t;
    }
  } else if (de(t)) {
    e.setupState = So(t);
  }
  Jo(e, n);
}
let Mx;
function Jo(e, t, n) {
  const o = e.type;
  if (!e.render) {
    if (!t && Mx && !o.render) {
      const i = o.template || Y0(e).template;
      if (i) {
        const {
          isCustomElement: r,
          compilerOptions: a
        } = e.appContext.config;
        const {
          delimiters: l,
          compilerOptions: f
        } = o;
        const m = Re(Re({
          isCustomElement: r,
          delimiters: l
        }, a), f);
        o.render = Mx(i, m);
      }
    }
    e.render = o.render || Ze;
  }
  {
    Gt(e);
    Qt();
    try {
      Zi(e);
    } finally {
      Xt();
      At();
    }
  }
}
function Ac(e) {
  return e.attrsProxy || (e.attrsProxy = new Proxy(e.attrs, {
    get(n, x) {
      Me(e, "get", "$attrs");
      return n[x];
    }
  }));
}
function Tc(e) {
  const n = x => {
    e.exposed = x || {};
  };
  return {
    get attrs() {
      return Ac(e);
    },
    slots: e.slots,
    emit: e.emit,
    expose: n
  };
}
function x0(e) {
  if (e.exposed) {
    return e.exposeProxy || (e.exposeProxy = new Proxy(So(wo(e.exposed)), {
      get(n, x) {
        if (x in n) {
          return n[x];
        }
        if (x in un) {
          return un[x](e);
        }
      },
      has(n, x) {
        return x in n || x in un;
      }
    }));
  }
}
function Oc(e) {
  return X(e) && "__vccOpts" in e;
}
const Pc = (e, t) => yi(e, t, vn);
const jc = Symbol.for("v-scx");
const Uc = () => Vn(jc);
const Nc = "3.3.11";
const Fc = "http://www.w3.org/2000/svg";
const Rt = typeof document !== "undefined" ? document : null;
const Bx = Rt && Rt.createElement("template");
const Mc = {
  insert: (e, t, n) => {
    t.insertBefore(e, n || null);
  },
  remove: e => {
    const n = e.parentNode;
    if (n) {
      n.removeChild(e);
    }
  },
  createElement: (e, t, n, x) => {
    const i = t ? Rt.createElementNS(Fc, e) : Rt.createElement(e, n ? {
      is: n
    } : undefined);
    if (e === "select" && x && x.multiple != null) {
      i.setAttribute("multiple", x.multiple);
    }
    return i;
  },
  createText: e => Rt.createTextNode(e),
  createComment: e => Rt.createComment(e),
  setText: (e, t) => {
    e.nodeValue = t;
  },
  setElementText: (e, t) => {
    e.textContent = t;
  },
  parentNode: e => e.parentNode,
  nextSibling: e => e.nextSibling,
  querySelector: e => Rt.querySelector(e),
  setScopeId(e, t) {
    e.setAttribute(t, "");
  },
  insertStaticContent(e, t, n, x, o, i) {
    const a = n ? n.previousSibling : t.lastChild;
    if (o && (o === i || o.nextSibling)) {
      for (; [] && (t.insertBefore(o.cloneNode(true), n), !(o === i || !(o = o.nextSibling))););
    } else {
      Bx.innerHTML = x ? "<svg>" + e + "</svg>" : e;
      const l = Bx.content;
      if (x) {
        const f = l.firstChild;
        for (; f.firstChild;) {
          l.appendChild(f.firstChild);
        }
        l.removeChild(f);
      }
      t.insertBefore(l, n);
    }
    return [a ? a.nextSibling : t.firstChild, n ? n.previousSibling : t.lastChild];
  }
};
const Bc = Symbol("_vtc");
const Vc = {
  name: String,
  type: String,
  css: {
    type: Boolean,
    default: true
  },
  duration: [String, Number, Object],
  enterFromClass: String,
  enterActiveClass: String,
  enterToClass: String,
  appearFromClass: String,
  appearActiveClass: String,
  appearToClass: String,
  leaveFromClass: String,
  leaveActiveClass: String,
  leaveToClass: String
};
Re({}, Vi, Vc);
function zc(e, t, n) {
  const o = e[Bc];
  if (o) {
    t = (t ? [t, ...o] : [...o]).join(" ");
  }
  if (t == null) {
    e.removeAttribute("class");
  } else if (n) {
    e.setAttribute("class", t);
  } else {
    e.className = t;
  }
}
const ex = Symbol("_vod");
const jn = {
  beforeMount(e, {
    value: t
  }, {
                transition: n
              }) {
    e[ex] = e.style.display === "none" ? "" : e.style.display;
    if (n && t) {
      n.beforeEnter(e);
    } else {
      cn(e, t);
    }
  },
  mounted(e, {
    value: t
  }, {
            transition: n
          }) {
    if (n && t) {
      n.enter(e);
    }
  },
  updated(e, {
    value: t,
    oldValue: n
  }, {
            transition: x
          }) {
    if (!t != !n) {
      if (x) {
        if (t) {
          x.beforeEnter(e);
          cn(e, true);
          x.enter(e);
        } else {
          x.leave(e, () => {
            cn(e, false);
          });
        }
      } else {
        cn(e, t);
      }
    }
  },
  beforeUnmount(e, {
    value: t
  }) {
    cn(e, t);
  }
};
function cn(e, t) {
  e.style.display = t ? e[ex] : "none";
}
function Lc(e, t, n) {
  const o = e.style;
  const i = ye(n);
  if (n && !i) {
    if (t && !ye(t)) {
      for (const r in t) {
        if (n[r] == null) {
          T0(o, r, "");
        }
      }
    }
    for (const r in n) {
      T0(o, r, n[r]);
    }
  } else {
    const r = o.display;
    if (i) {
      if (t !== n) {
        o.cssText = n;
      }
    } else if (t) {
      e.removeAttribute("style");
    }
    if (ex in e) {
      o.display = r;
    }
  }
}
const Vx = /\s*!important$/;
function T0(e, t, n) {
  if (G(n)) {
    n.forEach(o => T0(e, t, o));
  } else {
    if (n == null) {
      n = "";
    }
    if (t.startsWith("--")) {
      e.setProperty(t, n);
    } else {
      const o = qc(e, t);
      if (Vx.test(n)) {
        e.setProperty(Zt(o), n.replace(Vx, ""), "important");
      } else {
        e[o] = n;
      }
    }
  }
}
const zx = ["Webkit", "Moz", "ms"];
const h0 = {};
function qc(e, t) {
  const x = h0[t];
  if (x) {
    return x;
  }
  let o = Wt(t);
  if (o !== "filter" && o in e) {
    return h0[t] = o;
  }
  o = ro(o);
  for (let i = 0; i < zx.length; i++) {
    const r = zx[i] + o;
    if (r in e) {
      return h0[t] = r;
    }
  }
  return t;
}
const Lx = "http://www.w3.org/1999/xlink";
function Dc(e, t, n, x, o) {
  if (x && t.startsWith("xlink:")) {
    if (n == null) {
      e.removeAttributeNS(Lx, t.slice(6, t.length));
    } else {
      e.setAttributeNS(Lx, t, n);
    }
  } else {
    const r = _r(t);
    if (n == null || r && !io(n)) {
      e.removeAttribute(t);
    } else {
      e.setAttribute(t, r ? "" : n);
    }
  }
}
function Hc(e, t, n, x, o, i, r) {
  if (t === "innerHTML" || t === "textContent") {
    if (x) {
      r(x, o, i);
    }
    e[t] = n == null ? "" : n;
    return;
  }
  const l = e.tagName;
  if (t === "value" && l !== "PROGRESS" && !l.includes("-")) {
    e._value = n;
    const m = l === "OPTION" ? e.getAttribute("value") : e.value;
    const y = n == null ? "" : n;
    if (m !== y) {
      e.value = y;
    }
    if (n == null) {
      e.removeAttribute(t);
    }
    return;
  }
  let f = false;
  if (n === "" || n == null) {
    const m = typeof e[t];
    if (m === "boolean") {
      n = io(n);
    } else if (n == null && m === "string") {
      n = "";
      f = true;
    } else if (m === "number") {
      n = 0;
      f = true;
    }
  }
  try {
    e[t] = n;
  } catch {}
  if (f) {
    e.removeAttribute(t);
  }
}
function _c(e, t, n, x) {
  e.addEventListener(t, n, x);
}
function $c(e, t, n, x) {
  e.removeEventListener(t, n, x);
}
const qx = Symbol("_vei");
function Wc(e, t, n, x, o = null) {
  const r = e[qx] || (e[qx] = {});
  const a = r[t];
  if (x && a) {
    a.value = x;
  } else {
    const [l, f] = Kc(t);
    if (x) {
      const m = r[t] = Yc(x, o);
      _c(e, l, m, f);
    } else if (a) {
      $c(e, l, a, f);
      r[t] = undefined;
    }
  }
}
const Dx = /(?:Once|Passive|Capture)$/;
function Kc(e) {
  let n;
  if (Dx.test(e)) {
    n = {};
    let o;
    for (; o = e.match(Dx);) {
      e = e.slice(0, e.length - o[0].length);
      n[o[0].toLowerCase()] = true;
    }
  }
  return [e[2] === ":" ? e.slice(3) : Zt(e.slice(2)), n];
}
let m0 = 0;
const Jc = Promise.resolve();
const Gc = () => m0 || (Jc.then(() => m0 = 0), m0 = Date.now());
function Yc(e, t) {
  const x = o => {
    if (!o._vts) {
      o._vts = Date.now();
    } else if (o._vts <= x.attached) {
      return;
    }
    Qe(Zc(o, x.value), t, 5, [o]);
  };
  x.value = e;
  x.attached = Gc();
  return x;
}
function Zc(e, t) {
  if (G(t)) {
    const x = e.stopImmediatePropagation;
    e.stopImmediatePropagation = () => {
      x.call(e);
      e._stopped = true;
    };
    return t.map(o => i => !i._stopped && o && o(i));
  } else {
    return t;
  }
}
const Hx = e => e.charCodeAt(0) === 111 && e.charCodeAt(1) === 110 && e.charCodeAt(2) > 96 && e.charCodeAt(2) < 123;
const Qc = (e, t, n, x, o = false, i, r, a, l) => {
  if (t === "class") {
    zc(e, x, o);
  } else if (t === "style") {
    Lc(e, n, x);
  } else if (Jn(t)) {
    if (!B0(t)) {
      Wc(e, t, n, x, r);
    }
  } else if (t[0] === "." ? (t = t.slice(1), true) : t[0] === "^" ? (t = t.slice(1), false) : Xc(e, t, x, o)) {
    Hc(e, t, x, i, r, a, l);
  } else {
    if (t === "true-value") {
      e._trueValue = x;
    } else if (t === "false-value") {
      e._falseValue = x;
    }
    Dc(e, t, x, o);
  }
};
function Xc(e, t, n, x) {
  if (x) {
    if (t === "innerHTML" || t === "textContent") {
      return true;
    } else if (t in e && Hx(t) && X(n)) {
      return true;
    } else {
      return false;
    }
  }
  if (t === "spellcheck" || t === "draggable" || t === "translate") {
    return false;
  }
  if (t === "form") {
    return false;
  }
  if (t === "list" && e.tagName === "INPUT") {
    return false;
  }
  if (t === "type" && e.tagName === "TEXTAREA") {
    return false;
  }
  if (t === "width" || t === "height") {
    const i = e.tagName;
    if (i === "IMG" || i === "VIDEO" || i === "CANVAS" || i === "SOURCE") {
      return false;
    }
  }
  if (Hx(t) && ye(n)) {
    return false;
  } else {
    return t in e;
  }
}
const es = Re({
  patchProp: Qc
}, Mc);
let _x;
function ts() {
  return _x || (_x = lc(es));
}
const ns = (...e) => {
  const n = ts().createApp(...e);
  const {
    mount: x
  } = n;
  n.mount = o => {
    const r = xs(o);
    if (!r) {
      return;
    }
    const a = n._component;
    if (!X(a) && !a.render && !a.template) {
      a.template = r.innerHTML;
    }
    r.innerHTML = "";
    const l = x(r, false, r instanceof SVGElement);
    if (r instanceof Element) {
      r.removeAttribute("v-cloak");
      r.setAttribute("data-v-app", "");
    }
    return l;
  };
  return n;
};
function xs(e) {
  if (ye(e)) {
    return document.querySelector(e);
  } else {
    return e;
  }
}
var O0 = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
function os(e) {
  if (e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default")) {
    return e.default;
  } else {
    return e;
  }
}
var mt = {};
var P0 = O0 && O0.__assign || function () {
  P0 = Object.assign || function (t) {
    for (var x, o = 1, i = arguments.length; o < i; o++) {
      x = arguments[o];
      for (var r in x) {
        if (Object.prototype.hasOwnProperty.call(x, r)) {
          t[r] = x[r];
        }
      }
    }
    return t;
  };
  return P0.apply(this, arguments);
};
Object.defineProperty(mt, "__esModule", {
  value: true
});
mt.join = mt.subst = mt.query = undefined;
function rs(e, t, n) {
  if (n === undefined) {
    n = {};
  }
  if (typeof t === "string") {
    var o = e;
    var i = t;
    var r = n;
    return $x(i, r, o);
  } else {
    var a = e;
    var r = t;
    return $x(a, r);
  }
}
var zt = mt.default = rs;
function $x(e, t, n) {
  var o = Yo(e, t);
  var i = o.renderedPath;
  var r = o.remainingParams;
  var a = cs(r);
  var l = Go(a);
  var f = j0(i, "?", l);
  if (n) {
    return j0(n, "/", f);
  } else {
    return f;
  }
}
function Go(e) {
  return new URLSearchParams(e).toString();
}
mt.query = Go;
function is(e, t) {
  var n = Yo(e, t).renderedPath;
  return n;
}
mt.subst = is;
function Yo(e, t) {
  var x = P0({}, t);
  var o = ["boolean", "string", "number"];
  var i = e.replace(/:\w+/g, function (r) {
    var l = r.slice(1);
    if (/^\d+$/.test(l)) {
      return r;
    }
    if (!t.hasOwnProperty(l)) {
      throw new Error("Missing value for path parameter " + l + ".");
    }
    if (!o.includes(typeof t[l])) {
      throw new TypeError("Path parameter " + l + " cannot be of type " + typeof t[l] + ". " + ("Allowed types are: " + o.join(", ") + "."));
    }
    if (typeof t[l] == "string" && t[l].trim() === "") {
      throw new Error("Path parameter " + l + " cannot be an empty string.");
    }
    delete x[l];
    return encodeURIComponent(t[l]);
  });
  return {
    renderedPath: i,
    remainingParams: x
  };
}
function j0(e, t, n) {
  var o = e.endsWith(t) ? e.slice(0, -t.length) : e;
  var i = n.startsWith(t) ? n.slice(t.length) : n;
  if (o === "" || i === "") {
    return o + i;
  } else {
    return o + t + i;
  }
}
mt.join = j0;
function cs(e) {
  return Object.keys(e).filter(function (n) {
    return ss(e[n]);
  }).reduce(function (n, x) {
    n[x] = e[x];
    return n;
  }, {});
}
function ss(e) {
  return e != null;
}
var Un;
var as = new Uint8Array(16);
function ls() {
  if (!Un && (Un = typeof crypto !== "undefined" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== "undefined" && typeof msCrypto.getRandomValues == "function" && msCrypto.getRandomValues.bind(msCrypto), !Un)) {
    throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
  }
  return Un(as);
}
const us = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;
function fs(e) {
  return typeof e === "string" && us.test(e);
}
var ke = [];
for (var g0 = 0; g0 < 256; ++g0) {
  ke.push((g0 + 256).toString(16).substr(1));
}
function ds(e) {
  var n = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
  var x = (ke[e[n + 0]] + ke[e[n + 1]] + ke[e[n + 2]] + ke[e[n + 3]] + "-" + ke[e[n + 4]] + ke[e[n + 5]] + "-" + ke[e[n + 6]] + ke[e[n + 7]] + "-" + ke[e[n + 8]] + ke[e[n + 9]] + "-" + ke[e[n + 10]] + ke[e[n + 11]] + ke[e[n + 12]] + ke[e[n + 13]] + ke[e[n + 14]] + ke[e[n + 15]]).toLowerCase();
  if (!fs(x)) {
    throw TypeError("Stringified UUID is invalid");
  }
  return x;
}
function ps(e, t, n) {
  e = e || {};
  var o = e.random || (e.rng || ls)();
  o[6] = o[6] & 15 | 64;
  o[8] = o[8] & 63 | 128;
  if (t) {
    n = n || 0;
    for (var i = 0; i < 16; ++i) {
      t[n + i] = o[i];
    }
    return t;
  }
  return ds(o);
}
function Nn(e) {
  for (var n = 1; n < arguments.length; n++) {
    var x = arguments[n];
    for (var o in x) {
      e[o] = x[o];
    }
  }
  return e;
}
var hs = {
  read: function (e) {
    if (e[0] === "\"") {
      e = e.slice(1, -1);
    }
    return e.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent);
  },
  write: function (e) {
    return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g, decodeURIComponent);
  }
};
function U0(e, t) {
  function x(i, r, a) {
    if (typeof document !== "undefined") {
      a = Nn({}, t, a);
      if (typeof a.expires === "number") {
        a.expires = new Date(Date.now() + a.expires * 86400000);
      }
      if (a.expires) {
        a.expires = a.expires.toUTCString();
      }
      i = encodeURIComponent(i).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
      var f = "";
      for (var m in a) {
        if (a[m]) {
          f += "; " + m;
          if (a[m] !== true) {
            f += "=" + a[m].split(";")[0];
          }
        }
      }
      return document.cookie = i + "=" + e.write(r, i) + f;
    }
  }
  function o(i) {
    if (!(typeof document === "undefined" || arguments.length && !i)) {
      for (var a = document.cookie ? document.cookie.split("; ") : [], l = {}, f = 0; f < a.length; f++) {
        var m = a[f].split("=");
        var y = m.slice(1).join("=");
        try {
          var g = decodeURIComponent(m[0]);
          l[g] = e.read(y, g);
          if (i === g) {
            break;
          }
        } catch {}
      }
      if (i) {
        return l[i];
      } else {
        return l;
      }
    }
  }
  return Object.create({
    set: x,
    get: o,
    remove: function (i, r) {
      x(i, "", Nn({}, r, {
        expires: -1
      }));
    },
    withAttributes: function (i) {
      return U0(this.converter, Nn({}, this.attributes, i));
    },
    withConverter: function (i) {
      return U0(Nn({}, this.converter, i), this.attributes);
    }
  }, {
    attributes: {
      value: Object.freeze(t)
    },
    converter: {
      value: Object.freeze(e)
    }
  });
}
var Zo = U0(hs, {
  path: "/"
});
const ms = () => {
  const t = localStorage.getItem("uuid");
  if (t) {
    return t;
  }
  {
    const n = ps();
    localStorage.setItem("uuid", n);
    return n;
  }
};
const gs = e => {
  let n = "";
  let x = "";
  try {
    if (typeof document.hidden !== "undefined") {
      n = "hidden";
      x = "visibilitychange";
    } else if (typeof document.mozHidden !== "undefined") {
      n = "mozHidden";
      x = "mozvisibilitychange";
    } else if (typeof document.msHidden !== "undefined") {
      n = "msHidden";
      x = "msvisibilitychange";
    } else if (typeof document.webkitHidden !== "undefined") {
      n = "webkitHidden";
      x = "webkitvisibilitychange";
    }
    document.addEventListener(x, ys(function () {
      if (document[n]) {
        e(true);
      } else {
        console.log("页面显示");
        e(false);
      }
    }, 500));
  } catch (o) {
    console.log(o);
  }
};
const vs = e => {
  try {
    let n = navigator.userAgent.toLowerCase();
    let x = Qo("pid");
    console.log(x, n.includes("newsarticle"));
    if (x.includes("_oc") && n.includes("newsarticle")) {
      if (n.includes("newslite")) {
        Lt("snssdk35://category_feed?category=novel_channel&force_go_main=1");
      } else {
        Lt("snssdk143://category_feed?category=novel_channel");
      }
    }
    if (x.includes("_ks")) {
      const o = e && e.isJumpVideo;
      if (n.includes("nebula")) {
        Lt(o ? "ksnebula://liveaggregatesquare" : "ksnebula://home/<USER>");
        return;
      }
      if (n.includes("kwai")) {
        Lt(o ? "kwai://liveaggregatesquare" : "kwai://home/<USER>");
      }
    }
  } catch {}
};
const Lt = e => {
  var n = document.createElement("a");
  n.href = e;
  document.body.appendChild(n);
  n.click();
};
const Qo = (e, t) => {
  var x = new RegExp("(^|&)" + e + "=([^&]*)(&|$)");
  var o = window.location.search.substr(1).match(x);
  if (o != null) {
    if (t == 1) {
      return o[2].replace(/\./g, ":").toUpperCase();
    } else {
      return o[2];
    }
  } else {
    return "";
  }
};
const Wx = e => Zo.get(e);
const Kx = (e, t, n) => {
  let o = n;
  let i = new Date(new Date() * 1 + o * 1000);
  return Zo.set(e, t, {
    expires: i
  });
};
const bs = (e, t) => {
  console.log("开始了", t);
  if (t == "vivo") {
    var x = document.createElement("script");
    x.src = "https://h5.vivo.com.cn/qa/ifrstats/router.min.js";
    x.onload = function () {
      e();
    };
    document.head.appendChild(x);
  } else if (t == "oppo") {
    var x = document.createElement("script");
    x.src = "https://jits5.heytapdownload.com/cms-jits-heytapmobi-com/iframe/qa_router.min.js";
    x.onload = function () {
      e();
    };
    document.head.appendChild(x);
  } else {
    var x = document.createElement("script");
    x.src = "https://statres.quickapp.cn/quickapp/js/qa_router.min.js";
    x.onload = function () {
      e();
    };
    document.head.appendChild(x);
  }
};
function ys(e, t) {
  let n = 0;
  return function (...x) {
    const i = Date.now();
    if (i - n >= t) {
      n = i;
      return e.apply(this, x);
    }
  };
}
function ws(e) {
  const n = e.indexOf("clickid=");
  if (n === -1) {
    return null;
  }
  const x = n + 8;
  let o = e.indexOf("&", x);
  if (o === -1) {
    o = e.length;
  }
  return e.substring(x, o);
}
var Xo = {
  exports: {}
};
(function (e, t) {
  (function (x, o) {
    e.exports = o();
  })(O0, function () {
    return function () {
      var n = {
        686: function (i, r, a) {
          a.d(r, {
            default: function () {
              return Oe;
            }
          });
          var l = a(279);
          var f = a.n(l);
          var m = a(370);
          var y = a.n(m);
          var g = a(817);
          var A = a.n(g);
          function U(z) {
            try {
              return document.execCommand(z);
            } catch {
              return false;
            }
          }
          function H(v) {
            var F = A()(v);
            U("cut");
            return F;
          }
          var j = H;
          function V(z) {
            var N = document.documentElement.getAttribute("dir") === "rtl";
            var F = document.createElement("textarea");
            F.style.fontSize = "12pt";
            F.style.border = "0";
            F.style.padding = "0";
            F.style.margin = "0";
            F.style.position = "absolute";
            F.style[
              //decode_error: N is not defined
              B(N ? 930 : 854)] = "-9999px";
            var L = window.pageYOffset || document.documentElement.scrollTop;
            F.style.top = `${L}px`;
            F.setAttribute("readonly", "");
            F.value = z;
            return F;
          }
          function Z(v, N) {
            var L = V(v);
            N.container.appendChild(L);
            var M = A()(L);
            U("copy");
            L.remove();
            return M;
          }
          function oe(v) {
            var F = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {
              container: document.body
            };
            var L = "";
            if (typeof v === "string") {
              L = Z(v, F);
            } else if (v instanceof HTMLInputElement && !["text", "search", "url", "tel", "password"].includes(v == null ? undefined : v.type)) {
              L = Z(v.value, F);
            } else {
              L = A()(v);
              U("copy");
            }
            return L;
          }
          var le = oe;
          function $(z) {
            if (typeof Symbol == "function" && typeof Symbol.iterator === "symbol") {
              $ = function (F) {
                return typeof F;
              };
            } else {
              $ = function (F) {
                if (F && typeof Symbol === "function" && F.constructor === Symbol && F !== Symbol.prototype) {
                  return "symbol";
                } else {
                  return typeof F;
                }
              };
            }
            return $(z);
          }
          function he() {
            var N = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
            var F = N.action;
            var L = F === undefined ? "copy" : F;
            var M = N.container;
            var q = N.target;
            var ce = N.text;
            if (L !== "copy" && L !== "cut") {
              throw new Error("Invalid \"action\" value, use either \"copy\" or \"cut\"");
            }
            if (q !== undefined) {
              if (q && $(q) === "object" && q.nodeType === 1) {
                if (L === "copy" && q.hasAttribute("disabled")) {
                  throw new Error("Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute");
                }
                if (L === "cut" && (q.hasAttribute("readonly") || q.hasAttribute("disabled"))) {
                  throw new Error("Invalid \"target\" attribute. You can't cut text from elements with \"readonly\" or \"disabled\" attributes");
                }
              } else {
                throw new Error("Invalid \"target\" value, use a valid Element");
              }
            }
            if (ce) {
              return le(ce, {
                container: M
              });
            }
            if (q) {
              if (L === "cut") {
                return j(q);
              } else {
                return le(q, {
                  container: M
                });
              }
            }
          }
          var Le = he;
          function be(z) {
            if (typeof Symbol === "function" && typeof Symbol.iterator == "symbol") {
              be = function (F) {
                return typeof F;
              };
            } else {
              be = function (F) {
                if (F && typeof Symbol === "function" && F.constructor === Symbol && F !== Symbol.prototype) {
                  return "symbol";
                } else {
                  return typeof F;
                }
              };
            }
            return be(z);
          }
          function Te(z, v) {
            if (!(z instanceof v)) {
              throw new TypeError("Cannot call a class as a function");
            }
          }
          function He(z, v) {
            for (var F = 0; F < v.length; F++) {
              var L = v[F];
              L.enumerable = L.enumerable || false;
              L.configurable = true;
              if ("value" in L) {
                L.writable = true;
              }
              Object.defineProperty(z, L.key, L);
            }
          }
          function Be(z, v, N) {
            if (v) {
              He(z.prototype, v);
            }
            if (N) {
              He(z, N);
            }
            return z;
          }
          function Xe(z, v) {
            if (typeof v !== "function" && v !== null) {
              throw new TypeError("Super expression must either be null or a function");
            }
            z.prototype = Object.create(v && v.prototype, {
              constructor: {
                value: z,
                writable: true,
                configurable: true
              }
            });
            if (v) {
              _e(z, v);
            }
          }
          function _e(z, v) {
            _e = Object.setPrototypeOf || function (L, M) {
              L.__proto__ = M;
              return L;
            };
            return _e(z, v);
          }
          function it(z) {
            var v = me();
            return function () {
              var L = ie(z);
              var M;
              if (v) {
                var q = ie(this).constructor;
                M = Reflect.construct(L, arguments, q);
              } else {
                M = L.apply(this, arguments);
              }
              return ut(this, M);
            };
          }
          function ut(z, v) {
            if (v && (be(v) === "object" || typeof v === "function")) {
              return v;
            } else {
              return ft(z);
            }
          }
          function ft(z) {
            if (z === undefined) {
              throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
            }
            return z;
          }
          function me() {
            if (typeof Reflect === "undefined" || !Reflect.construct) {
              return false;
            }
            if (Reflect.construct.sham) {
              return false;
            }
            if (typeof Proxy == "function") {
              return true;
            }
            try {
              Date.prototype.toString.call(Reflect.construct(Date, [], function () {}));
              return true;
            } catch {
              return false;
            }
          }
          function ie(z) {
            ie = Object.setPrototypeOf ? Object.getPrototypeOf : function (F) {
              return F.__proto__ || Object.getPrototypeOf(F);
            };
            return ie(z);
          }
          function ne(z, v) {
            var F = `data-clipboard-${z}`;
            if (v.hasAttribute(F)) {
              return v.getAttribute(F);
            }
          }
          var qe = function (z) {
            Xe(F, z);
            var N = it(F);
            function F(L, M) {
              var ce;
              Te(this, F);
              ce = N.call(this);
              ce.resolveOptions(M);
              ce.listenClick(L);
              return ce;
            }
            Be(F, [{
              key: "resolveOptions",
              value: function () {
                var q = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
                this.action = typeof q.action === "function" ? q.action : this.defaultAction;
                this.target = typeof q.target === "function" ? q.target : this.defaultTarget;
                this.text = typeof q.text === "function" ? q.text : this.defaultText;
                this.container = be(q.container) === "object" ? q.container : document.body;
              }
            }, {
              key: "listenClick",
              value: function (M) {
                var ce = this;
                this.listener = y()(M, "click", function (ve) {
                  return ce.onClick(ve);
                });
              }
            }, {
              key: "onClick",
              value: function (M) {
                var ce = M.delegateTarget || M.currentTarget;
                var ve = this.action(ce) || "copy";
                var Ve = Le({
                  action: ve,
                  container: this.container,
                  target: this.target(ce),
                  text: this.text(ce)
                });
                this.emit(Ve ? "success" : "error", {
                  action: ve,
                  text: Ve,
                  trigger: ce,
                  clearSelection: function () {
                    if (ce) {
                      ce.focus();
                    }
                    window.getSelection().removeAllRanges();
                  }
                });
              }
            }, {
              key: "defaultAction",
              value: function (M) {
                return ne("action", M);
              }
            }, {
              key: "defaultTarget",
              value: function (M) {
                var ce = ne("target", M);
                if (ce) {
                  return document.querySelector(ce);
                }
              }
            }, {
              key: "defaultText",
              value: function (M) {
                return ne("text", M);
              }
            }, {
              key: "destroy",
              value: function () {
                this.listener.destroy();
              }
            }], [{
              key: "copy",
              value: function (M) {
                var ce = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {
                  container: document.body
                };
                return le(M, ce);
              }
            }, {
              key: "cut",
              value: function (M) {
                return j(M);
              }
            }, {
              key: "isSupported",
              value: function () {
                var q = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ["copy", "cut"];
                var ce = typeof q === "string" ? [q] : q;
                var ve = !!document.queryCommandSupported;
                ce.forEach(function (Ve) {
                  ve = ve && !!document.queryCommandSupported(Ve);
                });
                return ve;
              }
            }]);
            return F;
          }(f());
          var Oe = qe;
        },
        828: function (i) {
          var a = 9;
          if (typeof Element !== "undefined" && !Element.prototype.matches) {
            var l = Element.prototype;
            l.matches = l.matchesSelector || l.mozMatchesSelector || l.msMatchesSelector || l.oMatchesSelector || l.webkitMatchesSelector;
          }
          function f(m, y) {
            for (; m && m.nodeType !== a;) {
              if (typeof m.matches === "function" && m.matches(y)) {
                return m;
              }
              m = m.parentNode;
            }
          }
          i.exports = f;
        },
        438: function (i, r, a) {
          var l = a(828);
          function f(g, A, U, H, j) {
            var Z = y.apply(this, arguments);
            g.addEventListener(U, Z, j);
            return {
              destroy: function () {
                g.removeEventListener(U, Z, j);
              }
            };
          }
          function m(g, A, U, H, j) {
            if (typeof g.addEventListener === "function") {
              return f.apply(null, arguments);
            } else if (typeof U === "function") {
              return f.bind(null, document).apply(null, arguments);
            } else {
              if (typeof g == "string") {
                g = document.querySelectorAll(g);
              }
              return Array.prototype.map.call(g, function (Z) {
                return f(Z, A, U, H, j);
              });
            }
          }
          function y(g, A, U, H) {
            return function (j) {
              j.delegateTarget = l(j.target, A);
              if (j.delegateTarget) {
                H.call(g, j);
              }
            };
          }
          i.exports = m;
        },
        879: function (i, r) {
          r.node = function (l) {
            return l !== undefined && l instanceof HTMLElement && l.nodeType === 1;
          };
          r.nodeList = function (l) {
            var m = Object.prototype.toString.call(l);
            return l !== undefined && (m === "[object NodeList]" || m === "[object HTMLCollection]") && "length" in l && (l.length === 0 || r.node(l[0]));
          };
          r.string = function (l) {
            return typeof l === "string" || l instanceof String;
          };
          r.fn = function (l) {
            var m = Object.prototype.toString.call(l);
            return m === "[object Function]";
          };
        },
        370: function (i, r, a) {
          var f = a(879);
          var m = a(438);
          function y(H, j, V) {
            if (!H && !j && !V) {
              throw new Error("Missing required arguments");
            }
            if (!f.string(j)) {
              throw new TypeError("Second argument must be a String");
            }
            if (!f.fn(V)) {
              throw new TypeError("Third argument must be a Function");
            }
            if (f.node(H)) {
              return g(H, j, V);
            }
            if (f.nodeList(H)) {
              return A(H, j, V);
            }
            if (f.string(H)) {
              return U(H, j, V);
            }
            throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList");
          }
          function g(H, j, V) {
            H.addEventListener(j, V);
            return {
              destroy: function () {
                H.removeEventListener(j, V);
              }
            };
          }
          function A(H, j, V) {
            Array.prototype.forEach.call(H, function (oe) {
              oe.addEventListener(j, V);
            });
            return {
              destroy: function () {
                Array.prototype.forEach.call(H, function (le) {
                  le.removeEventListener(j, V);
                });
              }
            };
          }
          function U(H, j, V) {
            return m(document.body, H, j, V);
          }
          i.exports = y;
        },
        817: function (i) {
          function a(l) {
            var m;
            if (l.nodeName === "SELECT") {
              l.focus();
              m = l.value;
            } else if (l.nodeName === "INPUT" || l.nodeName === "TEXTAREA") {
              var y = l.hasAttribute("readonly");
              if (!y) {
                l.setAttribute("readonly", "");
              }
              l.select();
              l.setSelectionRange(0, l.value.length);
              if (!y) {
                l.removeAttribute("readonly");
              }
              m = l.value;
            } else {
              if (l.hasAttribute("contenteditable")) {
                l.focus();
              }
              var g = window.getSelection();
              var A = document.createRange();
              A.selectNodeContents(l);
              g.removeAllRanges();
              g.addRange(A);
              m = g.toString();
            }
            return m;
          }
          i.exports = a;
        },
        279: function (i) {
          function a() {}
          a.prototype = {
            on: function (l, f, m) {
              var g = this.e || (this.e = {});
              (g[l] || (g[l] = [])).push({
                fn: f,
                ctx: m
              });
              return this;
            },
            once: function (l, f, m) {
              var y = this;
              function g() {
                y.off(l, g);
                f.apply(m, arguments);
              }
              g._ = f;
              return this.on(l, g, m);
            },
            emit: function (l) {
              var m = [].slice.call(arguments, 1);
              var y = ((this.e || (this.e = {}))[l] || []).slice();
              var g = 0;
              var A = y.length;
              for (g; g < A; g++) {
                y[g].fn.apply(y[g].ctx, m);
              }
              return this;
            },
            off: function (l, f) {
              var y = this.e || (this.e = {});
              var g = y[l];
              var A = [];
              if (g && f) {
                for (var U = 0, H = g.length; U < H; U++) {
                  if (g[U].fn !== f && g[U].fn._ !== f) {
                    A.push(g[U]);
                  }
                }
              }
              if (A.length) {
                y[l] = A;
              } else {
                delete y[l];
              }
              return this;
            }
          };
          i.exports = a;
          i.exports.TinyEmitter = a;
        }
      };
      var x = {};
      function o(i) {
        if (x[i]) {
          return x[i].exports;
        }
        var a = x[i] = {
          exports: {}
        };
        n[i](a, a.exports, o);
        return a.exports;
      }
      (function () {
        o.n = function (i) {
          var r = i && i.__esModule ? function () {
            return i.default;
          } : function () {
            return i;
          };
          o.d(r, {
            a: r
          });
          return r;
        };
      })();
      (function () {
        o.d = function (i, r) {
          for (var l in r) {
            if (o.o(r, l) && !o.o(i, l)) {
              Object.defineProperty(i, l, {
                enumerable: true,
                get: r[l]
              });
            }
          }
        };
      })();
      (function () {
        o.o = function (i, r) {
          return Object.prototype.hasOwnProperty.call(i, r);
        };
      })();
      return o(686);
    }().default;
  });
})(Xo);
const ks = os(Xo.exports);
const er = "3.7.5";
const Cs = er;
const Ss = typeof atob === "function";
const Rs = typeof btoa === "function";
const en = typeof Buffer === "function";
const Jx = typeof TextDecoder === "function" ? new TextDecoder() : undefined;
const Gx = typeof TextEncoder == "function" ? new TextEncoder() : undefined;
const Es = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
const ln = Array.prototype.slice.call(Es);
const Fn = (e => {
  let t = {};
  e.forEach((n, x) => t[n] = x);
  return t;
})(ln);
const Is = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;
const Ce = String.fromCharCode.bind(String);
const Yx = typeof Uint8Array.from == "function" ? Uint8Array.from.bind(Uint8Array) : e => new Uint8Array(Array.prototype.slice.call(e, 0));
const tr = e => e.replace(/=/g, "").replace(/[+\/]/g, t => t == "+" ? "-" : "_");
const nr = e => e.replace(/[^A-Za-z0-9\+\/]/g, "");
const xr = e => {
  let n;
  let x;
  let o;
  let i;
  let r = "";
  const a = e.length % 3;
  for (let l = 0; l < e.length;) {
    if ((x = e.charCodeAt(l++)) > 255 || (o = e.charCodeAt(l++)) > 255 || (i = e.charCodeAt(l++)) > 255) {
      throw new TypeError("invalid character found");
    }
    n = x << 16 | o << 8 | i;
    r += ln[n >> 18 & 63] + ln[n >> 12 & 63] + ln[n >> 6 & 63] + ln[n & 63];
  }
  if (a) {
    return r.slice(0, a - 3) + "===".substring(a);
  } else {
    return r;
  }
};
const tx = Rs ? e => btoa(e) : en ? e => Buffer.from(e, "binary").toString("base64") : xr;
const N0 = en ? e => Buffer.from(e).toString("base64") : e => {
  const n = 4096;
  let x = [];
  for (let o = 0, i = e.length; o < i; o += n) {
    x.push(Ce.apply(null, e.subarray(o, o + n)));
  }
  return tx(x.join(""));
};
const qn = (e, t = false) => t ? tr(N0(e)) : N0(e);
const As = e => {
  if (e.length < 2) {
    var n = e.charCodeAt(0);
    if (n < 128) {
      return e;
    } else if (n < 2048) {
      return Ce(n >>> 6 | 192) + Ce(n & 63 | 128);
    } else {
      return Ce(n >>> 12 & 15 | 224) + Ce(n >>> 6 & 63 | 128) + Ce(n & 63 | 128);
    }
  } else {
    var n = 65536 + (e.charCodeAt(0) - 55296) * 1024 + (e.charCodeAt(1) - 56320);
    return Ce(n >>> 18 & 7 | 240) + Ce(n >>> 12 & 63 | 128) + Ce(n >>> 6 & 63 | 128) + Ce(n & 63 | 128);
  }
};
const Ts = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
const or = e => e.replace(Ts, As);
const Zx = en ? e => Buffer.from(e, "utf8").toString("base64") : Gx ? e => N0(Gx.encode(e)) : e => tx(or(e));
const $t = (e, t = false) => t ? tr(Zx(e)) : Zx(e);
const Qx = e => $t(e, true);
const Os = /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g;
const Ps = e => {
  switch (e.length) {
    case 4:
      var n = (e.charCodeAt(0) & 7) << 18 | (e.charCodeAt(1) & 63) << 12 | (e.charCodeAt(2) & 63) << 6 | e.charCodeAt(3) & 63;
      var x = n - 65536;
      return Ce((x >>> 10) + 55296) + Ce((x & 1023) + 56320);
    case 3:
      return Ce((e.charCodeAt(0) & 15) << 12 | (e.charCodeAt(1) & 63) << 6 | e.charCodeAt(2) & 63);
    default:
      return Ce((e.charCodeAt(0) & 31) << 6 | e.charCodeAt(1) & 63);
  }
};
const rr = e => e.replace(Os, Ps);
const ir = e => {
  e = e.replace(/\s+/g, "");
  if (!Is.test(e)) {
    throw new TypeError("malformed base64.");
  }
  e += "==".slice(2 - (e.length & 3));
  let n;
  let x = "";
  let o;
  let i;
  for (let r = 0; r < e.length;) {
    n = Fn[e.charAt(r++)] << 18 | Fn[e.charAt(r++)] << 12 | (o = Fn[e.charAt(r++)]) << 6 | (i = Fn[e.charAt(r++)]);
    x += o === 64 ? Ce(n >> 16 & 255) : i === 64 ? Ce(n >> 16 & 255, n >> 8 & 255) : Ce(n >> 16 & 255, n >> 8 & 255, n & 255);
  }
  return x;
};
const nx = Ss ? e => atob(nr(e)) : en ? e => Buffer.from(e, "base64").toString("binary") : ir;
const cr = en ? e => Yx(Buffer.from(e, "base64")) : e => Yx(nx(e).split("").map(t => t.charCodeAt(0)));
const sr = e => cr(ar(e));
const js = en ? e => Buffer.from(e, "base64").toString("utf8") : Jx ? e => Jx.decode(cr(e)) : e => rr(nx(e));
const ar = e => nr(e.replace(/[-_]/g, t => t == "-" ? "+" : "/"));
const F0 = e => js(ar(e));
const Us = e => {
  if (typeof e !== "string") {
    return false;
  }
  const n = e.replace(/\s+/g, "").replace(/={0,2}$/, "");
  return !/[^\s0-9a-zA-Z\+/]/.test(n) || !/[^\s0-9a-zA-Z\-_]/.test(n);
};
const lr = e => ({
  value: e,
  enumerable: false,
  writable: true,
  configurable: true
});
const ur = function () {
  const t = (n, x) => Object.defineProperty(String.prototype, n, lr(x));
  t("fromBase64", function () {
    return F0(this);
  });
  t("toBase64", function (n) {
    return $t(this, n);
  });
  t("toBase64URI", function () {
    return $t(this, true);
  });
  t("toBase64URL", function () {
    return $t(this, true);
  });
  t("toUint8Array", function () {
    return sr(this);
  });
};
const fr = function () {
  const t = (n, x) => Object.defineProperty(Uint8Array.prototype, n, lr(x));
  t("toBase64", function (n) {
    return qn(this, n);
  });
  t("toBase64URI", function () {
    return qn(this, true);
  });
  t("toBase64URL", function () {
    return qn(this, true);
  });
};
const Ns = () => {
  ur();
  fr();
};
const Fs = {
  version: er,
  VERSION: Cs,
  atob: nx,
  atobPolyfill: ir,
  btoa: tx,
  btoaPolyfill: xr,
  fromBase64: F0,
  toBase64: $t,
  encode: $t,
  encodeURI: Qx,
  encodeURL: Qx,
  utob: or,
  btou: rr,
  decode: F0,
  isValid: Us,
  fromUint8Array: qn,
  toUint8Array: sr,
  extendString: ur,
  extendUint8Array: fr,
  extendBuiltins: Ns
};
const Ms = {
  class: "m-t[0px]"
};
const Bs = ["src"];
const Vs = {
  class: "m-t[200px]"
};
const zs = ["src"];
const Ls = {
  key: 0,
  class: "container flex flex-col items-center"
};
const qs = {
  class: "box-border relative max-w-[750px] w-full flex flex-col overflow-hidden"
};
const Ds = ["src"];
const Hs = ["src"];
const _s = {
  class: "text-center color-#666666 text-20px pt-10 pb-2"
};
const $s = {
  key: 0,
  class: "color-#666666 text-center mb-4"
};
const Ws = ["src"];
const Ks = {
  key: 0,
  id: "popup",
  class: "z-2 fixed left-0 right-0 bottom-0 w-100% h-100%",
  style: {
    "background-color": "rgba(0, 0, 0, 0.6)"
  }
};
const Js = J("img", {
  class: "w-20px",
  src: "https://cdn.tianjinzhaofa.cn/web/image/Frame.png",
  alt: ""
}, null, -1);
const Gs = [Js];
const Ys = wc("<input class=\"w-93% h-50px bg-#F2F5F9 mt-10px px-12px text-16px border-none\" type=\"text\" id=\"phone\" name=\"phone\" pattern=\"[0-9]{11}\" placeholder=\"请输入手机号\" maxlength=\"11\" required><span id=\"phoneError\" class=\"mt-10px text-13px color-red\"></span><div class=\"flex items-center mt-16px\"><input type=\"radio\" id=\"agree\" name=\"agreement\"><span class=\"text-14px color-#666666 mt-3px\">我已经阅读并同意<a href=\"https://cdn.tianjinzhaofa.cn/web/image/privacy.html\">《个人信息授权及隐私政策》</a></span></div>", 3);
const Zs = {
  key: 1,
  class: "container mx-auto max-w-[750px]"
};
const Qs = {
  class: "relative box-border flex items-center w-full p-4 bg-white text-sm mb-2"
};
const Xs = {
  class: "flex flex-col"
};
const ea = {
  key: 0
};
const ta = {
  class: "flex"
};
const na = ["href"];
const xa = ["href"];
const oa = ["href"];
const ra = J("img", {
  src: "https://cdn.tianjinzhaofa.cn/portal/common/tousu.png",
  class: "w-[3.5rem]",
  alt: "",
  srcset: ""
}, null, -1);
const ia = [ra];
const ca = {
  class: "box-border relative"
};
const sa = ["src"];
const aa = ["src"];
const la = {
  class: "flex flex-col items-center z-2 relative pb-8"
};
const ua = ["src"];
const fa = {
  class: "flex flex-col text-center text-white my-2 leading-9 w-90%"
};
const da = {
  class: "text-[26px] mt-2"
};
const pa = {
  class: "text-[22px]"
};
const ha = {
  class: "text-[18px] mb-2"
};
const ma = ["href"];
const ga = ["src"];
const va = {
  class: "text-center py-4 text-gray-500 relative z-3 bg-#ffffff"
};
const ba = {
  class: "text-xs mb-2 px-12"
};
const ya = {
  class: "text-20px mb-2"
};
const wa = {
  key: 0,
  class: "mb-2"
};
const ka = J("div", {
  id: "qa-web"
}, null, -1);
const Xx = "u2";
const eo = "v.u2.25.0626-1533";
const Ca = "https://api.tianjinzhaofa.cn/portal-conf";
const Sa = {
  __name: "App",
  setup(e) {
    const n = new URLSearchParams(window.location.search);
    const x = n.get("tfchannel");
    const o = n.get("debug");
    const i = n.get("adid");
    const r = n.get("creativeid");
    const a = n.get("creativetype");
    const l = n.get("pid");
    const f = n.get("bd_vid");
    const m = n.get("pageId");
    const y = n.get("lbid");
    const g = n.get("bookId");
    const A = n.get("chapterId");
    const U = n.get("mark_id");
    const H = U || f || n.get("clickid");
    let j = Qo("tid");
    const V = n.get("rd_type");
    const Z = n.get("u_aid");
    const oe = n.get("u_did");
    const le = n.get("u_cid");
    const $ = n.get("u_vid");
    const he = n.get("u_pid");
    const Le = n.get("u_referrer");
    const be = n.get("u_channel");
    const Te = n.get("account_id");
    const He = n.get("channel");
    const Be = n.get("oaidmd5");
    const Xe = n.get("imeimd5");
    const _e = n.get("back_name");
    const it = n.get("back_url");
    const ut = n.get("back_pkg");
    let ft = n.get("cos_pop");
    const me = n.get("csite");
    const ie = n.get("union_site");
    const ne = n.get("mgc_cb");
    const qe = n.get("pos");
    const Oe = Y(false);
    const z = Y(false);
    const v = Y(false);
    const N = Y("");
    const F = Y("");
    const L = Y("");
    const M = Y("");
    const q = Y("");
    const ce = Y("");
    const ve = Y("");
    const Ve = Y("");
    const dt = Y("");
    const p = Y("");
    const h = Y("");
    const b = Y("");
    const w = Y("");
    const k = Y("");
    const C = Y("");
    const I = Y("");
    const S = Y([]);
    const R = Y("");
    const T = Y(false);
    const E = Y("");
    const D = Y(true);
    const O = Y("");
    const _ = Y("");
    const W = Y(false);
    const ee = Y("");
    const se = Y("");
    const te = Y("");
    const ge = Y("");
    const K = Y("");
    const we = Y("");
    const Pt = Y("");
    const $e = Y("");
    const jt = Y(1);
    const Pe = Y(false);
    const Ue = Y(false);
    const Ut = Y("");
    try {
      if (j) {
        if (j.indexOf("%") == -1) {
          j = encodeURIComponent(j);
        }
        j = Fs.toBase64(j);
      }
    } catch {}
    const xx = ms();
    let Nt = Y(0);
    const ox = Y("");
    const rx = document.visibilityState;
    let Q = {};
    let tn = 0;
    let bn = 8;
    const ue = dn({
      QUICK_APP_ST_CHANNEL: l,
      topic_id: "b8a21ad6-ea87-45fa-b289-549e2fa68f16",
      landVersion: Xx,
      portalVersion: eo,
      hapType: "dp-auto",
      adid: i,
      creativeid: r,
      creativetype: a,
      clickid: H,
      uuid: xx,
      pkg: "",
      pid: l,
      channel: "",
      tid: j,
      lbid: y,
      pageid: m,
      tfchannel: x,
      rd_type: V,
      u_aid: Z,
      u_did: oe,
      u_cid: le,
      u_vid: $,
      u_pid: he,
      bd_vid: f,
      u_referrer: Le,
      u_channel: be,
      account_id: Te,
      oaidmd5: Be,
      imeimd5: Xe,
      landPopIndex: jt,
      csite: me,
      union_site: ie,
      visbState: rx,
      back_name: _e,
      back_url: it,
      back_pkg: ut,
      cos_pop: ft,
      vid: l,
      mgc_cb: ne,
      pos: qe
    });
    const Ee = dn({
      pid: l,
      QUICK_APP_ST_CHANNEL: l,
      landVersion: Xx,
      portalVersion: eo,
      hapType: "dp-auto",
      clickid: H,
      adid: i,
      creativeid: r,
      creativetype: a,
      channel: "",
      uuid: xx,
      tid: j,
      lbid: y,
      pageid: m,
      tfchannel: x,
      rd_type: V,
      u_aid: Z,
      u_did: oe,
      u_cid: le,
      u_vid: $,
      u_pid: he,
      bd_vid: f,
      u_referrer: Le,
      u_channel: be,
      account_id: Te,
      oaidmd5: Be,
      imeimd5: Xe,
      bookId: g,
      chapterId: A,
      landPopIndex: jt,
      csite: me,
      union_site: ie,
      visbState: rx,
      back_name: _e,
      back_url: it,
      back_pkg: ut,
      vid: l,
      mgc_cb: ne,
      pos: qe,
      change_pkg_type: ""
    });
    let Ft = "";
    let Mt = [];
    Fo(async () => {
      try {
        const u = await fetch(Ca + "?pid=" + l + "&channel=" + He + "&account_id=" + Te + "&oaidmd5=" + Be + "&clickid=" + H);
        const {
          data: d
        } = await u.json();
        Q = d;
        Pt.value = d.clipboard;
        z.value = d.isAutomatic;
        Oe.value = d.isComplained;
        v.value = d.isCookie;
        N.value = d.packageName;
        F.value = d.appName;
        L.value = d.appVersion;
        M.value = d.updatedOn;
        q.value = d.companyName;
        ce.value = d.agreementUrl;
        ve.value = d.privacyUrl;
        Ve.value = d.introduction;
        dt.value = d.logoUrl;
        p.value = d.backgroundTopUrl;
        h.value = d.backgroundBottomUrl;
        b.value = d.screenshotUrl;
        w.value = d.complainButtonUrl;
        K.value = d.brand;
        k.value = zt(d.complainUrl, {
          pid: l,
          package: d.packageName,
          channel: d.channel
        });
        C.value = d.page;
        I.value = d.channel;
        S.value = d.slogan;
        R.value = d.jumpTip;
        T.value = d.isIcpDisplay;
        E.value = d.icpRecord;
        D.value = d.isMetaDataDisplay;
        we.value = d.icpCompanyName;
        Ee.channel = I.value;
        Ee.uuid = ue.uuid;
        Ee.change_pkg_type = d.change_pkg_type;
        ox.value = "#";
        O.value = d.clickToOpenUrl;
        _.value = d.b;
        ue.pkg = d.packageName;
        ue.channel = d.channel;
        $e.value = d.jumpToken;
        ue.mode = d.mode;
        dr();
        Tr();
      } catch (u) {
        console.log("error", u);
      }
    });
    const dr = () => {
      const {
        extraConfig: u
      } = Q;
      if (H && H == "http://ad.partner.gifshow.com/track/activate?callback") {
        let P = ws(location.href);
        Ee.clickid = P;
        ue.clickid = P;
      }
      if (!V) {
        Or();
        yt({
          eventName: "webland_init"
        });
      }
      if (l.includes("auto")) {
        C.value = "index";
        ue.pkg = Q.packageA;
        N.value = Q.packageA;
        console.log("来了哈");
        return;
      }
      if (u) {
        if (typeof u.closeAttr === "number" && u.closeAttr) {
          C.value = "index";
          ue.pkg = null;
          return;
        }
        if (typeof u.backRetryCount === "number") {
          bn = u.backRetryCount;
        }
        if (K.value == "xiaomi") {
          bn = 5;
        }
        if (K.value == "honor") {
          bn = 0;
        }
        try {
          if (u.backGlobal) {
            Mt = JSON.parse(u.backGlobal);
          }
        } catch {}
      }
      if (!z.value) {
        return;
      }
      if (i && (i.includes("_") || i.includes("$") || i.includes("{"))) {
        console.log("__AID__", false);
        C.value = "index";
        ue.pkg = Q.packageA;
        N.value = Q.packageA;
        return;
      }
      if (I.value == "oppo" && oe && oe == "$ad$") {
        return;
      }
      if (u && u.specialPop) {
        Ut.value = u.specialPop;
      }
      if (!z.value && !o || ft == 1) {
        W.value = false;
      } else if (!Wx("RedirectPop")) {
        wn();
      }
      Sr();
      let d = navigator.userAgent.toLowerCase();
      if (u && u.toKkPage && K.value != "huawei" && K.value != "vivo") {
        const P = window.location.search + "&rd_type=st";
        Ft = "https://cdn.wuhanshengwan.cn/portal/core/index.html" + P;
        if (window.Kwai) {
          try {
            if (K.value != "oppo") {
              const pe = "https://cdn.wuhanshengwan.cn/portal/kfc/index.html" + P;
              if (d.includes("nebula")) {
                const We = "#Intent;scheme=ksnebula;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end";
                window.location.href = "intent://webview?url=" + encodeURIComponent(pe) + We;
                return;
              }
              if (d.includes("kswebview")) {
                const We = "#Intent;scheme=kwai;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end";
                window.location.href = "intent://webview?url=" + encodeURIComponent(pe) + We;
                return;
              }
            }
          } catch {}
          let ae = "webview";
          Ft = "https://cdn.wuhanshengwan.cn/portal/core/index.html" + P;
          if (d.includes("nebula")) {
            window.location.href = "ksnebula://" + ae + "?url=" + encodeURIComponent(Ft);
            return;
          }
          if (d.includes("kswebview")) {
            window.location.href = "kwai://" + ae + "?url=" + encodeURIComponent(Ft);
            return;
          }
        } else {
          if (d.includes("kswebview") || d.includes("nebula")) {
            window.location.href = "ksnebula://adwebview?url=" + encodeURIComponent(Ft);
          } else {
            window.location.href = Ft;
          }
          return;
        }
      }
      if (o0()) {
        yt({
          eventName: "webland_auto_frequency"
        });
        return;
      }
      if (Q.universalAdvertisingEnabled) {
        yn();
      }
      if (!l.includes("vvzn")) {
        mr();
      }
      if (u && u.isAllJump) {
        hr();
      }
      bs(() => {
        switch (K.value) {
          case "vivo":
            cx();
            break;
          case "oppo":
            pr();
            kn();
            break;
          case "xiaomi":
            ux();
            kn();
            break;
          case "huawei":
            r0();
            break;
          case "honor":
            r0();
            break;
        }
        if (o) {
          kn();
        }
      }, K.value);
      Ue.value = true;
      i0("dp-auto");
    };
    const ix = () => {
      try {
        const u = (window.navigator.platform || "").toLowerCase();
        return u.includes("win") || u.includes("intel");
      } catch (u) {
        console.error("Error determining platform:", u);
        return false;
      }
    };
    const pr = () => {
      if (!ix() && Q.extraConfig && typeof Q.extraConfig.opBridge === "number") {
        if (Q.extraConfig.opBridge == 1) {
          const u = encodeURIComponent("https://cdn.wuhanshengwan.cn/portal/rand/index.html" + window.location.search + "&rd_type=st");
          window.location.href = "pictorial://pictorial.com/common/webview_activity?url=" + u;
        } else if (Q.extraConfig.opBridge == 2) {
          const u = encodeURIComponent("https://cdn.wuhanshengwan.cn/portal/rand/index.html" + window.location.search + "&rd_type=st");
          window.location.href = "oaps://mk/web?u=" + u;
        }
      }
    };
    const cx = (c = "hib-auto") => {
      if (!ix() && Q.extraConfig && typeof Q.extraConfig.isUseVo === "number" && Q.extraConfig.isUseVo == 1) {
        document.title = "限时优惠";
        const d = encodeURIComponent(JSON.stringify({
          ...Ee,
          hapType: c
        }));
        const P = encodeURIComponent("https://qapp-h5-pre.vivo.com.cn/?packageName=" + ue.pkg + "&path=" + C.value + "&params=" + d);
        window.location.href = "hiboard://vivo.com/oepration?url=" + P;
      }
    };
    const hr = () => {
      try {
        const d = "https://cdn.wuhanshengwan.cn/portal/core/index.html" + window.location.search + "&rd_type=st";
        window.location.href = "ksnebula://yodaweb?url=" + encodeURIComponent(d);
        if (K.value != "oppo") {
          if (K.value != "vivo") {
            if (K.value == "huawei") {
              var u = Ne("gm-auto", true);
              u = encodeURIComponent(u);
              window.location.href = "higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + u + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
            } else if (K.value == "honor") {
              var u = Ne("gm-auto", false);
              u = encodeURIComponent(u);
              window.location.href = "higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + u + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
            }
          }
        }
      } catch (d) {
        console.log("error", d);
      }
    };
    let nn = 0;
    const sx = () => {
      try {
        const {
          extraConfig: u
        } = Q;
        if (u && u.soonAbn) {
          return;
        }
        if (Mt && Array.isArray(Mt) && Mt.length > 0) {
          nn = nn < Mt.length ? nn : 0;
          const d = Mt[nn];
          if (d) {
            nn++;
            console.log("per ...", d.package);
            ue.pkg = d.package;
            N.value = d.package;
            C.value = d.page;
            $e.value = d.jump_token;
            ue.message = "per";
            Ee.message = "per";
          }
        }
      } catch (u) {
        console.error(u);
      }
    };
    const yn = () => {
      if (Q.universalAdvertisingEnabled) {
        let u = Q.huaweiFirst ? "huawei" : K.value;
        let d = lx(u);
        if ((!d || d.length <= 0) && ["huawei", "honor"].includes(K.value)) {
          u = u == "huawei" ? "honor" : "huawei";
          d = lx(u);
          console.log("change-", u);
        }
        if (Array.isArray(d) && d.length > 0) {
          if (tn < d.length) {
            const P = d[tn];
            const ae = P.package.replace(" huawei", "");
            Ee.pid = P.pid;
            ue.pid = P.pid;
            ue.pkg = ae;
            N.value = ae;
            C.value = P.page;
            $e.value = P.jump_token;
            Ee.change_pkg_type = P.change_pkg_type;
            Q.day_start_num = P.day_start_num;
            tn++;
            return true;
          } else {
            console.log("an is none");
            tn = 0;
            yn();
          }
        }
        return false;
      }
    };
    const wn = () => {
      W.value = true;
      const u = Math.floor(Math.random() * 7) + 1;
      jt.value = u;
    };
    let ax = 0;
    const mr = () => {
      try {
        window.history.pushState(null, null, "#");
        window.addEventListener("popstate", function (u) {
          ax++;
          if (ax < 15) {
            wn();
            window.history.pushState(null, null, "#");
          }
        });
      } catch {}
    };
    const lx = c => {
      const d = Q.universalAdvertisingConfig;
      const P = d.find(ae => ae.brand === c);
      if (P) {
        return P.config;
      } else {
        return null;
      }
    };
    const o0 = c => {
      let d = 0;
      if (Q.extraConfig && typeof Q.extraConfig.frequencyTime === "number") {
        d = Q.extraConfig.frequencyTime;
      }
      if (d > 0) {
        const P = Wx("IsFrequency");
        if (P && P == "true") {
          console.log("frequency...", Oe.value);
          return true;
        }
        if (Oe.value) {
          Kx("IsFrequency", true, 172800);
        } else {
          Kx("IsFrequency", true, d);
        }
      }
      return false;
    };
    const kn = c => {
      try {
        Er(c);
        // TOLOOK
        setTimeout(() => {
          gr(c);
          routeToQuickapp("qa-btn-" + ue.pkg + Nt.value);
        }, 500);
      } catch (d) {
        console.log("qaRouter error", d);
      }
    };
    const gr = c => {
      try {
        if (K.value == "oppo") {
          const px = fx(c || "tzu-auto");
          let s0 = new URLSearchParams(px).toString();
          s0 = encodeURIComponent(encodeURIComponent(s0));
          var d = "http://www.tzujian.com";
          var P = d + "/?i=" + N.value + "&p=/" + C.value + "&random=0.551192603796344&a=" + s0;
          var ae = document.getElementsByTagName("qa-router-button")[0];
          var pe = "https://api.tianjinzhaofa.cn/redirect?jumpUrl=";
          P = pe + encodeURIComponent(P);
          var We = ae.innerHTML.replace("#img#", "<img src=\"" + P + "\" alt=\".\">");
          var wt = "*";
          var on = document.getElementsByClassName("qa-inner-iframe")[0];
          on.contentWindow.postMessage({
            _action: "postBtnInfo",
            innerCode: We
          }, wt);
        }
      } catch {}
    };
    const r0 = (c = 0) => {
      if (Q.extraConfig && Q.extraConfig.backRetryIsPass) {
        c = 300;
      }
      if (Q.extraConfig && typeof Q.extraConfig.isUseHww === "number") {
        if (Q.extraConfig.isUseHww == 1) {
          var d = Ne("hww-auto");
          d = encodeURIComponent(d);
          window.location.href = "hww://www.huawei.com/totemweather?type=1&url=" + d;
        } else if (Q.extraConfig.isUseHww == 3) {
          var P = "hms://hbm.link.cloud.huawei.com/web?url=https://cdn.wuhanshengwan.cn/portal/common/qa/rpm/rpm.html";
          var d = zt(P, {
            pkg: N.value,
            page: C.value,
            hapType: "hms-auto",
            ...Ee
          });
          window.location.href = d;
        } else if (Q.extraConfig.isUseHww == 4) {
          var ae = "https://cdn.wuhanshengwan.cn/portal/common/qa/page/redirect.html?";
          var d = zt(ae, {
            pkg: N.value,
            page: C.value,
            hapType: "seh-auto",
            ...Ee
          });
          var P = "search://com.huawei.search/search?weburl=" + encodeURIComponent(d);
          window.location.href = P;
        } else if (Q.extraConfig.isUseHww == 5) {
          var pe = Ne("gm-auto", true);
          pe = encodeURIComponent(pe);
          window.location.href = "higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + pe + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
        } else if (Q.extraConfig.isUseHww == 88) {
          var We = [];
          var d = encodeURIComponent(Ne("hww-auto", true));
          We.push("hww://www.huawei.com/totemweather?type=1&url=" + d);
          var pe = encodeURIComponent(Ne("gm-auto", true));
          We.push("higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + pe + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}");
          var wt = encodeURIComponent(Ne("hiapp-auto", true));
          We.push("hiapp://com.huawei.appmarket?channelId=1&activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + wt + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}");
          var on = window.location.search + "&rd_type=st";
          on = "https://cdn.wuhanshengwan.cn/portal/core/index.html" + on;
          We.push("kwai://webview?url=" + encodeURIComponent(on));
          We.forEach(jr => {
            vr(jr);
          });
        }
      } else {
        var wt = Ne("hiapp-auto", true);
        wt = encodeURIComponent(wt);
        window.location.href = "hiapp://com.huawei.appmarket?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + wt + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
      }
    };
    const vr = c => {
      var d = document.createElement("iframe");
      d.src = c;
      d.style.width = "1px";
      d.style.height = "1px";
      document.body.appendChild(d);
    };
    const ux = c => {
      br(c);
      let d = Ne(c || "src-auto");
      d += "&intent=2&__SRC__=" + encodeURIComponent(JSON.stringify({
        packageName: "com.android.browser"
      }));
      ge.value = // TOLOOK
        setTimeout(() => {
          window.location.href = d;
          clearTimeout(ge.value);
        }, 1000);
    };
    const br = c => {
      let d = Ne(c || "mui-auto");
      d = d.replace("hap://app/", "");
      d += "&intent=2&__SRC__=" + encodeURIComponent(JSON.stringify({
        packageName: "com.android.browser"
      }));
      window.location.href = "com.miui.hybrid://hybrid.xiaomi.com/app/" + d;
    };
    const i0 = (c = "dp-auto") => {
      try {
        if (K.value == "xiaomi") {
          return;
        }
        let d = 10;
        if (K.value == "huawei") {
          d = 300;
          location.href = Ne(c, true);
        }
        ee.value = // TOLOOK
          setTimeout(() => {
            location.href = Ne(c);
            clearTimeout(ee.value);
          }, d);
      } catch {}
    };
    const yr = () => {
      if (R.value == "课程预约页-支付") {
        window.location.href = location.origin + "/whhsj/page/index.html" + location.search + "#/index";
      } else if (R.value == "电商") {
        if (Q.logoUrl) {
          window.location.href = Q.logoUrl;
          return;
        }
        window.location.href = "https://mobile.yangkeduo.com/goods.html?ps=1jiBTfIkhR";
      } else {
        Cn.value = true;
      }
    };
    const c0 = () => {
      Sn();
      if (!Ue.value && Q.introduction && Q.introduction.includes("://")) {
        location.href = Q.introduction;
        return;
      }
      if (W.value) {
        wn();
      }
      if (o0()) {
        yt({
          eventName: "webland_auto_frequency"
        });
        return;
      }
      yt({
        eventName: "webland_bt_click"
      });
      i0("dp-click");
      if (K.value == "huawei" || K.value == "honor") {
        r0(0);
      }
    };
    const fx = c => {
      return Object.fromEntries(Object.entries({
        ...Ee,
        hapType: c
      }).filter(([P, ae]) => ae !== null));
    };
    const Ne = (c, u) => {
      let P = "hap://app/";
      if (u && K.value == "huawei") {
        P = "hwfastapp://";
        var ae = zt("" + P + ue.pkg + "/" + C.value, {
          ...Ee,
          hapType: c
        });
        let pe = ["com.whxfy.zhanyuebk", "com.whxfy.zhiyixm", "com.bjdlh.deyuebt", "com.indigo.novel", "com.tjmms.zhishihg", "com.whlq.qqiaoxzs", "com.bjjcy.yybqbao", "com.tjawy.yytianqi", "com.whlq.lanyue", "com.bjzjf.jijutool"];
        if (!window.Kwai && K.value == "huawei" && !pe.includes(ue.pkg)) {
          return "hwfastapp://com.tjmms.zhishihg/substitute?startLink=" + encodeURIComponent(ae);
        }
      }
      return zt("" + P + ue.pkg + "/" + C.value, {
        ...Ee,
        hapType: c
      });
    };
    const dx = () => {};
    const wr = () => {
      try {
        if (i) {
          if (i.includes("_") || i.includes("$") || i.includes("{")) {
            return false;
          }
          if (!Ue.value) {
            return false;
          }
        }
      } catch {}
      return true;
    };
    let xn = 0;
    const kr = () => {
      const {
        extraConfig: u
      } = Q;
      try {
        let d = 0;
        if (u && typeof u.hddNum === "number") {
          d = u.hddNum;
        }
        if (K.value == "honor") {
          d = 0;
        }
        if (d > 0) {
          console.log("init", d);
          const P = 10;
          const ae = // TOLOOK
            setInterval(() => {
              xn++;
              if (xn <= d) {
                if (u && u.soonAbn) {
                  yn();
                }
                sx();
                console.log("deal task", xn);
                Ee.retryCount = xn * P;
                ue.retryCount = xn * P;
                Ee.visbState = document.visibilityState;
                ue.visbState = document.visibilityState;
                Cr();
              } else {
                clearInterval(ae);
              }
            }, P * 1000);
        }
      } catch (d) {
        console.error(d);
      }
    };
    const Cr = () => {
      try {
        console.log(tn, Ne("hidde-auto"));
        location.href = Ne("hidde-auto");
      } catch {}
    };
    const Sr = () => {
      gs(c => {
        if (c) {
          Pe.value = false;
          clearTimeout(ee.value);
          clearTimeout(se.value);
          clearTimeout(te.value);
          clearTimeout(ge.value);
          if (K.value != "vivo") {
            kr();
          }
        } else if (wr() && ft != 1) {
          if (o0(true)) {
            yt({
              eventName: "webland_retry_auto_frequency"
            });
            return;
          }
          wn();
          if (Q.universalAdvertisingEnabled) {
            yn();
          }
          sx();
          if (Nt.value < bn) {
            Nt.value++;
            Ee.retryCount = Nt.value;
            ue.retryCount = Nt.value;
            Rr();
          } else {
            vs(Q.extraConfig);
          }
        }
      });
    };
    const Rr = () => {
      if (K.value == "oppo" || K.value == "vivo" || K.value == "xiaomi") {
        sessionStorage.setItem("qa_btn_call_count", 0);
        sessionStorage.removeItem("qa_btn_session_id");
        if (K.value == "xiaomi") {
          ux("mui-retry-auto");
        }
        if (K.value == "vivo") {
          cx("hib-retry-auto");
        }
        if (K.value == "oppo") {
          kn("iframe-retry-auto");
        }
      }
      i0("retry-auto");
    };
    const Er = c => {
      const d = fx(c || "iframe-auto");
      var P = "qa-btn-" + ue.pkg + Nt.value;
      var ae = "\n\t\t\t<qa-router-button id=\"my-router-btn\" data-key=" + P + " data-package-name=" + ue.pkg + " data-page=" + C.value + "\n\t\t\t\tdata-params='" + (d && JSON.stringify(d)) + `' data-design-params='{"fontSize": 16,"designWidth": 1080}'
\t\t\t\tdata-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
\t\t\t\tdata-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
\t\t\t\tdata-jump-token=` + ($e.value || "") + `
\t\t\t\t>
\t\t\t\t<templates>
\t\t\t\t\t<div class="btn-box">
              <div style="display:none">#img#</div>
              <span id="btn" class="btn an_scale" style="background: linear-gradient(90deg, rgb(236, 85, 40) 0%, rgb(255, 112, 112) 100%);">点击进入</span>
\t\t\t\t\t</div>
\t\t\t\t</templates>
\t\t\t\t<styles>
\t\t\t\t\t.btn-box{
\t\t\t\t\t  width:100%;
\t\t\t\t\t}
\t\t\t\t\t.btn {
\t\t\t\t\tdisplay:block;
\t\t\t\t\twidth:80%;
          height: 52px;
          line-height: 52px;
\t\t\t\t\tmargin:0 auto;
          border-radius: 90px;
          text-align: center;
          font-size: 20px;
          color: #ffffff;
\t\t\t\t\t}
\t\t\t\t\t.an_scale {
\t\t\t\t\tanimation-name: Scale;
\t\t\t\t\tanimation-iteration-count: infinite;
\t\t\t\t\tanimation-duration: 1500ms;
\t\t\t\t\tanimation-fill-mode: none;
\t\t\t\t\tanimation-timing-function: linear;
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t@keyframes Scale {
\t\t\t\t\t0% {
\t\t\t\t\ttransform: scale(1);
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t50% {
\t\t\t\t\ttransform: scale(1.1);
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t100% {
\t\t\t\t\ttransform: scale(1);
\t\t\t\t\t}
\t\t\t\t\t}
\t\t\t\t</styles>
\t\t\t</qa-router-button>
\t\t`;
      document.getElementById("qa-web").innerHTML = ae;
    };
    const Cn = Y(false);
    const Ir = () => {
      var u = document.getElementById("phone");
      var d = document.getElementById("agree");
      if (!d.checked) {
        alert("隐私协议未同意哦！");
      } else {
        var P = /^1[3-9]\d{9}$/;
        if (P.test(u.value)) {
          var ae = location.origin + "/gluttony/abc/index.html" + location.search;
          yt({
            eventName: "eduReservation",
            detail: u.value,
            message: ae
          });
          phoneError.textContent = "";
          console.log("手机号验证通过");
          Cn.value = false;
          alert("恭喜您，报名成功，审核通过后会有老师联系您！");
          u.value = "";
          d.checked = "";
        } else {
          phoneError.textContent = "手机号格式不正确";
        }
      }
    };
    const Ar = () => {
      Cn.value = false;
    };
    const Sn = () => {
      try {
        if (Pt.value) {
          ks.copy(Pt.value);
        }
      } catch (u) {
        console.warn(u);
      }
    };
    const Tr = () => {
      const u = document.getElementsByTagName("body")[0];
      u.addEventListener("touchstart", Sn);
      u.addEventListener("touchend", Sn);
      u.addEventListener("scroll", Sn);
    };
    const yt = c => {
      const d = {
        ...ue,
        ...c,
        eventType: "webland_view",
        pageName: "webland_page",
        brand: K.value,
        jumpToken: $e.value,
        isAutomatic: z.value,
        isComplained: Oe.value,
        host: window.location.host,
        day_start_num: Q.day_start_num
      };
      const P = zt("https://ap-nanjing.cls.tencentcs.com/track", d);
      const ae = new Image();
      ae.src = P;
    };
    const Or = () => {
      try {
        if (l.includes("_oc")) {
          const u = window.sessionStorage.setItem;
          window.sessionStorage.setItem = function (d, P) {
            if (d == "bridge" && P) {
              P = JSON.parse(P);
              yt({
                eventName: "webland_ockey_init",
                oc_key: d,
                oc_obj: JSON.stringify(P),
                oc_name: P.innerAppName || P.appName
              });
              u.call(this, d, P);
            }
          };
        }
      } catch {}
    };
    const Pr = () => {
      const u = "https://cdn.tianjinzhaofa.cn/web/complain/index.html";
      location.href = u + "?pid=" + l + "&package=" + N.value + "&channel=" + I.value;
    };
    return (c, u) => {
      tt();
      return ct(nt, null, [W.value && Ut.value ? (tt(), ct("div", {
        key: 0,
        onClick: c0,
        style: {
          background: "rgba(0, 0, 0, 1)"
        },
        class: "box-border flex flex-col w-full mx-auto w-[750px] flex flex-col items-center h-screen mx-auto fixed z-150 top-0 left-0"
      }, [J("div", Ms, [J("img", {
        src: "https://cdn.tianjinzhaofa.cn/portal/common/tip/specialPop/" + Ut.value + "/" + ue.landPopIndex + ".webp",
        width: "100%"
      }, null, 8, Bs)])])) : W.value ? (tt(), ct("div", {
        key: 1,
        onClick: c0,
        style: {
          background: "rgba(0, 0, 0, 1)"
        },
        class: "box-border flex flex-col w-full mx-auto w-[750px] flex flex-col items-center h-screen mx-auto absolute z-100 top-0 left-0"
      }, [J("div", Vs, [J("img", {
        src: "https://cdn.tianjinzhaofa.cn/portal/common/tip/" + ue.landPopIndex + ".webp",
        width: "310"
      }, null, 8, zs)])])) : Bt("", true), J("div", null, [R.value && ["课程预约页", "课程预约页-支付", "天天旅游咨询", "电商"].includes(R.value) ? (tt(), ct("div", Ls, [J("div", qs, [J("img", {
        src: p.value,
        alt: "",
        class: "w-full"
      }, null, 8, Ds), J("img", {
        onLoad: dx,
        onClick: u[0] || (u[0] = (...P) => c.startApp && c.startApp(...P)),
        src: dt.value,
        alt: "",
        class: "absolute top-25px left-25px h-40px"
      }, null, 40, Hs), J("div", _s, ze(we.value), 1), T.value ? (tt(), ct("div", $s, ze(E.value), 1)) : Bt("", true), J("a", {
        href: "#",
        onClick: yr,
        class: "fixed bottom-60px opacity-86 mx-12px"
      }, [J("img", {
        class: "w-100%",
        src: O.value,
        alt: ""
      }, null, 8, Ws)])]), Cn.value ? (tt(), ct("div", Ks, [J("div", {
        class: "absolute bottom-0 bg-#ffffff w-100% pt-50px rounded-t-lg"
      }, [J("div", {
        class: "absolute top-20px right-20px",
        onClick: Ar
      }, Gs), J("div", {
        class: "flex flex-col p-20px",
        id: "phoneForm"
      }, [Ys, J("button", {
        onClick: Ir,
        class: "w-100% h-50px bg-#EB6832 text-16px text-center border-0 color-#ffffff mt-16px"
      }, "立即报名")])])])) : Bt("", true)])) : F.value ? (tt(), ct("div", Zs, [J("div", Qs, [J("div", Xs, [J("div", null, ze(I.value === "uc" ? "快" : "") + "应用名称: " + ze(F.value), 1), J("div", null, "应用版本: " + ze(L.value), 1), Pn(J("div", null, "开发者: " + ze(q.value), 513), [[jn, D.value]]), I.value === "uc" ? (tt(), ct("div", ea, "开发者: " + ze(we.value), 1)) : Bt("", true), J("div", ta, [Pn(J("div", null, [J("a", {
        class: "text-blue-500 mr-6 underline-offset-4",
        href: ce.value
      }, "应用权限 >>", 8, na)], 512), [[jn, ce.value]]), Pn(J("div", null, [J("a", {
        class: "text-blue-500 mr-6 underline-offset-4",
        href: ve.value
      }, "隐私政策 >>", 8, xa)], 512), [[jn, ve.value]]), Pn(J("div", null, [J("a", {
        class: "text-blue-500 underline-offset-4",
        href: Ve.value
      }, "功能介绍 >>", 8, oa)], 512), [[jn, Ve.value]])])]), J("div", {
        onClick: Pr,
        class: "absolute top-3 right-3"
      }, ia)]), J("div", ca, [J("img", {
        src: p.value,
        alt: "",
        class: "absolute top-0 w-full"
      }, null, 8, sa), J("img", {
        src: h.value,
        alt: "",
        class: "absolute bottom-0 w-full"
      }, null, 8, aa), J("div", la, [J("img", {
        onLoad: dx,
        onClick: u[1] || (u[1] = (...P) => c.startApp && c.startApp(...P)),
        src: dt.value,
        alt: "",
        height: "44",
        class: "mt-6"
      }, null, 40, ua), J("div", fa, [J("span", da, ze(S.value && S.value[0]), 1), J("span", pa, ze(S.value && S.value[1]), 1), J("span", ha, ze(S.value && S.value[2]), 1)]), J("a", {
        href: ox.value,
        onClick: c0,
        class: "color-#FA602A bg-white no-underline text-24px w-80% mb-8 rounded-90px flex justify-center items-center h-60px"
      }, "立即打开", 8, ma), J("img", {
        src: b.value,
        alt: "",
        class: "w-75% rounded-30px"
      }, null, 8, ga)])]), J("div", va, [J("div", ba, ze(R.value) + ",具体金额以活动规则为准", 1), J("div", ya, ze(we.value), 1), T.value ? (tt(), ct("div", wa, ze(E.value), 1)) : Bt("", true)])])) : Bt("", true), ka])], 64);
    };
  }
};
ns(Sa).mount("#app");