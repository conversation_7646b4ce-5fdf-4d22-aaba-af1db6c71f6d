(() => {
  'use strict';

  var M = {
    dXLRX: function (rb, rI) {
      return rb < rI;
    },
    aKpId: function (rb, rI) {
      return rb != rI;
    },
    hUZwM: "undefined",
    MHsgv: function (rb, rI) {
      return rb === rI;
    },
    lsFFd: function (rb, rI) {
      return rb == rI;
    },
    fuMQn: "number",
    iUrvW: function (rb, rI, rp, rk) {
      return rb(rI, rp, rk);
    },
    mIoyD: "function",
    VpHUw: "next",
    ISqNu: function (rb, rI, rp) {
      return rb(rI, rp);
    },
    Wkdvr: function (rb) {
      return rb();
    },
    BDXtx: function (rb, rI) {
      return rb > rI;
    },
    blMLw: function (rb, rI) {
      return rb * rI;
    },
    pJbWE: function (rb, rI) {
      return rb - rI;
    },
    IQPLt: function (rb, rI) {
      return rb !== rI;
    },
    cJnAk: function (rb, rI, rp, rk, rt) {
      return rb(rI, rp, rk, rt);
    },
    airUC: function (rb, rI) {
      return rb + rI;
    },
    txFgS: "object",
    EELZS: function (rb, rI) {
      return rb(rI);
    },
    mmfes: function (rb, rI, rp) {
      return rb(rI, rp);
    },
    rMtVc: function (rb, rI) {
      return rb + rI;
    },
    YdOsd: function (rb, rI) {
      return rb + rI;
    },
    gaNlN: function (rb, rI) {
      return rb == rI;
    },
    pPiqN: function (rb, rI) {
      return rb(rI);
    },
    TlMcd: function (rb, rI) {
      return rb + rI;
    },
    QTpdL: function (rb, rI) {
      return rb(rI);
    },
    zyVAp: "hidden",
    kCofD: "visibilitychange",
    TJRFE: "mozHidden",
    KjABa: "mozvisibilitychange",
    QkYeH: "msHidden",
    IgTKI: "msvisibilitychange",
    rOPpt: "webkitHidden",
    PlJiO: "webkitvisibilitychange",
    fvWzi: "pkg",
    JAufR: "_oc",
    VkQlC: function (rb, rI) {
      return rb(rI);
    },
    oLsRP: "snssdk35://category_feed?category=novel_channel&force_go_main=1",
    RrDRo: "snssdk143://category_feed?category=novel_channel",
    lKtQb: "_ks",
    YLBAv: function (rb, rI) {
      return rb === rI;
    },
    NmydO: "know",
    bQgAj: "newslite",
    kQLxT: "news_article_lite",
    RcQfJ: "com.ss.android.article.lite",
    JOHZp: "news_article",
    xfFhe: "com.ss.android.article.news",
    GBRyY: "com.ss.android.ugc.aweme.lite",
    BHobX: "com.ss.android.ugc.aweme",
    GCiBx: "clickid=",
    hzngP: function (rb, rI) {
      return rb >= rI;
    },
    KtmUO: "symbol",
    cyknm: "mVBhO",
    KpEgR: "Generator is already running",
    arXYl: function (rb, rI) {
      return rb < rI;
    },
    ZUtpR: function (rb, rI) {
      return rb !== rI;
    },
    ToDfd: "_invoke",
    IXWLm: "GeneratorFunction",
    LfUpX: "@@iterator",
    kIGhi: "@@toStringTag",
    kPGRn: function (rb, rI, rp, rk) {
      return rb(rI, rp, rk);
    },
    ZVLhy: function (rb, rI, rp, rk) {
      return rb(rI, rp, rk);
    },
    ItmPB: function (rb, rI, rp, rk) {
      return rb(rI, rp, rk);
    },
    SFhjE: function (rb, rI, rp, rk) {
      return rb(rI, rp, rk);
    },
    SmPOX: function (rb, rI, rp) {
      return rb(rI, rp);
    },
    zDQYy: "return",
    Eourm: "intenthiapp-auto",
    Qbfxl: "intent://com.huawei.appmarket?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"",
    mTtrL: "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}#Intent;scheme=hiapp;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end",
    ZCoBm: function (rb, rI) {
      return rb(rI);
    },
    yAYYp: "hiapp-auto",
    dMHhK: function (rb, rI) {
      return rb(rI);
    },
    YuuwA: function (rb, rI) {
      return rb == rI;
    },
    InbQK: function (rb, rI) {
      return rb + rI;
    },
    mbBIO: "string",
    NKTff: function (rb, rI) {
      return rb === rI;
    },
    CULni: function (rb, rI) {
      return rb === rI;
    },
    penbn: "Arguments",
    Txlro: function (rb, rI) {
      return rb === rI;
    },
    Hgnmw: "WRJHu",
    kRpAN: function (rb, rI) {
      return rb < rI;
    },
    cpYWP: function (rb, rI) {
      return rb % rI;
    },
    hDyEn: function (rb, rI) {
      return rb(rI);
    },
    RhxsZ: function (rb, rI) {
      return rb(rI);
    },
    TooAB: function (rb, rI) {
      return rb(rI);
    },
    KPyZp: "GgjDp",
    efbRw: function (rb, rI) {
      return rb(rI);
    },
    QwSOj: function (rb, rI) {
      return rb(rI);
    },
    rgUqj: function (rb, rI) {
      return rb(rI);
    },
    UotHI: function (rb, rI) {
      return rb * rI;
    },
    VHcCj: function (rb, rI) {
      return rb + rI;
    },
    drUqf: "com.whxfy.zhiyixm",
    klpDJ: "com.bjdlh.deyuebt",
    JFWNm: "kfc-",
    bxdcz: "hap",
    GibYh: function (rb, rI) {
      return rb - rI;
    },
    gSjFU: function (rb, rI) {
      return rb(rI);
    },
    rfizD: function (rb, rI) {
      return rb(rI);
    },
    sDkNm: "average ...",
    NBloO: "webland_bt_click",
    vgpGt: "OeoAY",
    Cshbq: "https://backend.diamondfavour.net/api/landing/config",
    dErGl: "&account_id=",
    TlpRW: "&oaidmd5=",
    jzIKm: function (rb, rI) {
      return rb || rI;
    },
    OLVln: function (rb, rI) {
      return rb + rI;
    },
    FVwmq: function (rb, rI) {
      return rb + rI;
    },
    fPHmy: function (rb, rI) {
      return rb == rI;
    },
    klXip: "webland_kfc_init",
    hNXpF: function (rb, rI) {
      return rb == rI;
    },
    tUqEa: "honor",
    EQrkQ: "IrBTu",
    GSjli: ".webp",
    lggLb: function (rb, rI) {
      return rb != rI;
    },
    fDhEs: "gWUpe",
    TsNpc: function (rb, rI, rp) {
      return rb(rI, rp);
    },
    SLvXZ: "my-router-btn",
    CeBEH: "\n\t\t\t<qa-router-button id=\"my-router-btn\" data-key=",
    xtsCn: " data-page=",
    hGeet: "https://backend.riverlimittech.net/api/cloud/log?",
    qTRks: function (rb, rI, rp, rk, rt, re, rR, rv) {
      return rb(rI, rp, rk, rt, re, rR, rv);
    },
    BDJkm: "v.k.25.0704-1545",
    wRcpi: function (rb) {
      return rb();
    }
  };
  function D(rb) {
    for (var rI = 1; M.dXLRX(rI, arguments.length); rI++) {
      var rp = arguments[rI];
      for (var rk in rp) {
        rb[rk] = rp[rk];
      }
    }
    return rb;
  }
  var R = function rb(rI, rp) {
    var rk = {
      doWtz: function (re, rR) {
        return re != rR;
      },
      Ldasq: function (re, rR) {
        return M.lsFFd(re, rR);
      },
      qbbsd: M.fuMQn,
      EMShT: function (re, rR) {
        return re * rR;
      },
      CJZtc: function (re, rR) {
        return re + rR;
      },
      HPINB: function (re, rR) {
        return re !== rR;
      },
      nAsIn: function (re, rR, rv, rx) {
        return M.iUrvW(re, rR, rv, rx);
      },
      MWNwD: function (re, rR, rv) {
        return re(rR, rv);
      }
    };
    function rt(re, rR, rv) {
      if (rk.doWtz("undefined", typeof document)) {
        if (rk.Ldasq(rk.qbbsd, typeof (rv = D({}, rp, rv)).expires)) {
          rv.expires = new Date(Date.now() + rk.EMShT(86400000, rv.expires));
        }
        if (rv.expires) {
          rv.expires = rv.expires.toUTCString();
        }
        re = encodeURIComponent(re).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
        var rx = "";
        for (var rG in rv) {
          if (rv[rG]) {
            rx += rk.CJZtc("; ", rG);
            if (rk.HPINB(true, rv[rG])) {
              rx += "=" + rv[rG].split(";")[0];
            }
          }
        }
        return document.cookie = rk.CJZtc(rk.CJZtc(re + "=", rI.write(rR, re)), rx);
      }
    }
    return Object.create({
      set: rt,
      get: function (re) {
        if (M.aKpId(M.hUZwM, typeof document) && (!arguments.length || re)) {
          for (var rR = document.cookie ? document.cookie.split("; ") : [], rv = {}, rx = 0; M.dXLRX(rx, rR.length); rx++) {
            var rG = rR[rx].split("=");
            var rd = rG.slice(1).join("=");
            try {
              var ro = decodeURIComponent(rG[0]);
              rv[ro] = rI.read(rd, ro);
              if (M.MHsgv(re, ro)) {
                break;
              }
            } catch (rK) {}
          }
          if (re) {
            return rv[re];
          } else {
            return rv;
          }
        }
      },
      remove: function (re, rR) {
        rk.nAsIn(rt, re, "", D({}, rR, {
          expires: -1
        }));
      },
      withAttributes: function (re) {
        return rb(this.converter, rk.nAsIn(D, {}, this.attributes, re));
      },
      withConverter: function (re) {
        return rk.MWNwD(rb, rk.nAsIn(D, {}, this.converter, re), this.attributes);
      }
    }, {
      attributes: {
        value: Object.freeze(rp)
      },
      converter: {
        value: Object.freeze(rI)
      }
    });
  }({
    read: function (rI) {
      if (rI[0] === "\"") {
        rI = rI.slice(1, -1);
      }
      return rI.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent);
    },
    write: function (rI) {
      return encodeURIComponent(rI).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g, decodeURIComponent);
    }
  }, {
    path: "/"
  });
  function G(rI) {
    var rp = {
      mvXBp: M.VpHUw,
      bQKeA: function (rk, rt, re) {
        return M.ISqNu(rk, rt, re);
      },
      mCsnB: function (rk, rt) {
        return rk !== rt;
      },
      YZNxD: "vatcb"
    };
    G = typeof Symbol == "function" && M.lsFFd("symbol", typeof Symbol.iterator) ? function (rk) {
      var rt = {
        JOzah: function (re, rR, rv, rx) {
          return re(rR, rv, rx);
        },
        OiVpT: rp.mvXBp,
        BmCHv: function (re, rR, rv) {
          return rp.bQKeA(re, rR, rv);
        },
        aImrg: "return"
      };
      if (rp.mCsnB("BMjeR", rp.YZNxD)) {
        return typeof rk;
      } else if (R) {
        if (X) {
          ru(G, Q, {
            value: J,
            enumerable: !K,
            configurable: !r7,
            writable: !V
          });
        } else {
          J[L] = z;
        }
      } else {
        function rR(rv, rx) {
          rt.JOzah(rR, z, rv, function (rG) {
            return this._invoke(rv, rx, rG);
          });
        }
        rR(rt.OiVpT, 0);
        rt.BmCHv(rR, "throw", 1);
        rR(rt.aImrg, 2);
      }
    } : function (rk) {
      if (rk && M.mIoyD == typeof Symbol && rk.constructor === Symbol && rk !== Symbol.prototype) {
        return "symbol";
      } else {
        return typeof rk;
      }
    };
    return G(rI);
  }
  function K(rI, rp) {
    M.ISqNu(setTimeout, function () {
      M.Wkdvr(rI);
    }, 0);
  }
  function J(rI, rp) {
    var rk;
    var rt = M.BDXtx(arguments.length, 2) && arguments[2] !== undefined ? arguments[2] : null;
    do {
      rk = Math.floor(M.blMLw(Math.random(), M.pJbWE(rp, rI) + 1)) + rI;
    } while (M.IQPLt(null, rt) && rk === rt);
    return rk;
  }
  function L(rI, rp, rk, rt) {
    var re;
    if (Array.isArray(rp)) {
      rp.map(function (rR, rv) {
        if (rk || /\[\]$/.test(rI)) {
          rt(rI, rv);
        } else {
          M.cJnAk(L, M.airUC(M.airUC(rI, "[") + (M.MHsgv(M.txFgS, G(rv)) ? rR : ""), "]"), rv, rk, rt);
        }
      });
    } else if (rk || M.txFgS !== M.EELZS(G, rp)) {
      M.mmfes(rt, rI, rp);
    } else {
      for (re in rp) {
        L(M.rMtVc(M.YdOsd(rI, "["), re) + "]", rp[re], rk, rt);
      }
    }
  }
  function z(rI, rp) {
    var rk = {
      eishU: function (rx, rG) {
        return M.gaNlN(rx, rG);
      },
      gQLNN: M.mIoyD,
      nXyvJ: function (rx, rG) {
        return M.EELZS(rx, rG);
      }
    };
    var rt;
    var re = [];
    function rR(rx, rG) {
      rG = rk.eishU(rk.gQLNN, typeof rG) ? rG() : rk.eishU(null, rG) ? "" : rG;
      re[re.length] = encodeURIComponent(rx) + "=" + rk.nXyvJ(encodeURIComponent, rG);
    }
    if (rp === undefined) {
      rp = false;
    }
    if (Array.isArray(rI)) {
      rI.map(function (rx) {
        rR(rx.name, rx.value);
      });
    } else if (M.MHsgv(M.txFgS, M.pPiqN(G, rI))) {
      for (var rv in rI) {
        rR(rv, rI[rv]);
      }
    } else {
      for (rt in rI) {
        L(rt, rI[rt], rp, rR);
      }
    }
    return re.join("&").replace(/%20/g, "+");
  }
  function q(rI, rp, rk) {
    var rt = rk;
    var re = new Date(M.TlMcd(new Date() * 1, M.blMLw(1000, rt)));
    return R.set(rI, rp, {
      expires: re
    });
  }
  function W(rI) {
    var rp = "";
    var rk = "";
    try {
      if (document.hidden !== undefined) {
        rp = M.zyVAp;
        rk = M.kCofD;
      } else if (M.IQPLt(undefined, document.mozHidden)) {
        rp = M.TJRFE;
        rk = M.KjABa;
      } else if (M.IQPLt(undefined, document.msHidden)) {
        rp = M.QkYeH;
        rk = M.IgTKI;
      } else if (document.webkitHidden !== undefined) {
        rp = M.rOPpt;
        rk = M.PlJiO;
      }
      console.log(222, rk);
      document.addEventListener(rk, function () {
        if (document[rp]) {
          rI(true);
        } else {
          M.QTpdL(rI, false);
        }
      });
    } catch (rt) {
      console.log(rt);
    }
  }
  function Z(rI) {
    for (var rp = window.location.search.substring(1).split("&"), rk = 0; rk < rp.length; rk++) {
      var rt = rp[rk].split("=");
      if (M.aKpId("brand", rt[0]) && M.fvWzi != rt[0] && rt[0] != "") {
        rI[`${rt[0]}`] = M.pPiqN(decodeURIComponent, rt[1]);
      }
    }
    return rI;
  }
  function B() {
    try {
      var rI = navigator.userAgent.toLowerCase();
      var rp = function () {
        for (var rk = window.location.search.substring(1).split("&"), rt = 0; rt < rk.length; rt++) {
          var re = rk[rt].split("=");
          if (re[0] == "pid") {
            return decodeURIComponent(re[1]);
          }
        }
        return false;
      }();
      if (rp.includes(M.JAufR) && rI.includes("newsarticle")) {
        if (rI.includes("newslite")) {
          M.VkQlC(H, M.oLsRP);
        } else {
          M.VkQlC(H, M.RrDRo);
        }
      }
      if (rp.includes(M.lKtQb)) {
        if (rI.includes("nebula")) {
          H("ksnebula://home/<USER>");
          return;
        }
        if (rI.includes("kwai")) {
          M.QTpdL(H, "kwai://home/<USER>");
        }
      }
    } catch (rk) {}
  }
  function H(rI) {
    var rp = document.createElement("a");
    rp.href = rI;
    document.body.appendChild(rp);
    rp.click();
  }
  function F() {
    return new Date().toDateString();
  }
  function Q() {
    if (M.YLBAv("xGLBD", "gWKux")) {
      var re = W.universalAdvertisingConfig.find(function (rR) {
        return rR.brand === re;
      });
      if (re) {
        return re.config;
      } else {
        return null;
      }
    } else {
      var rI = navigator.userAgent.toLowerCase();
      var rp = {
        app: M.NmydO,
        slotType: M.NmydO,
        ver: null
      };
      if (rI.includes(M.bQgAj) || rI.includes(M.kQLxT)) {
        rp.app = M.RcQfJ;
      } else if (rI.includes("newsarticle") || rI.includes(M.JOHZp)) {
        rp.app = M.xfFhe;
      } else if (rI.includes("novelapp")) {
        rp.app = "com.dragon.read";
      } else if (rI.includes("novel_fm")) {
        rp.app = "com.xs.fm";
      } else if (rI.includes("aweme_lite")) {
        rp.app = M.GBRyY;
      } else if (rI.includes("aweme")) {
        rp.app = M.BHobX;
      }
      if (rI.includes("sif")) {
        rp.slotType = "reward";
      } else {
        rp.slotType = "feed";
      }
      var rk = rI.match(/(newsarticle|newslite)[\/ ](\d+\.\d+\.\d+)/);
      if (rk) {
        rp.ver = rk[2];
      }
      return rp;
    }
  }
  function Y(rI) {
    var rp = rI.indexOf(M.GCiBx);
    if (rp === -1) {
      return null;
    }
    var rk = rp + 8;
    var rt = rI.indexOf("&", rk);
    if (rt === -1) {
      rt = rI.length;
    }
    return rI.substring(rk, rt);
  }
  function V(rI) {
    if (M.cyknm === "eCqCr") {
      if (M.hzngP(M, r1.length)) {
        return {
          done: true
        };
      } else {
        return {
          done: false,
          value: Z[D++]
        };
      }
    } else {
      V = M.mIoyD == typeof Symbol && M.KtmUO == typeof Symbol.iterator ? function (rk) {
        return typeof rk;
      } : function (rk) {
        if (rk && typeof Symbol == "function" && M.MHsgv(rk.constructor, Symbol) && rk !== Symbol.prototype) {
          return M.KtmUO;
        } else {
          return typeof rk;
        }
      };
      return V(rI);
    }
  }
  function X() {
    var rI = {
      CgmuJ: M.IXWLm,
      CfChR: "[object Generator]"
    };
    var rp;
    var rk;
    var rt = typeof Symbol == "function" ? Symbol : {};
    var re = rt.iterator || M.LfUpX;
    var rR = rt.toStringTag || M.kIGhi;
    function rv(rJ, rL, ri, rc) {
      var rA = {
        NkVaI: function (rU, rz) {
          return rU(rz);
        },
        IeXDI: M.KpEgR,
        XqypW: function (rU, rz) {
          return M.arXYl(rU, rz);
        },
        OKaYX: function (rU, rz) {
          return M.BDXtx(rU, rz);
        },
        oBROD: function (rU, rz) {
          return rU === rz;
        },
        LDbpY: function (rU, rz) {
          return rU(rz);
        },
        jkGtW: "The iterator does not provide a '",
        YqNHT: "' method",
        khOiy: function (rU, rz) {
          return M.ZUtpR(rU, rz);
        },
        DcsfK: "RhBkX",
        EqlOm: function (rU, rz) {
          return rU < rz;
        },
        OzEvj: function (rU, rz) {
          return M.BDXtx(rU, rz);
        }
      };
      var rn = rL && rL.prototype instanceof rG ? rL : rG;
      var rg = Object.create(rn.prototype);
      M.cJnAk(r0, rg, M.ToDfd, function (rU, rz, rO) {
        var rE = {
          ApLxL: "average upper",
          cGSrS: rA.DcsfK,
          BfQLC: "iSZDR",
          VHvWW: function (rB, rH) {
            return rB && rH;
          },
          dgiwr: function (rB, rH) {
            return rB < rH;
          },
          vIjJF: function (rB, rH) {
            return rB < rH;
          },
          RHCFd: function (rB, rH) {
            return rA.EqlOm(rB, rH);
          },
          TerGs: function (rB, rH) {
            return rB > rH;
          },
          dHYPN: function (rB, rH) {
            return rA.OzEvj(rB, rH);
          }
        };
        var rq;
        var rN;
        var rT;
        var rC = 0;
        var rj = rO || [];
        var rW = false;
        var rw = {
          p: 0,
          n: 0,
          v: rp,
          a: rZ,
          f: rZ.bind(rp, 4),
          d: function (rB, rH) {
            rq = rB;
            rN = 0;
            rT = rp;
            rw.n = rH;
            return rx;
          }
        };
        function rZ(rB, rH) {
          var rP = {
            xIpkX: function (rY, rV) {
              return rY(rV);
            },
            kOoWC: function (rY, rV) {
              return rY(rV);
            },
            MnDXz: rE.ApLxL
          };
          if (rE.cGSrS === rE.BfQLC) {
            var rV = rP.xIpkX(rX, R);
            if (X.isArray(rV) && rV.length > 1) {
              var rX = rP.kOoWC(rN, rV);
              if (rX) {
                rr.page = rX.page;
                rB.packageName = rX.package;
                r0.jumpToken = rX.jump_token;
                rD.day_start_num = rX.day_start_num;
                z.change_pkg_type = rX.change_pkg_type;
                r8.change_pkg_type = rX.change_pkg_type;
                rm.log("average ...");
              } else {
                q.log(rP.MnDXz);
                rl();
              }
            }
          } else {
            rN = rB;
            rT = rH;
            rk = 0;
            for (; rE.VHvWW(!rW, rC) && !ra && rE.dgiwr(rk, rj.length); rk++) {
              var ra;
              var rF = rj[rk];
              var rQ = rw.p;
              var rs = rF[2];
              if (rB > 3) {
                if (ra = rs === rH) {
                  rT = rF[(rN = rF[4]) ? 5 : (rN = 3, 3)];
                  rF[4] = rF[5] = rp;
                }
              } else if (rF[0] <= rQ) {
                if (ra = rE.vIjJF(rB, 2) && rQ < rF[1]) {
                  rN = 0;
                  rw.v = rH;
                  rw.n = rF[1];
                } else if (rQ < rs && (ra = rE.RHCFd(rB, 3) || rE.TerGs(rF[0], rH) || rE.dHYPN(rH, rs))) {
                  rF[4] = rB;
                  rF[5] = rH;
                  rw.n = rs;
                  rN = 0;
                }
              }
            }
            if (ra || rB > 1) {
              return rx;
            }
            rW = true;
            throw rH;
          }
        }
        return function (rB, rH, rP) {
          if (rC > 1) {
            throw rA.NkVaI(TypeError, rA.IeXDI);
          }
          if (rW && rH === 1) {
            rZ(rH, rP);
          }
          rN = rH;
          rT = rP;
          for (; (rk = rA.XqypW(rN, 2) ? rp : rT) || !rW;) {
            if (!rq) {
              if (rN) {
                if (rA.XqypW(rN, 3)) {
                  if (rA.OKaYX(rN, 1)) {
                    rw.n = -1;
                  }
                  rZ(rN, rT);
                } else {
                  rw.n = rT;
                }
              } else {
                rw.v = rT;
              }
            }
            try {
              rC = 2;
              if (rq) {
                if (!rN) {
                  rB = "next";
                }
                if (rk = rq[rB]) {
                  if (!(rk = rk.call(rq, rT))) {
                    throw TypeError("iterator result is not an object");
                  }
                  if (!rk.done) {
                    return rk;
                  }
                  rT = rk.value;
                  if (rN < 2) {
                    rN = 0;
                  }
                } else {
                  if (rA.oBROD(1, rN) && (rk = rq.return)) {
                    rk.call(rq);
                  }
                  if (rN < 2) {
                    rT = rA.LDbpY(TypeError, rA.jkGtW + rB + rA.YqNHT);
                    rN = 1;
                  }
                }
                rq = rp;
              } else if (rA.khOiy(rk = (rW = rw.n < 0) ? rT : rU.call(rz, rw), rx)) {
                break;
              }
            } catch (ra) {
              rq = rp;
              rN = 1;
              rT = ra;
            } finally {
              rC = 1;
            }
          }
          return {
            value: rk,
            done: rW
          };
        };
      }(rJ, ri, rc), true);
      return rg;
    }
    var rx = {};
    function rG() {}
    function rd() {}
    function ro() {}
    rk = Object.getPrototypeOf;
    var rK = [][re] ? rk(rk([][re]())) : (r0(rk = {}, re, function () {
      return this;
    }), rk);
    var rS = ro.prototype = rG.prototype = Object.create(rK);
    function ry(rJ) {
      if (Object.setPrototypeOf) {
        Object.setPrototypeOf(rJ, ro);
      } else {
        rJ.__proto__ = ro;
        r0(rJ, rR, rI.CgmuJ);
      }
      rJ.prototype = Object.create(rS);
      return rJ;
    }
    rd.prototype = ro;
    M.kPGRn(r0, rS, "constructor", ro);
    M.kPGRn(r0, ro, "constructor", rd);
    rd.displayName = "GeneratorFunction";
    M.ZVLhy(r0, ro, rR, M.IXWLm);
    M.pPiqN(r0, rS);
    M.ItmPB(r0, rS, rR, "Generator");
    r0(rS, re, function () {
      return this;
    });
    M.SFhjE(r0, rS, "toString", function () {
      return rI.CfChR;
    });
    return (X = function () {
      return {
        w: rv,
        m: ry
      };
    })();
  }
  function r0(rI, rp, rk, rt) {
    var rR = Object.defineProperty;
    try {
      rR({}, "", {});
    } catch (rv) {
      rR = 0;
    }
    r0 = function (rx, rG, rd, ro) {
      if (rG) {
        if (rR) {
          rR(rx, rG, {
            value: rd,
            enumerable: !ro,
            configurable: !ro,
            writable: !ro
          });
        } else {
          rx[rG] = rd;
        }
      } else {
        function rK(rS, ry) {
          r0(rx, rS, function (rJ) {
            return this._invoke(rS, ry, rJ);
          });
        }
        rK("next", 0);
        M.mmfes(rK, "throw", 1);
        M.SmPOX(rK, M.zDQYy, 2);
      }
    };
    r0(rI, rp, rk, rt);
  }
  function r1(rI, rp) {
    var rk = {
      WKtvA: function (rd, ro) {
        return M.InbQK(rd, ro);
      },
      Wadsk: function (rd, ro) {
        return rd + ro;
      },
      FtoQC: "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}",
      tkyDI: function (rd, ro) {
        return rd == ro;
      }
    };
    var rt = M.aKpId(M.hUZwM, typeof Symbol) && rI[Symbol.iterator] || rI["@@iterator"];
    if (!rt) {
      if (Array.isArray(rI) || (rt = r2(rI)) || rp && rI && typeof rI.length == "number") {
        if (rt) {
          rI = rt;
        }
        var re = 0;
        function rR() {}
        return {
          s: rR,
          n: function () {
            if (re >= rI.length) {
              return {
                done: true
              };
            } else {
              return {
                done: false,
                value: rI[re++]
              };
            }
          },
          e: function (ro) {
            throw ro;
          },
          f: rR
        };
      }
      throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
    }
    var rv;
    var rx = true;
    var rG = false;
    return {
      s: function () {
        rt = rt.call(rI);
      },
      n: function () {
        var ro = rt.next();
        rx = ro.done;
        return ro;
      },
      e: function (ro) {
        rG = true;
        rv = ro;
      },
      f: function () {
        try {
          if (!(rx || M.YuuwA(null, rt.return))) {
            rt.return();
          }
        } finally {
          if (rG) {
            throw rv;
          }
        }
      }
    };
  }
  function r2(rI, rp) {
    if (rI) {
      if (M.mbBIO == typeof rI) {
        return r3(rI, rp);
      }
      var rk = {}.toString.call(rI).slice(8, -1);
      if (M.NKTff("Object", rk) && rI.constructor) {
        rk = rI.constructor.name;
      }
      if (rk === "Map" || M.MHsgv("Set", rk)) {
        return Array.from(rI);
      } else if (M.CULni(M.penbn, rk) || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(rk)) {
        return r3(rI, rp);
      } else {
        return undefined;
      }
    }
  }
  function r3(rI, rp) {
    if (M.Txlro("RQeOW", "IpMZb")) {
      Y(M.name, r1.value);
    } else {
      if (M.gaNlN(null, rp) || rp > rI.length) {
        rp = rI.length;
      }
      for (var rt = 0, re = Array(rp); rt < rp; rt++) {
        re[rt] = rI[rt];
      }
      return re;
    }
  }
  function r4(rI, rp) {
    if (M.Hgnmw !== "WRJHu") {
      var rv = Y("dp-click");
      M({
        eventName: "webland_bt_click"
      });
      r1.href = rv;
    } else {
      var rt = Object.keys(rI);
      if (Object.getOwnPropertySymbols) {
        var re = Object.getOwnPropertySymbols(rI);
        if (rp) {
          re = re.filter(function (rv) {
            return Object.getOwnPropertyDescriptor(rI, rv).enumerable;
          });
        }
        rt.push.apply(rt, re);
      }
      return rt;
    }
  }
  function r5(rI) {
    for (var rp = 1; M.kRpAN(rp, arguments.length); rp++) {
      var rk = arguments[rp] != null ? arguments[rp] : {};
      if (M.cpYWP(rp, 2)) {
        M.ISqNu(r4, M.hDyEn(Object, rk), true).forEach(function (rt) {
          M.ZVLhy(r6, rI, rt, rk[rt]);
        });
      } else if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(rI, Object.getOwnPropertyDescriptors(rk));
      } else {
        r4(M.RhxsZ(Object, rk)).forEach(function (rt) {
          Object.defineProperty(rI, rt, Object.getOwnPropertyDescriptor(rk, rt));
        });
      }
    }
    return rI;
  }
  function r6(rI, rp, rk) {
    if ((rp = function (rt) {
      var re = {
        zLMuW: function (rv, rx) {
          return rv != rx;
        },
        tEidV: function (rv, rx) {
          return rv(rx);
        },
        GBdzS: M.txFgS,
        pGxtr: function (rv, rx) {
          return M.dMHhK(rv, rx);
        }
      };
      var rR = function (rv) {
        if (re.zLMuW("object", re.tEidV(V, rv)) || !rv) {
          return rv;
        }
        var rx = rv[Symbol.toPrimitive];
        if (rx !== undefined) {
          var rG = rx.call(rv, "string");
          if (re.zLMuW(re.GBdzS, re.pGxtr(V, rG))) {
            return rG;
          }
          throw new TypeError("@@toPrimitive must return a primitive value.");
        }
        return re.tEidV(String, rv);
      }(rt);
      if (M.gaNlN(M.KtmUO, V(rR))) {
        return rR;
      } else {
        return rR + "";
      }
    }(rp)) in rI) {
      Object.defineProperty(rI, rp, {
        value: rk,
        enumerable: true,
        configurable: true,
        writable: true
      });
    } else {
      rI[rp] = rk;
    }
    return rI;
  }
  function r7(rI, rp, rk, rt, re, rR, rv) {
    try {
      var rG = rI[rR](rv);
      var rd = rG.value;
    } catch (ro) {
      if (M.MHsgv(M.KPyZp, "GgjDp")) {
        M.efbRw(rk, ro);
        return;
      } else {
        var rS = M.TooAB(Z, D || M.yAYYp);
        rS = r2(rS);
        rM = M.ISqNu(F, function () {
          rS.location.href = "hiapp://com.huawei.appmarket?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + rS + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
        }, 300);
      }
    }
    if (rG.done) {
      M.QwSOj(rp, rd);
    } else {
      Promise.resolve(rd).then(rt, re);
    }
  }
  var r8;
  var r9 = {
    landVersion: "kfc",
    portalVersion: M.BDJkm,
    resultCode: 0
  };
  var rr = "";
  var rf = {};
  var ru = false;
  var rm = 10;
  var rM = 0;
  var rh = "";
  var rl = "";
  var rD = [];
  (r8 = M.wRcpi(X).m(function rI() {
    var rp = {
      bpEcj: function (ra, rF) {
        return M.fPHmy(ra, rF);
      },
      cWvAF: M.klXip,
      TOciI: function (ra) {
        return ra();
      },
      ZyCIt: function (ra, rF) {
        return M.hNXpF(ra, rF);
      },
      ITcUC: function (ra, rF) {
        return ra === rF;
      },
      ychQQ: M.tUqEa,
      lSczp: function (ra) {
        return ra();
      },
      ICtSX: "hiapp://com.huawei.appmarket?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"",
      NtFWs: function (ra, rF) {
        return ra !== rF;
      },
      InWFC: M.EQrkQ,
      bpyIZ: function (ra, rF) {
        return M.VkQlC(ra, rF);
      },
      iXdsY: "intenthiapp-auto",
      lCqBD: M.Qbfxl,
      wqOoz: function (ra, rF) {
        return ra || rF;
      },
      dtgxC: M.yAYYp,
      fbIKo: function (ra, rF, rQ) {
        return M.SmPOX(ra, rF, rQ);
      },
      MZlwk: function (ra, rF) {
        return M.aKpId(ra, rF);
      },
      oJOBH: function (ra) {
        return M.Wkdvr(ra);
      },
      MBfoH: "pop-img",
      tqPto: "https://cdn.wuhanshengwan.cn/portal/common/core/",
      ZunJj: M.GSjli,
      klgyz: function (ra, rF) {
        return M.lggLb(ra, rF);
      },
      ZoZnu: "weCwj",
      puySk: M.fDhEs,
      ytMSp: M.JFWNm,
      mVqdB: function (ra, rF, rQ, rs) {
        return M.kPGRn(ra, rF, rQ, rs);
      },
      VQXLN: function (ra, rF) {
        return ra(rF);
      },
      yqvvG: function (ra, rF, rQ) {
        return M.TsNpc(ra, rF, rQ);
      },
      PNwvn: function (ra, rF) {
        return ra != rF;
      },
      HfnhZ: "j79gukslrn",
      ymDov: M.SLvXZ,
      noQqy: M.CeBEH,
      KPuaE: " data-package-name=",
      nmSCX: M.xtsCn,
      gpixU: `' data-design-params='{"fontSize": 16,"designWidth": 1080}'
\t\t\t\tdata-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
\t\t\t\tdata-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
\t\t\t\tdata-jump-token=`,
      XKTKZ: M.hGeet
    };
    var rk;
    var re;
    var rR;
    var rv;
    var rx;
    var rG;
    var rd;
    var ro;
    var rK;
    var rS;
    var ry;
    var rJ;
    var rL;
    var ri;
    var rc;
    var rA;
    var rn;
    var rg;
    var rU;
    var rz;
    var rO;
    var rE;
    var rq;
    var rN;
    var rT;
    var rC;
    var rj;
    var rW;
    var rw;
    var rZ;
    var rB;
    var rH;
    var rP;
    return X().w(function (ra) {
      var rF = {
        YpJEp: function (rQ, rs) {
          return rQ === rs;
        },
        RwNHY: "popstate",
        POXkk: function (rQ, rs) {
          return M.rgUqj(rQ, rs);
        },
        cDABg: function (rQ, rs) {
          return rQ || rs;
        },
        QtUSO: function (rQ, rs, rY) {
          return M.ISqNu(rQ, rs, rY);
        },
        ZjWhF: M.hUZwM,
        uApGp: function (rQ, rs) {
          return rQ == rs;
        },
        Dwiro: M.fuMQn,
        ckJFr: function (rQ, rs) {
          return rQ + rs;
        },
        KtAeq: function (rQ, rs) {
          return M.UotHI(rQ, rs);
        },
        nRKdj: function (rQ, rs) {
          return M.VHcCj(rQ, rs);
        },
        yGoXR: M.drUqf,
        FndLG: M.klpDJ,
        YDASj: "com.indigo.novel",
        gfWBz: "com.tjawy.yytianqi",
        lOVYx: "hwfastapp://com.tjmms.zhishihg/substitute?startLink=",
        WOCBC: M.JFWNm,
        XOjdi: M.bxdcz,
        lEkQK: function (rQ, rs) {
          return rQ / rs;
        },
        VPYPn: function (rQ, rs) {
          return M.GibYh(rQ, rs);
        },
        kigSR: function (rQ) {
          return rQ();
        },
        VBhvd: function (rQ, rs) {
          return M.gSjFU(rQ, rs);
        },
        XGDPb: function (rQ, rs) {
          return M.gSjFU(rQ, rs);
        },
        rCUnm: function (rQ, rs) {
          return rQ == rs;
        },
        ATySN: function (rQ, rs) {
          return M.rfizD(rQ, rs);
        },
        NwPSn: function (rQ, rs) {
          return rQ < rs;
        },
        iquLX: "per",
        EODJb: function (rQ, rs) {
          return rQ == rs;
        },
        GPVlC: function (rQ, rs) {
          return M.hDyEn(rQ, rs);
        },
        nwXIi: function (rQ, rs) {
          return M.dMHhK(rQ, rs);
        },
        isemv: M.sDkNm,
        DkUXM: function (rQ, rs, rY, rV) {
          return M.ZVLhy(rQ, rs, rY, rV);
        },
        sCvHT: "dp-click",
        eAPOU: M.NBloO,
        Baidy: M.zyVAp,
        aHQRn: M.QkYeH,
        zlfXQ: function (rQ, rs) {
          return M.ZUtpR(rQ, rs);
        },
        BFrCB: M.vgpGt,
        jgqqf: "pop-img"
      };
      for (;;) {
        switch (ra.n) {
          case 0:
            r9 = Z(r9);
            ra.n = 1;
            return M.rgUqj(fetch, `${M.Cshbq}?pid=${r9.pid}&channel=${r9.channel}${M.dErGl}${r9.account_id}${M.TlpRW}${r9.oaidmd5}`);
          case 1:
            rk = ra.v;
            ra.n = 2;
            return rk.json();
          case 2:
            re = ra.v;
            rR = re.data;
            rv = (rf = M.jzIKm(rR, {})).extraConfig;
            rx = M.NKTff(undefined, rv) ? {} : rv;
            r9.channel = rf.channel;
            r9.day_start_num = rf.day_start_num;
            r9.change_pkg_type = rf.change_pkg_type;
            rr = rf.brand;
            rG = M.Wkdvr(Q);
            rd = rG.app;
            ro = rG.slotType;
            r9.realSourcePkg = M.OLVln(M.FVwmq(rd, "-"), ro);
            K(function () {
              if (rp.bpEcj("http://ad.partner.gifshow.com/track/activate?callback", r9.clickid)) {
                var rQ = Y(location.href);
                r9.clickid = rQ;
              }
              rH({
                eventName: rp.cWvAF
              });
              rp.TOciI(rK);
            });
            rK = function () {
              if (rx) {
                if (rp.bpEcj("number", typeof rx.closeAttr) && rx.closeAttr) {
                  return;
                }
                if (rp.ZyCIt("number", typeof rx.backRetryCount)) {
                  rm = rx.backRetryCount;
                }
                try {
                  if (rp.ITcUC("bUJgI", "bUJgI")) {
                    if (rx.backGlobal) {
                      rD = JSON.parse(rx.backGlobal);
                    }
                  } else {
                    var rV;
                    var rX = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
                    do {
                      rV = F.floor(rd.random() * (ra - R + 1)) + ry;
                    } while (rX !== null && rV === rX);
                    return rV;
                  }
                } catch (rV) {}
              }
              if (rp.ychQQ == rr) {
                rm = 0;
              }
              if (rf.isAutomatic && rZ()) {
                ru = true;
                rT();
                rp.TOciI(rP);
                rw();
                rp.lSczp(rB);
                rp.TOciI(rS);
                rU();
                rL();
              }
            };
            rS = function () {
              ri();
            };
            ry = function (rQ) {
              var rs = rf.universalAdvertisingConfig.find(function (rY) {
                return rF.YpJEp(rY.brand, rQ);
              });
              if (rs) {
                return rs.config;
              } else {
                return null;
              }
            };
            rJ = 0;
            rL = function () {
              try {
                window.history.pushState(null, null, "#");
                window.addEventListener(rF.RwNHY, function (rs) {
                  if (++rJ < 2) {
                    rP();
                    window.history.pushState(null, null, "#");
                  }
                });
              } catch (rs) {}
            };
            ri = function (rQ) {
              try {
                rh = rF.QtUSO(setTimeout, function () {
                  var rs = rF.POXkk(rA, rF.cDABg(rQ, "dp-auto"));
                  location.href = rs;
                  clearTimeout(rh);
                }, 0);
              } catch (rs) {}
            };
            rc = function (rQ) {
              var rs = {
                EHIdB: function (rX, f0) {
                  return rX + f0;
                },
                gnXhG: rp.ICtSX
              };
              if (window.Kwai) {
                if (rp.NtFWs("IrBTu", rp.InWFC)) {
                  if (rF.ZjWhF != typeof f1) {
                    if (rF.uApGp(rF.Dwiro, typeof (r0 = r1({}, r2, r3)).expires)) {
                      r4.expires = new r5(rF.ckJFr(r6.now(), rF.KtAeq(86400000, r7.expires)));
                    }
                    if (r8.expires) {
                      r9.expires = rr.expires.toUTCString();
                    }
                    rf = ru(rm).replace(/%(2[346B]|5E|60|7C)/g, rM).replace(/[()]/g, rh);
                    var f0 = "";
                    for (var f1 in rl) {
                      if (rD[f1]) {
                        f0 += rF.ckJFr("; ", f1);
                        if (rb[f1] !== true) {
                          f0 += rF.nRKdj("=", rI[f1].split(";")[0]);
                        }
                      }
                    }
                    return rp.cookie = rF.ckJFr(rk + "=", rP.write(re, rR)) + f0;
                  }
                } else {
                  var rY = rp.bpyIZ(rA, rQ || rp.iXdsY);
                  rY = rp.bpyIZ(encodeURIComponent, rY);
                  window.location.href = rp.lCqBD + rY + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}#Intent;scheme=hiapp;category=android.intent.category.BROWSABLE;action=android.intent.action.VIEW;end";
                }
              } else {
                var rV = rA(rp.wqOoz(rQ, rp.dtgxC));
                rV = encodeURIComponent(rV);
                rl = rp.fbIKo(setTimeout, function () {
                  window.location.href = rs.EHIdB(rs.gnXhG, rV) + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
                }, 300);
              }
            };
            window.onBtnClick = function () {
              rp.TOciI(rP);
              rH({
                eventName: "webland_bt_click"
              });
              var rQ = rA("dp-click");
              location.href = rQ;
              if (!(rr != "huawei" && rp.MZlwk(rp.ychQQ, rr))) {
                rp.oJOBH(rc);
              }
            };
            rA = function (rQ, rs) {
              var f1 = "hap://app/";
              r9.hapType = rQ ? rF.WOCBC + rQ : rF.XOjdi;
              var rX = z(r9);
              if (rr == "huawei") {
                f1 = "hwfastapp://";
              }
              if (rs) {
                f1 = "hap://app/";
              }
              var f0 = `${f1}${rf.packageName}/${rf.page}?${rX}`;
              try {
                if (window.Kwai) {
                  return f0;
                }
              } catch (f2) {}
              if (!(rr != "huawei" || ["com.whxfy.zhanyuebk", rF.yGoXR, rF.FndLG, rF.YDASj, "com.tjmms.zhishihg", "com.whlq.qqiaoxzs", "com.bjjcy.yybqbao", rF.gfWBz, "com.whlq.lanyue", "com.bjzjf.jijutool"].includes(rf.packageName))) {
                f0 = rF.lOVYx + encodeURIComponent(f0);
              }
              return f0;
            };
            rn = function (rQ) {
              var rs = {
                cYyAb: rp.MBfoH,
                hWiHp: rp.tqPto,
                HgroS: rp.ZunJj,
                hYRQt: function (rY, rV) {
                  return rY == rV;
                },
                fZbkC: function (rY, rV) {
                  return rp.klgyz(rY, rV);
                },
                FaQiE: function (rY, rV) {
                  return rp.NtFWs(rY, rV);
                },
                fygVR: rp.ZoZnu,
                ERlAK: rp.puySk,
                HReHK: function (rY, rV) {
                  return rY === rV;
                },
                XJsIJ: function (rY, rV) {
                  return rY(rV);
                },
                tUkVz: function (rY, rV) {
                  return rp.MZlwk(rY, rV);
                },
                LsCyT: function (rY, rV) {
                  return rY(rV);
                }
              };
              rQ = rp.ytMSp + rQ;
              return Object.fromEntries(Object.entries(rp.mVqdB(r5, r5({}, r9), {}, {
                hapType: rQ
              })).filter(function (rY) {
                var rV;
                var rX;
                rX = 2;
                var f0 = function (f1) {
                  if (Array.isArray(f1)) {
                    return f1;
                  }
                }(rV = rY) || function (f1, f2) {
                  var f3 = {
                    xHFJt: "pop",
                    zEpbG: function (fu, fm, fM, fh) {
                      return fu(fm, fM, fh);
                    },
                    CwEya: rs.cYyAb,
                    cfoEu: rs.hWiHp,
                    ZjLrj: rs.HgroS
                  };
                  var f4 = rs.hYRQt(null, f1) ? null : rs.fZbkC("undefined", typeof Symbol) && f1[Symbol.iterator] || f1["@@iterator"];
                  if (f4 != null) {
                    var f5;
                    var f6;
                    var f7;
                    var f8;
                    var f9 = [];
                    var fr = true;
                    var ff = false;
                    try {
                      if (rs.FaQiE(rs.fygVR, rs.ERlAK)) {
                        f7 = (f4 = f4.call(f1)).next;
                        if (rs.HReHK(0, f2)) {
                          if (rs.XJsIJ(Object, f4) !== f4) {
                            return;
                          }
                          fr = false;
                        } else {
                          for (; !(fr = (f5 = f7.call(f4)).done) && (f9.push(f5.value), f9.length !== f2); fr = true);
                        }
                      } else {
                        r1.getElementById(f3.xHFJt).style.display = "block";
                        var fm = f3.zEpbG(ff, 1, 4, rJ.landPopIndex);
                        r2.landPopIndex = fm;
                        rM.getElementById(f3.CwEya).src = f3.cfoEu.concat(fm, f3.ZjLrj);
                      }
                    } catch (fm) {
                      ff = true;
                      f6 = fm;
                    } finally {
                      try {
                        if (!fr && rs.tUkVz(null, f4.return) && (f8 = f4.return(), rs.LsCyT(Object, f8) !== f8)) {
                          return;
                        }
                      } finally {
                        if (ff) {
                          throw f6;
                        }
                      }
                    }
                    return f9;
                  }
                }(rV, rX) || r2(rV, rX) || function () {
                  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
                }();
                f0[0];
                return f0[1] !== null;
              }));
            };
            rg = new Date();
            new BroadcastChannel("webControl");
            rU = function () {
              rp.VQXLN(W, function (rQ) {
                if (rQ) {
                  rF.POXkk(clearTimeout, "");
                  clearTimeout(rh);
                  clearTimeout("");
                  rF.POXkk(clearTimeout, rl);
                  rF.POXkk(clearTimeout, "");
                  clearTimeout("");
                  clearTimeout("");
                } else if (ru && rf.isAutomatic) {
                  var rY = rF.lEkQK(rF.VPYPn(new Date(), rg), 60000);
                  r9.resultCode = Math.floor(rY);
                  rz();
                  if (rM < rm) {
                    rM++;
                    r9.retryCount = rM;
                    rF.kigSR(rW);
                  } else {
                    rF.QtUSO(setTimeout, function () {
                      B();
                    }, 500);
                  }
                }
              });
            };
            rz = function (rQ) {
              if (rr == "vivo" && rx && rx.averageCount) {
                rT();
              } else {
                rF.kigSR(rE);
              }
              rF.VBhvd(rN, true);
              rP();
            };
            rO = 0;
            rE = function (rQ) {
              try {
                if (rf.universalAdvertisingEnabled) {
                  var rs = rF.XGDPb(ry, rr);
                  if (Array.isArray(rs) && rs.length > 1) {
                    var rY = rs.length - 1;
                    rO = J(0, rY, rO);
                    if (rF.rCUnm(rF.Dwiro, typeof (rX = rQ)) && !rF.ATySN(isNaN, rX) && rF.NwPSn(rQ, rY)) {
                      rO = rQ;
                    }
                    var rV = rs[rO];
                    rf.page = rV.page;
                    rf.packageName = rV.package;
                    rf.jumpToken = rV.jump_token;
                    rf.day_start_num = rV.day_start_num;
                    r9.change_pkg_type = rV.change_pkg_type;
                    rf.change_pkg_type = rV.change_pkg_type;
                  }
                }
              } catch (f0) {
                console.error(f0);
              }
              var rX;
            };
            rq = 0;
            rN = function () {
              try {
                if (rx && rx.soonAbn) {
                  return;
                }
                if (rD && Array.isArray(rD) && rD.length > 0) {
                  rq = rq < rD.length ? rq : 0;
                  var rQ = rD[rq];
                  if (rQ) {
                    rq++;
                    rf.page = rQ.page;
                    rf.packageName = rQ.package;
                    rf.jumpToken = rQ.jumpToken;
                    r9.message = rF.iquLX;
                    console.log("per ...", rf.packageName);
                  }
                }
              } catch (rs) {
                console.error(rs);
              }
            };
            rT = function () {
              try {
                if (rF.EODJb("vivo", rr) && rf.universalAdvertisingEnabled && rx.averageCount) {
                  var rQ = rF.GPVlC(ry, rr);
                  if (Array.isArray(rQ) && rQ.length > 1) {
                    var rs = rF.nwXIi(rC, rQ);
                    if (rs) {
                      rf.page = rs.page;
                      rf.packageName = rs.package;
                      rf.jumpToken = rs.jump_token;
                      rf.day_start_num = rs.day_start_num;
                      rf.change_pkg_type = rs.change_pkg_type;
                      r9.change_pkg_type = rs.change_pkg_type;
                      console.log(rF.isemv);
                    } else {
                      console.log("average upper");
                      rF.kigSR(B);
                    }
                  }
                }
                return false;
              } catch (rY) {
                console.error(rY);
              }
            };
            rC = function (rQ, rs) {
              var rV;
              var rX = rs || 2;
              var f0 = r1(rQ.sort(function (f3, f4) {
                return f4.ratio - f3.ratio;
              }));
              try {
                for (f0.s(); !(rV = f0.n()).done;) {
                  var f1 = rV.value;
                  var f2 = rF.VBhvd(rj, f1.package);
                  if (f2 < rX) {
                    rF.DkUXM(q, `${f1.package}_${F()}`, f2 + 1, 86400);
                    return f1;
                  }
                }
              } catch (f3) {
                f0.e(f3);
              } finally {
                f0.f();
              }
              return null;
            };
            rj = function (rQ) {
              var rs;
              rs = `${rQ}_${rp.lSczp(F)}`;
              var rY = R.get(rs);
              if (rY) {
                return rp.yqvvG(parseInt, rY, 10);
              } else {
                return 0;
              }
            };
            rW = function () {
              rF.GPVlC(ri, "retry-auto");
            };
            rw = function () {
              try {
                if (!(rr != "huawei" && rp.PNwvn(rp.ychQQ, rr))) {
                  r9.cbAd = rp.HfnhZ;
                }
              } catch (rQ) {}
            };
            rZ = function () {
              try {
                if (r9.adid && (r9.adid.includes("_") || r9.adid.includes("$") || r9.adid.includes("{"))) {
                  return false;
                }
              } catch (rQ) {}
              return true;
            };
            rB = function (rQ) {
              var rs = rp.VQXLN(rn, rQ || "iframe-auto");
              var rY = rp.ymDov.concat(rM);
              var rV = rp.noQqy.concat(rY, rp.KPuaE).concat(rf.packageName, rp.nmSCX).concat(rf.page, "\n\t\t\t\tdata-params='").concat(rs && JSON.stringify(rs), rp.gpixU).concat(rf.jumpToken || "", `
\t\t\t\t>
\t\t\t\t<templates>
\t\t\t\t\t<div class="btn-box">
              <div style="display:none">#img#</div>
              <span id="btn" class="btn an_scale" style="background: linear-gradient(90deg, rgb(236, 85, 40) 0%, rgb(255, 112, 112) 100%);">点击进入</span>
\t\t\t\t\t</div>
\t\t\t\t</templates>
\t\t\t\t<styles>
\t\t\t\t\t.btn-box{
\t\t\t\t\t  width:100%;
\t\t\t\t\t}
\t\t\t\t\t.btn {
\t\t\t\t\tdisplay:block;
\t\t\t\t\twidth:80%;
          height: 52px;
          line-height: 52px;
\t\t\t\t\tmargin:0 auto;
          border-radius: 90px;
          text-align: center;
          font-size: 20px;
          color: #ffffff;
\t\t\t\t\t}
\t\t\t\t\t.an_scale {
\t\t\t\t\tanimation-name: Scale;
\t\t\t\t\tanimation-iteration-count: infinite;
\t\t\t\t\tanimation-duration: 1500ms;
\t\t\t\t\tanimation-fill-mode: none;
\t\t\t\t\tanimation-timing-function: linear;
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t@keyframes Scale {
\t\t\t\t\t0% {
\t\t\t\t\ttransform: scale(1);
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t50% {
\t\t\t\t\ttransform: scale(1.1);
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t100% {
\t\t\t\t\ttransform: scale(1);
\t\t\t\t\t}
\t\t\t\t\t}
\t\t\t\t</styles>
\t\t\t</qa-router-button>
\t\t`);
              document.getElementById("qa-web").innerHTML = rV;
            };
            window.handleExposeEvent = function () {};
            window.handleClickEvent = function () {
              var rQ = rA(rF.sCvHT);
              rH({
                eventName: rF.eAPOU
              });
              location.href = rQ;
            };
            rH = function (rQ) {
              var rs = rp.mVqdB(r5, rp.fbIKo(r5, r5({}, rQ), r9), {}, {
                eventType: "webland_view",
                pageName: "webland_page",
                brand: rr,
                pkg: rf.packageName,
                jumpToken: rf.jumpToken,
                isAutomatic: rf.isAutomatic,
                isComplained: rf.isComplained,
                UserAgent: navigator.userAgent,
                host: window.location.host,
                day_start_num: rf.day_start_num
              });
              var rY = rp.XKTKZ.concat(z(rs));
              new Image().src = rY;
            };
            rP = function () {
              var rQ = {
                quNGt: function (rY, rV) {
                  return rY(rV);
                },
                cCSLn: function (rY, rV) {
                  return rY !== rV;
                },
                uUJnx: rF.Baidy,
                QCZNx: "visibilitychange",
                zrQqV: rF.aHQRn,
                oeAin: "msvisibilitychange",
                aBrSa: function (rY, rV) {
                  return rF.zlfXQ(rY, rV);
                },
                lUQNV: "webkitHidden"
              };
              if (rF.YpJEp(rF.BFrCB, "jgNTs")) {
                var rV = {
                  fEeKK: function (f1, f2) {
                    return f1(f2);
                  },
                  jhkZE: function (f1, f2) {
                    return rQ.quNGt(f1, f2);
                  }
                };
                var rX = "";
                var f0 = "";
                try {
                  if (rQ.cCSLn(undefined, rT.hidden)) {
                    rX = rQ.uUJnx;
                    f0 = rQ.QCZNx;
                  } else if (rK.mozHidden !== undefined) {
                    rX = "mozHidden";
                    f0 = "mozvisibilitychange";
                  } else if (rv.msHidden !== undefined) {
                    rX = rQ.zrQqV;
                    f0 = rQ.oeAin;
                  } else if (rQ.aBrSa(undefined, rN.webkitHidden)) {
                    rX = rQ.lUQNV;
                    f0 = "webkitvisibilitychange";
                  }
                  rg.log(222, f0);
                  z.addEventListener(f0, function () {
                    if (rx[rX]) {
                      rV.fEeKK(rD, true);
                    } else {
                      rV.jhkZE(rX, false);
                    }
                  });
                } catch (f1) {
                  rD.log(f1);
                }
              } else {
                document.getElementById("pop").style.display = "block";
                var rs = rF.DkUXM(J, 1, 4, r9.landPopIndex);
                r9.landPopIndex = rs;
                document.getElementById(rF.jgqqf).src = `https://cdn.wuhanshengwan.cn/portal/common/core/${rs}.webp`;
              }
            };
          case 3:
            return ra.a(2);
        }
      }
    }, rI);
  }), function () {
    var rp = {
      GEBtF: function (re, rR, rv, rx, rG, rd, ro, rK) {
        return M.qTRks(re, rR, rv, rx, rG, rd, ro, rK);
      },
      ILFKq: function (re, rR) {
        return re(rR);
      }
    };
    var rk = this;
    var rt = arguments;
    return new Promise(function (re, rR) {
      var rx = r8.apply(rk, rt);
      function rG(ro) {
        rp.GEBtF(r7, rx, re, rR, rG, rd, "next", ro);
      }
      function rd(ro) {
        r7(rx, re, rR, rG, rd, "throw", ro);
      }
      rp.ILFKq(rG, undefined);
    });
  })();
})();