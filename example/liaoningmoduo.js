(function () {
  const n = document.createElement("link").relList;
  if (n && n.supports && n.supports("modulepreload")) {
    return;
  }
  for (const r of document.querySelectorAll("link[rel=\"modulepreload\"]")) {
    o(r);
  }
  new MutationObserver(r => {
    for (const a of r) {
      if (a.type === "childList") {
        for (const l of a.addedNodes) {
          if (l.tagName === "LINK" && l.rel === "modulepreload") {
            o(l);
          }
        }
      }
    }
  }).observe(document, {
    childList: true,
    subtree: true
  });
  function x(r) {
    const a = {};
    if (r.integrity) {
      a.integrity = r.integrity;
    }
    if (r.referrerpolicy) {
      a.referrerPolicy = r.referrerpolicy;
    }
    if (r.crossorigin === "use-credentials") {
      a.credentials = "include";
    } else if (r.crossorigin === "anonymous") {
      a.credentials = "omit";
    } else {
      a.credentials = "same-origin";
    }
    return a;
  }
  function o(r) {
    if (r.ep) {
      return;
    }
    r.ep = true;
    const a = x(r);
    fetch(r.href, a);
  }
})();
function V0(e, t) {
  const x = Object.create(null);
  const o = e.split(",");
  for (let r = 0; r < o.length; r++) {
    x[o[r]] = true;
  }
  if (t) {
    return r => !!x[r.toLowerCase()];
  } else {
    return r => !!x[r];
  }
}
const ue = {};
const Ht = [];
const Ye = () => {};
const Nr = () => false;
const Gn = e => e.charCodeAt(0) === 111 && e.charCodeAt(1) === 110 && (e.charCodeAt(2) > 122 || e.charCodeAt(2) < 97);
const z0 = e => e.startsWith("onUpdate:");
const Re = Object.assign;
const L0 = (e, t) => {
  const x = e.indexOf(t);
  if (x > -1) {
    e.splice(x, 1);
  }
};
const Mr = Object.prototype.hasOwnProperty;
const xe = (e, t) => Mr.call(e, t);
const Y = Array.isArray;
const qt = e => Yn(e) === "[object Map]";
const no = e => Yn(e) === "[object Set]";
const X = e => typeof e === "function";
const ye = e => typeof e === "string";
const Yt = e => typeof e == "symbol";
const pe = e => e !== null && typeof e === "object";
const xo = e => (pe(e) || X(e)) && X(e.then) && X(e.catch);
const oo = Object.prototype.toString;
const Yn = e => oo.call(e);
const Fr = e => {
  return Yn(e).slice(8, -1);
};
const ro = e => Yn(e) === "[object Object]";
const H0 = e => ye(e) && e !== "NaN" && e[0] !== "-" && "" + parseInt(e, 10) === e;
const Bn = V0(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted");
const Zn = e => {
  const n = Object.create(null);
  return x => n[x] || (n[x] = e(x));
};
const Br = /-(\w)/g;
const Wt = Zn(e => {
  return e.replace(Br, (n, x) => x ? x.toUpperCase() : "");
});
const Vr = /\B([A-Z])/g;
const Zt = Zn(e => e.replace(Vr, "-$1").toLowerCase());
const io = Zn(e => e.charAt(0).toUpperCase() + e.slice(1));
const u0 = Zn(e => e ? "on" + io(e) : "");
const Ot = (e, t) => !Object.is(e, t);
const f0 = (e, t) => {
  for (let x = 0; x < e.length; x++) {
    e[x](t);
  }
};
const Dn = (e, t, n) => {
  Object.defineProperty(e, t, {
    configurable: true,
    enumerable: false,
    value: n
  });
};
const zr = e => {
  const t = parseFloat(e);
  if (isNaN(t)) {
    return e;
  } else {
    return t;
  }
};
let hx;
const y0 = () => {
  return hx || (hx = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
};
function q0(e) {
  if (Y(e)) {
    const n = {};
    for (let x = 0; x < e.length; x++) {
      const o = e[x];
      const r = ye(o) ? Dr(o) : q0(o);
      if (r) {
        for (const i in r) {
          n[i] = r[i];
        }
      }
    }
    return n;
  } else if (ye(e) || pe(e)) {
    return e;
  }
}
const Lr = /;(?![^(]*\))/g;
const Hr = /:([^]+)/;
const qr = /\/\*[^]*?\*\//g;
function Dr(e) {
  const n = {};
  e.replace(qr, "").split(Lr).forEach(x => {
    if (x) {
      const r = x.split(Hr);
      if (r.length > 1) {
        n[r[0].trim()] = r[1].trim();
      }
    }
  });
  return n;
}
function D0(e) {
  let n = "";
  if (ye(e)) {
    n = e;
  } else if (Y(e)) {
    for (let x = 0; x < e.length; x++) {
      const o = D0(e[x]);
      if (o) {
        n += o + " ";
      }
    }
  } else if (pe(e)) {
    for (const x in e) {
      if (e[x]) {
        n += x + " ";
      }
    }
  }
  return n.trim();
}
const $r = "itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly";
const _r = V0($r);
function co(e) {
  return !!e || e === "";
}
const Le = e => {
  if (ye(e)) {
    return e;
  } else if (e == null) {
    return "";
  } else if (Y(e) || pe(e) && (e.toString === oo || !X(e.toString))) {
    return JSON.stringify(e, so, 2);
  } else {
    return String(e);
  }
};
const so = (e, t) => {
  if (t && t.__v_isRef) {
    return so(e, t.value);
  } else if (qt(t)) {
    return {
      ["Map(" + t.size + ")"]: [...t.entries()].reduce((x, [o, r], i) => {
        x[d0(o, i) + " =>"] = r;
        return x;
      }, {})
    };
  } else if (no(t)) {
    return {
      ["Set(" + t.size + ")"]: [...t.values()].map(x => d0(x))
    };
  } else if (Yt(t)) {
    return d0(t);
  } else if (pe(t) && !Y(t) && !ro(t)) {
    return String(t);
  } else {
    return t;
  }
};
const d0 = (e, t = "") => {
  var x;
  if (Yt(e)) {
    return "Symbol(" + ((x = e.description) != null ? x : t) + ")";
  } else {
    return e;
  }
};
let We;
class Wr {
  constructor(t = false) {
    this.detached = t;
    this._active = true;
    this.effects = [];
    this.cleanups = [];
    this.parent = We;
    if (!t && We) {
      this.index = (We.scopes || (We.scopes = [])).push(this) - 1;
    }
  }
  get active() {
    return this._active;
  }
  run(t) {
    if (this._active) {
      const n = We;
      try {
        We = this;
        return t();
      } finally {
        We = n;
      }
    }
  }
  on() {
    We = this;
  }
  off() {
    We = this.parent;
  }
  stop(t) {
    if (this._active) {
      let x;
      let o;
      x = 0;
      o = this.effects.length;
      for (; x < o; x++) {
        this.effects[x].stop();
      }
      x = 0;
      o = this.cleanups.length;
      for (; x < o; x++) {
        this.cleanups[x]();
      }
      if (this.scopes) {
        x = 0;
        o = this.scopes.length;
        x = 0;
        o = this.scopes.length;
        for (; x < o; x++) {
          this.scopes[x].stop(true);
        }
      }
      if (!this.detached && this.parent && !t) {
        const r = this.parent.scopes.pop();
        if (r && r !== this) {
          this.parent.scopes[this.index] = r;
          r.index = this.index;
        }
      }
      this.parent = undefined;
      this._active = false;
    }
  }
}
function Kr(e, t = We) {
  if (t && t.active) {
    t.effects.push(e);
  }
}
function Jr() {
  return We;
}
const $0 = e => {
  const t = new Set(e);
  t.w = 0;
  t.n = 0;
  return t;
};
const ao = e => (e.w & wt) > 0;
const lo = e => (e.n & wt) > 0;
const Gr = ({
              deps: e
            }) => {
  if (e.length) {
    for (let n = 0; n < e.length; n++) {
      e[n].w |= wt;
    }
  }
};
const Yr = e => {
  const {
    deps: n
  } = e;
  if (n.length) {
    let x = 0;
    for (let o = 0; o < n.length; o++) {
      const r = n[o];
      if (ao(r) && !lo(r)) {
        r.delete(e);
      } else {
        n[x++] = r;
      }
      r.w &= ~wt;
      r.n &= ~wt;
    }
    n.length = x;
  }
};
const w0 = new WeakMap();
let cn = 0;
let wt = 1;
const k0 = 30;
let Ke;
const At = Symbol("");
const S0 = Symbol("");
class _0 {
  constructor(t, n = null, x) {
    this.fn = t;
    this.scheduler = n;
    this.active = true;
    this.deps = [];
    this.parent = undefined;
    Kr(this, x);
  }
  run() {
    if (!this.active) {
      return this.fn();
    }
    let n = Ke;
    let x = vt;
    for (; n;) {
      if (n === this) {
        return;
      }
      n = n.parent;
    }
    try {
      this.parent = Ke;
      Ke = this;
      vt = true;
      wt = 1 << ++cn;
      if (cn <= k0) {
        Gr(this);
      } else {
        mx(this);
      }
      return this.fn();
    } finally {
      if (cn <= k0) {
        Yr(this);
      }
      wt = 1 << --cn;
      Ke = this.parent;
      vt = x;
      this.parent = undefined;
      if (this.deferStop) {
        this.stop();
      }
    }
  }
  stop() {
    if (Ke === this) {
      this.deferStop = true;
    } else if (this.active) {
      mx(this);
      if (this.onStop) {
        this.onStop();
      }
      this.active = false;
    }
  }
}
function mx(e) {
  const {
    deps: n
  } = e;
  if (n.length) {
    for (let x = 0; x < n.length; x++) {
      n[x].delete(e);
    }
    n.length = 0;
  }
}
let vt = true;
const uo = [];
function Qt() {
  uo.push(vt);
  vt = false;
}
function Xt() {
  const t = uo.pop();
  vt = t === undefined ? true : t;
}
function Fe(e, t, n) {
  if (vt && Ke) {
    let o = w0.get(e);
    if (!o) {
      w0.set(e, o = new Map());
    }
    let r = o.get(n);
    if (!r) {
      o.set(n, r = $0());
    }
    fo(r);
  }
}
function fo(e, t) {
  let x = false;
  if (cn <= k0) {
    if (!lo(e)) {
      e.n |= wt;
      x = !ao(e);
    }
  } else {
    x = !e.has(Ke);
  }
  if (x) {
    e.add(Ke);
    Ke.deps.push(e);
  }
}
function ut(e, t, n, x, o, r) {
  const a = w0.get(e);
  if (!a) {
    return;
  }
  let l = [];
  if (t === "clear") {
    l = [...a.values()];
  } else if (n === "length" && Y(e)) {
    const p = Number(x);
    a.forEach((m, v) => {
      if (v === "length" || !Yt(v) && v >= p) {
        l.push(m);
      }
    });
  } else {
    if (n !== undefined) {
      l.push(a.get(n));
    }
    switch (t) {
      case "add":
        if (Y(e)) {
          if (H0(n)) {
            l.push(a.get("length"));
          }
        } else {
          l.push(a.get(At));
          if (qt(e)) {
            l.push(a.get(S0));
          }
        }
        break;
      case "delete":
        if (!Y(e)) {
          l.push(a.get(At));
          if (qt(e)) {
            l.push(a.get(S0));
          }
        }
        break;
      case "set":
        if (qt(e)) {
          l.push(a.get(At));
        }
        break;
    }
  }
  if (l.length === 1) {
    if (l[0]) {
      C0(l[0]);
    }
  } else {
    const p = [];
    for (const m of l) {
      if (m) {
        p.push(...m);
      }
    }
    C0($0(p));
  }
}
function C0(e, t) {
  const x = Y(e) ? e : [...e];
  for (const o of x) {
    if (o.computed) {
      bx(o);
    }
  }
  for (const o of x) {
    if (!o.computed) {
      bx(o);
    }
  }
}
function bx(e, t) {
  if (e !== Ke || e.allowRecurse) {
    if (e.scheduler) {
      e.scheduler();
    } else {
      e.run();
    }
  }
}
const Zr = V0("__proto__,__v_isRef,__isVue");
const po = new Set(Object.getOwnPropertyNames(Symbol).filter(e => e !== "arguments" && e !== "caller").map(e => Symbol[e]).filter(Yt));
const gx = Qr();
function Qr() {
  const t = {};
  ["includes", "indexOf", "lastIndexOf"].forEach(n => {
    t[n] = function (...x) {
      const r = re(this);
      for (let a = 0, l = this.length; a < l; a++) {
        Fe(r, "get", a + "");
      }
      const i = r[n](...x);
      if (i === -1 || i === false) {
        return r[n](...x.map(re));
      } else {
        return i;
      }
    };
  });
  ["push", "pop", "shift", "unshift", "splice"].forEach(n => {
    t[n] = function (...x) {
      Qt();
      const r = re(this)[n].apply(this, x);
      Xt();
      return r;
    };
  });
  return t;
}
function Xr(e) {
  const n = re(this);
  Fe(n, "has", e);
  return n.hasOwnProperty(e);
}
class ho {
  constructor(t = false, n = false) {
    this._isReadonly = t;
    this._shallow = n;
  }
  get(t, n, x) {
    const r = this._isReadonly;
    const i = this._shallow;
    if (n === "__v_isReactive") {
      return !r;
    }
    if (n === "__v_isReadonly") {
      return r;
    }
    if (n === "__v_isShallow") {
      return i;
    }
    if (n === "__v_raw") {
      if (x === (r ? i ? fi : vo : i ? go : bo).get(t) || Object.getPrototypeOf(t) === Object.getPrototypeOf(x)) {
        return t;
      } else {
        return undefined;
      }
    }
    const a = Y(t);
    if (!r) {
      if (a && xe(gx, n)) {
        return Reflect.get(gx, n, x);
      }
      if (n === "hasOwnProperty") {
        return Xr;
      }
    }
    const l = Reflect.get(t, n, x);
    if ((Yt(n) ? po.has(n) : Zr(n)) || (!r && Fe(t, "get", n), i)) {
      return l;
    } else if (Ae(l)) {
      if (a && H0(n)) {
        return l;
      } else {
        return l.value;
      }
    } else if (pe(l)) {
      if (r) {
        return yo(l);
      } else {
        return fn(l);
      }
    } else {
      return l;
    }
  }
}
class mo extends ho {
  constructor(t = false) {
    super(false, t);
  }
  set(t, n, x, o) {
    let i = t[n];
    if (Kt(i) && Ae(i) && !Ae(x)) {
      return false;
    }
    if (!this._shallow && (!$n(x) && !Kt(x) && (i = re(i), x = re(x)), !Y(t) && Ae(i) && !Ae(x))) {
      i.value = x;
      return true;
    }
    const a = Y(t) && H0(n) ? Number(n) < t.length : xe(t, n);
    const l = Reflect.set(t, n, x, o);
    if (t === re(o)) {
      if (a) {
        if (Ot(x, i)) {
          ut(t, "set", n, x);
        }
      } else {
        ut(t, "add", n, x);
      }
    }
    return l;
  }
  deleteProperty(t, n) {
    const o = xe(t, n);
    t[n];
    const r = Reflect.deleteProperty(t, n);
    if (r && o) {
      ut(t, "delete", n, undefined);
    }
    return r;
  }
  has(t, n) {
    const o = Reflect.has(t, n);
    if (!Yt(n) || !po.has(n)) {
      Fe(t, "has", n);
    }
    return o;
  }
  ownKeys(t) {
    Fe(t, "iterate", Y(t) ? "length" : At);
    return Reflect.ownKeys(t);
  }
}
class ei extends ho {
  constructor(t = false) {
    super(true, t);
  }
  set(t, n) {
    return true;
  }
  deleteProperty(t, n) {
    return true;
  }
}
const ti = new mo();
const ni = new ei();
const xi = new mo(true);
const W0 = e => e;
const Qn = e => Reflect.getPrototypeOf(e);
function In(e, t, n = false, x = false) {
  e = e.__v_raw;
  const r = re(e);
  const i = re(t);
  if (!n) {
    if (Ot(t, i)) {
      Fe(r, "get", t);
    }
    Fe(r, "get", i);
  }
  const {
    has: a
  } = Qn(r);
  const l = x ? W0 : n ? G0 : dn;
  if (a.call(r, t)) {
    return l(e.get(t));
  }
  if (a.call(r, i)) {
    return l(e.get(i));
  }
  if (e !== r) {
    e.get(t);
  }
}
function En(e, t = false) {
  const x = this.__v_raw;
  const o = re(x);
  const r = re(e);
  if (!t) {
    if (Ot(e, r)) {
      Fe(o, "has", e);
    }
    Fe(o, "has", r);
  }
  if (e === r) {
    return x.has(e);
  } else {
    return x.has(e) || x.has(r);
  }
}
function An(e, t = false) {
  e = e.__v_raw;
  if (!t) {
    Fe(re(e), "iterate", At);
  }
  return Reflect.get(e, "size", e);
}
function vx(e) {
  e = re(e);
  const n = re(this);
  if (!Qn(n).has.call(n, e)) {
    n.add(e);
    ut(n, "add", e, e);
  }
  return this;
}
function yx(e, t) {
  t = re(t);
  const x = re(this);
  const {
    has: o,
    get: r
  } = Qn(x);
  let i = o.call(x, e);
  if (!i) {
    e = re(e);
    i = o.call(x, e);
  }
  const a = r.call(x, e);
  x.set(e, t);
  if (i) {
    if (Ot(t, a)) {
      ut(x, "set", e, t);
    }
  } else {
    ut(x, "add", e, t);
  }
  return this;
}
function wx(e) {
  const n = re(this);
  const {
    has: x,
    get: o
  } = Qn(n);
  let r = x.call(n, e);
  if (!r) {
    e = re(e);
    r = x.call(n, e);
  }
  if (o) {
    o.call(n, e);
  }
  const i = n.delete(e);
  if (r) {
    ut(n, "delete", e, undefined);
  }
  return i;
}
function kx() {
  const t = re(this);
  const n = t.size !== 0;
  const x = t.clear();
  if (n) {
    ut(t, "clear", undefined, undefined);
  }
  return x;
}
function Tn(e, t) {
  return function (x, o) {
    const i = this;
    const a = i.__v_raw;
    const l = re(a);
    const p = t ? W0 : e ? G0 : dn;
    if (!e) {
      Fe(l, "iterate", At);
    }
    return a.forEach((m, v) => x.call(o, p(m), p(v), i));
  };
}
function On(e, t, n) {
  return function (...x) {
    const r = this.__v_raw;
    const i = re(r);
    const a = qt(i);
    const l = e === "entries" || e === Symbol.iterator && a;
    const p = e === "keys" && a;
    const m = r[e](...x);
    const v = n ? W0 : t ? G0 : dn;
    if (!t) {
      Fe(i, "iterate", p ? S0 : At);
    }
    return {
      next() {
        const {
          value: y,
          done: E
        } = m.next();
        if (E) {
          return {
            value: y,
            done: E
          };
        } else {
          return {
            value: l ? [v(y[0]), v(y[1])] : v(y),
            done: E
          };
        }
      },
      [Symbol.iterator]() {
        return this;
      }
    };
  };
}
function mt(e) {
  return function (...t) {
    if (e === "delete") {
      return false;
    } else if (e === "clear") {
      return undefined;
    } else {
      return this;
    }
  };
}
function oi() {
  const t = {
    get(i) {
      return In(this, i);
    },
    get size() {
      return An(this);
    },
    has: En,
    add: vx,
    set: yx,
    delete: wx,
    clear: kx,
    forEach: Tn(false, false)
  };
  const n = {
    get(i) {
      return In(this, i, false, true);
    },
    get size() {
      return An(this);
    },
    has: En,
    add: vx,
    set: yx,
    delete: wx,
    clear: kx,
    forEach: Tn(false, true)
  };
  const x = {
    get(i) {
      return In(this, i, true);
    },
    get size() {
      return An(this, true);
    },
    has(i) {
      return En.call(this, i, true);
    },
    add: mt("add"),
    set: mt("set"),
    delete: mt("delete"),
    clear: mt("clear"),
    forEach: Tn(true, false)
  };
  const o = {
    get(i) {
      return In(this, i, true, true);
    },
    get size() {
      return An(this, true);
    },
    has(i) {
      return En.call(this, i, true);
    },
    add: mt("add"),
    set: mt("set"),
    delete: mt("delete"),
    clear: mt("clear"),
    forEach: Tn(true, true)
  };
  ["keys", "values", "entries", Symbol.iterator].forEach(i => {
    t[i] = On(i, false, false);
    x[i] = On(i, true, false);
    n[i] = On(i, false, true);
    o[i] = On(i, true, true);
  });
  return [t, x, n, o];
}
const [ri, ii, ci, si] = oi();
function K0(e, t) {
  const n = t ? e ? si : ci : e ? ii : ri;
  return (x, o, r) => {
    if (o === "__v_isReactive") {
      return !e;
    } else if (o === "__v_isReadonly") {
      return e;
    } else if (o === "__v_raw") {
      return x;
    } else {
      return Reflect.get(xe(n, o) && o in x ? n : x, o, r);
    }
  };
}
const ai = {
  get: K0(false, false)
};
const li = {
  get: K0(false, true)
};
const ui = {
  get: K0(true, false)
};
const bo = new WeakMap();
const go = new WeakMap();
const vo = new WeakMap();
const fi = new WeakMap();
function di(e) {
  switch (e) {
    case "Object":
    case "Array":
      return 1;
    case "Map":
    case "Set":
    case "WeakMap":
    case "WeakSet":
      return 2;
    default:
      return 0;
  }
}
function pi(e) {
  if (e.__v_skip || !Object.isExtensible(e)) {
    return 0;
  } else {
    return di(Fr(e));
  }
}
function fn(e) {
  if (Kt(e)) {
    return e;
  } else {
    return J0(e, false, ti, ai, bo);
  }
}
function hi(e) {
  return J0(e, false, xi, li, go);
}
function yo(e) {
  return J0(e, true, ni, ui, vo);
}
function J0(e, t, n, x, o) {
  if (!pe(e) || e.__v_raw && !(t && e.__v_isReactive)) {
    return e;
  }
  const i = o.get(e);
  if (i) {
    return i;
  }
  const a = pi(e);
  if (a === 0) {
    return e;
  }
  const l = new Proxy(e, a === 2 ? x : n);
  o.set(e, l);
  return l;
}
function Dt(e) {
  if (Kt(e)) {
    return Dt(e.__v_raw);
  } else {
    return !!(e && e.__v_isReactive);
  }
}
function Kt(e) {
  return !!(e && e.__v_isReadonly);
}
function $n(e) {
  return !!(e && e.__v_isShallow);
}
function wo(e) {
  return Dt(e) || Kt(e);
}
function re(e) {
  const n = e && e.__v_raw;
  if (n) {
    return re(n);
  } else {
    return e;
  }
}
function ko(e) {
  Dn(e, "__v_skip", true);
  return e;
}
const dn = e => pe(e) ? fn(e) : e;
const G0 = e => pe(e) ? yo(e) : e;
function So(e) {
  if (vt && Ke) {
    e = re(e);
    fo(e.dep || (e.dep = $0()));
  }
}
function Co(e, t) {
  e = re(e);
  const n = e.dep;
  if (n) {
    C0(n);
  }
}
function Ae(e) {
  return !!(e && e.__v_isRef === true);
}
function Z(e) {
  return mi(e, false);
}
function mi(e, t) {
  if (Ae(e)) {
    return e;
  } else {
    return new bi(e, t);
  }
}
class bi {
  constructor(t, n) {
    this.__v_isShallow = n;
    this.dep = undefined;
    this.__v_isRef = true;
    this._rawValue = n ? t : re(t);
    this._value = n ? t : dn(t);
  }
  get value() {
    So(this);
    return this._value;
  }
  set value(t) {
    const x = this.__v_isShallow || $n(t) || Kt(t);
    t = x ? t : re(t);
    if (Ot(t, this._rawValue)) {
      this._rawValue = t;
      this._value = x ? t : dn(t);
      Co(this);
    }
  }
}
function gi(e) {
  if (Ae(e)) {
    return e.value;
  } else {
    return e;
  }
}
const vi = {
  get: (e, t, n) => gi(Reflect.get(e, t, n)),
  set: (e, t, n, x) => {
    const r = e[t];
    if (Ae(r) && !Ae(n)) {
      r.value = n;
      return true;
    } else {
      return Reflect.set(e, t, n, x);
    }
  }
};
function Ro(e) {
  if (Dt(e)) {
    return e;
  } else {
    return new Proxy(e, vi);
  }
}
class yi {
  constructor(t, n, x, o) {
    this._setter = n;
    this.dep = undefined;
    this.__v_isRef = true;
    this.__v_isReadonly = false;
    this._dirty = true;
    this.effect = new _0(t, () => {
      if (!this._dirty) {
        this._dirty = true;
        Co(this);
      }
    });
    this.effect.computed = this;
    this.effect.active = this._cacheable = !o;
    this.__v_isReadonly = x;
  }
  get value() {
    const n = re(this);
    So(n);
    if (n._dirty || !n._cacheable) {
      n._dirty = false;
      n._value = n.effect.run();
    }
    return n._value;
  }
  set value(t) {
    this._setter(t);
  }
}
function wi(e, t, n = false) {
  let o;
  let r;
  const i = X(e);
  if (i) {
    o = e;
    r = Ye;
  } else {
    o = e.get;
    r = e.set;
  }
  return new yi(o, r, i || !r, n);
}
function ki(e, ...t) {}
function yt(e, t, n, x) {
  let o;
  try {
    o = x ? e(...x) : e();
  } catch (r) {
    Xn(r, t, n);
  }
  return o;
}
function Ze(e, t, n, x) {
  if (X(e)) {
    const i = yt(e, t, n, x);
    if (i && xo(i)) {
      i.catch(a => {
        Xn(a, t, n);
      });
    }
    return i;
  }
  const r = [];
  for (let i = 0; i < e.length; i++) {
    r.push(Ze(e[i], t, n, x));
  }
  return r;
}
function Xn(e, t, n, x = true) {
  const r = t ? t.vnode : null;
  if (t) {
    let i = t.parent;
    const a = t.proxy;
    const l = n;
    for (; i;) {
      const m = i.ec;
      if (m) {
        for (let v = 0; v < m.length; v++) {
          if (m[v](e, a, l) === false) {
            return;
          }
        }
      }
      i = i.parent;
    }
    const p = t.appContext.config.errorHandler;
    if (p) {
      yt(p, null, 10, [e, a, l]);
      return;
    }
  }
  Si(e, n, r, x);
}
function Si(e, t, n, x = true) {
  console.error(e);
}
let pn = false;
let R0 = false;
const Ee = [];
let xt = 0;
const $t = [];
let lt = null;
let Rt = 0;
const Io = Promise.resolve();
let Y0 = null;
function Ci(e) {
  const n = Y0 || Io;
  if (e) {
    return n.then(this ? e.bind(this) : e);
  } else {
    return n;
  }
}
function Ri(e) {
  let n = xt + 1;
  let x = Ee.length;
  for (; n < x;) {
    const o = n + x >>> 1;
    const r = Ee[o];
    const i = hn(r);
    if (i < e || i === e && r.pre) {
      n = o + 1;
    } else {
      x = o;
    }
  }
  return n;
}
function Z0(e) {
  if (!Ee.length || !Ee.includes(e, pn && e.allowRecurse ? xt + 1 : xt)) {
    if (e.id == null) {
      Ee.push(e);
    } else {
      Ee.splice(Ri(e.id), 0, e);
    }
    Eo();
  }
}
function Eo() {
  if (!pn && !R0) {
    R0 = true;
    Y0 = Io.then(To);
  }
}
function Ii(e) {
  const n = Ee.indexOf(e);
  if (n > xt) {
    Ee.splice(n, 1);
  }
}
function Ei(e) {
  if (Y(e)) {
    $t.push(...e);
  } else if (!lt || !lt.includes(e, e.allowRecurse ? Rt + 1 : Rt)) {
    $t.push(e);
  }
  Eo();
}
function Sx(e, t, n = pn ? xt + 1 : 0) {
  for (; n < Ee.length; n++) {
    const o = Ee[n];
    if (o && o.pre) {
      if (e && o.id !== e.uid) {
        continue;
      }
      Ee.splice(n, 1);
      n--;
      o();
    }
  }
}
function Ao(e) {
  if ($t.length) {
    const n = [...new Set($t)];
    $t.length = 0;
    if (lt) {
      lt.push(...n);
      return;
    }
    lt = n;
    lt.sort((x, o) => hn(x) - hn(o));
    Rt = 0;
    for (; Rt < lt.length; Rt++) {
      lt[Rt]();
    }
    lt = null;
    Rt = 0;
  }
}
const hn = e => e.id == null ? Infinity : e.id;
const Ai = (e, t) => {
  const x = hn(e) - hn(t);
  if (x === 0) {
    if (e.pre && !t.pre) {
      return -1;
    }
    if (t.pre && !e.pre) {
      return 1;
    }
  }
  return x;
};
function To(e) {
  R0 = false;
  pn = true;
  Ee.sort(Ai);
  const n = Ye;
  try {
    for (xt = 0; xt < Ee.length; xt++) {
      const x = Ee[xt];
      if (x && x.active !== false) {
        if ("production" !== "production") {
          n(x);
        }
        yt(x, null, 14);
      }
    }
  } finally {
    xt = 0;
    Ee.length = 0;
    Ao();
    pn = false;
    Y0 = null;
    if (Ee.length || $t.length) {
      To();
    }
  }
}
function Ti(e, t, ...n) {
  if (e.isUnmounted) {
    return;
  }
  const o = e.vnode.props || ue;
  let r = n;
  const i = t.startsWith("update:");
  const a = i && t.slice(7);
  if (a && a in o) {
    const v = (a === "modelValue" ? "model" : a) + "Modifiers";
    const {
      number: y,
      trim: E
    } = o[v] || ue;
    if (E) {
      r = n.map(O => ye(O) ? O.trim() : O);
    }
    if (y) {
      r = n.map(zr);
    }
  }
  let l;
  let p = o[l = u0(t)] || o[l = u0(Wt(t))];
  if (!p && i) {
    p = o[l = u0(Zt(t))];
  }
  if (p) {
    Ze(p, e, 6, r);
  }
  const m = o[l + "Once"];
  if (m) {
    if (!e.emitted) {
      e.emitted = {};
    } else if (e.emitted[l]) {
      return;
    }
    e.emitted[l] = true;
    Ze(m, e, 6, r);
  }
}
function Oo(e, t, n = false) {
  const o = t.emitsCache;
  const r = o.get(e);
  if (r !== undefined) {
    return r;
  }
  const i = e.emits;
  let a = {};
  let l = false;
  if (!X(e)) {
    const p = m => {
      const v = Oo(m, t, true);
      if (v) {
        l = true;
        Re(a, v);
      }
    };
    if (!n && t.mixins.length) {
      t.mixins.forEach(p);
    }
    if (e.extends) {
      p(e.extends);
    }
    if (e.mixins) {
      e.mixins.forEach(p);
    }
  }
  if (!i && !l) {
    if (pe(e)) {
      o.set(e, null);
    }
    return null;
  } else {
    if (Y(i)) {
      i.forEach(p => a[p] = null);
    } else {
      Re(a, i);
    }
    if (pe(e)) {
      o.set(e, a);
    }
    return a;
  }
}
function e0(e, t) {
  if (!e || !Gn(t)) {
    return false;
  } else {
    t = t.slice(2).replace(/Once$/, "");
    return xe(e, t[0].toLowerCase() + t.slice(1)) || xe(e, Zt(t)) || xe(e, t);
  }
}
let Je = null;
let Uo = null;
function Wn(e) {
  const n = Je;
  Je = e;
  Uo = e && e.type.__scopeId || null;
  return n;
}
function Oi(e, t = Je, n) {
  if (!t || e._n) {
    return e;
  }
  const x = (...o) => {
    if (x._d) {
      jx(-1);
    }
    const r = Wn(t);
    let i;
    try {
      i = e(...o);
    } finally {
      Wn(r);
      if (x._d) {
        jx(1);
      }
    }
    return i;
  };
  x._n = true;
  x._c = true;
  x._d = true;
  return x;
}
function p0(e) {
  const {
    type: n,
    vnode: x,
    proxy: o,
    withProxy: r,
    props: i,
    propsOptions: [a],
    slots: l,
    attrs: p,
    emit: m,
    render: v,
    renderCache: y,
    data: E,
    setupState: O,
    ctx: q,
    inheritAttrs: N
  } = e;
  let D;
  let G;
  const oe = Wn(e);
  try {
    if (x.shapeFlag & 4) {
      const K = r || o;
      const de = "production" !== "production" && O.__isScriptSetup ? new Proxy(K, {
        get(je, ve, Te) {
          ki("Property '" + String(ve) + "' was accessed via 'this'. Avoid using 'this' in templates.");
          return Reflect.get(je, ve, Te);
        }
      }) : K;
      D = nt(v.call(de, K, y, i, O, E, q));
      G = p;
    } else {
      const K = n;
      "production";
      D = nt(K.length > 1 ? K(i, {
        attrs: p,
        slots: l,
        emit: m
      }) : K(i, null));
      G = n.props ? p : Ui(p);
    }
  } catch (K) {
    un.length = 0;
    Xn(K, e, 1);
    D = ot(Ut);
  }
  let fe = D;
  if (G && N !== false) {
    const K = Object.keys(G);
    const {
      shapeFlag: de
    } = fe;
    if (K.length && de & 7) {
      if (a && K.some(z0)) {
        G = Pi(G, a);
      }
      fe = Jt(fe, G);
    }
  }
  if (x.dirs) {
    fe = Jt(fe);
    fe.dirs = fe.dirs ? fe.dirs.concat(x.dirs) : x.dirs;
  }
  if (x.transition) {
    fe.transition = x.transition;
  }
  D = fe;
  Wn(oe);
  return D;
}
const Ui = e => {
  let n;
  for (const x in e) {
    if (x === "class" || x === "style" || Gn(x)) {
      (n || (n = {}))[x] = e[x];
    }
  }
  return n;
};
const Pi = (e, t) => {
  const n = {};
  for (const x in e) {
    if (!z0(x) || !(x.slice(9) in t)) {
      n[x] = e[x];
    }
  }
  return n;
};
function ji(e, t, n) {
  const {
    props: o,
    children: r,
    component: i
  } = e;
  const {
    props: a,
    children: l,
    patchFlag: p
  } = t;
  const m = i.emitsOptions;
  if (t.dirs || t.transition) {
    return true;
  }
  if (n && p >= 0) {
    if (p & 1024) {
      return true;
    }
    if (p & 16) {
      if (o) {
        return Cx(o, a, m);
      } else {
        return !!a;
      }
    }
    if (p & 8) {
      const v = t.dynamicProps;
      for (let y = 0; y < v.length; y++) {
        const E = v[y];
        if (a[E] !== o[E] && !e0(m, E)) {
          return true;
        }
      }
    }
  } else if ((r || l) && (!l || !l.$stable)) {
    return true;
  } else if (o === a) {
    return false;
  } else if (o) {
    if (a) {
      return Cx(o, a, m);
    } else {
      return true;
    }
  } else {
    return !!a;
  }
  return false;
}
function Cx(e, t, n) {
  const o = Object.keys(t);
  if (o.length !== Object.keys(e).length) {
    return true;
  }
  for (let r = 0; r < o.length; r++) {
    const i = o[r];
    if (t[i] !== e[i] && !e0(n, i)) {
      return true;
    }
  }
  return false;
}
function Ni({
              vnode: e,
              parent: t
            }, n) {
  for (; t && t.subTree === e;) {
    (e = t.vnode).el = n;
    t = t.parent;
  }
}
const Mi = Symbol.for("v-ndc");
const Fi = e => e.__isSuspense;
function Bi(e, t) {
  if (t && t.pendingBranch) {
    if (Y(e)) {
      t.effects.push(...e);
    } else {
      t.effects.push(e);
    }
  } else {
    Ei(e);
  }
}
const Un = {};
function h0(e, t, n) {
  return Po(e, t, n);
}
function Po(e, t, {
  immediate: n,
  deep: x,
  flush: o,
  onTrack: r,
  onTrigger: i
} = ue) {
  var l;
  const p = Jr() === ((l = Ce) == null ? undefined : l.scope) ? Ce : null;
  let m;
  let v = false;
  let y = false;
  if (Ae(e)) {
    m = () => e.value;
    v = $n(e);
  } else if (Dt(e)) {
    m = () => e;
    x = true;
  } else if (Y(e)) {
    y = true;
    v = e.some(K => Dt(K) || $n(K));
    m = () => e.map(K => {
      if (Ae(K)) {
        return K.value;
      }
      if (Dt(K)) {
        return Et(K);
      }
      if (X(K)) {
        return yt(K, p, 2);
      }
    });
  } else if (X(e)) {
    if (t) {
      m = () => yt(e, p, 2);
    } else {
      m = () => {
        if (!(p && p.isUnmounted)) {
          if (E) {
            E();
          }
          return Ze(e, p, 3, [O]);
        }
      };
    }
  } else {
    m = Ye;
  }
  if (t && x) {
    const K = m;
    m = () => Et(K());
  }
  let E;
  let O = K => {
    E = oe.onStop = () => {
      yt(K, p, 4);
      E = oe.onStop = undefined;
    };
  };
  let q;
  if (bn) {
    O = Ye;
    if (t) {
      if (n) {
        Ze(t, p, 3, [m(), y ? [] : undefined, O]);
      }
    } else {
      m();
    }
    if (o === "sync") {
      const K = Nc();
      q = K.__watcherHandles || (K.__watcherHandles = []);
    } else {
      return Ye;
    }
  }
  let N = y ? new Array(e.length).fill(Un) : Un;
  const D = () => {
    if (oe.active) {
      if (t) {
        const de = oe.run();
        if (x || v || (y ? de.some((je, ve) => Ot(je, N[ve])) : Ot(de, N)) || false) {
          if (E) {
            E();
          }
          Ze(t, p, 3, [de, N === Un ? undefined : y && N[0] === Un ? [] : N, O]);
          N = de;
        }
      } else {
        oe.run();
      }
    }
  };
  D.allowRecurse = !!t;
  let G;
  if (o === "sync") {
    G = D;
  } else if (o === "post") {
    G = () => Me(D, p && p.suspense);
  } else {
    D.pre = true;
    if (p) {
      D.id = p.uid;
    }
    G = () => Z0(D);
  }
  const oe = new _0(m, G);
  if (t) {
    if (n) {
      D();
    } else {
      N = oe.run();
    }
  } else if (o === "post") {
    Me(oe.run.bind(oe), p && p.suspense);
  } else {
    oe.run();
  }
  const fe = () => {
    oe.stop();
    if (p && p.scope) {
      L0(p.scope.effects, oe);
    }
  };
  if (q) {
    q.push(fe);
  }
  return fe;
}
function Vi(e, t, n) {
  const o = this.proxy;
  const r = ye(e) ? e.includes(".") ? jo(o, e) : () => o[e] : e.bind(o, o);
  let i;
  if (X(t)) {
    i = t;
  } else {
    i = t.handler;
    n = t;
  }
  const a = Ce;
  Gt(this);
  const l = Po(r, i.bind(o), n);
  if (a) {
    Gt(a);
  } else {
    Tt();
  }
  return l;
}
function jo(e, t) {
  const x = t.split(".");
  return () => {
    let r = e;
    for (let i = 0; i < x.length && r; i++) {
      r = r[x[i]];
    }
    return r;
  };
}
function Et(e, t) {
  if (!pe(e) || e.__v_skip || (t = t || new Set(), t.has(e))) {
    return e;
  }
  t.add(e);
  if (Ae(e)) {
    Et(e.value, t);
  } else if (Y(e)) {
    for (let x = 0; x < e.length; x++) {
      Et(e[x], t);
    }
  } else if (no(e) || qt(e)) {
    e.forEach(x => {
      Et(x, t);
    });
  } else if (ro(e)) {
    for (const x in e) {
      Et(e[x], t);
    }
  }
  return e;
}
function Pn(e, t) {
  const x = Je;
  if (x === null) {
    return e;
  }
  const o = o0(x) || x.proxy;
  const r = e.dirs || (e.dirs = []);
  for (let i = 0; i < t.length; i++) {
    let [a, l, p, m = ue] = t[i];
    if (a) {
      if (X(a)) {
        a = {
          mounted: a,
          updated: a
        };
      }
      if (a.deep) {
        Et(l);
      }
      r.push({
        dir: a,
        instance: o,
        value: l,
        oldValue: undefined,
        arg: p,
        modifiers: m
      });
    }
  }
  return e;
}
function St(e, t, n, x) {
  const r = e.dirs;
  const i = t && t.dirs;
  for (let a = 0; a < r.length; a++) {
    const l = r[a];
    if (i) {
      l.oldValue = i[a].value;
    }
    let p = l.dir[x];
    if (p) {
      Qt();
      Ze(p, n, 8, [e.el, l, e, t]);
      Xt();
    }
  }
}
const qe = [Function, Array];
const zi = {
  mode: String,
  appear: Boolean,
  persisted: Boolean,
  onBeforeEnter: qe,
  onEnter: qe,
  onAfterEnter: qe,
  onEnterCancelled: qe,
  onBeforeLeave: qe,
  onLeave: qe,
  onAfterLeave: qe,
  onLeaveCancelled: qe,
  onBeforeAppear: qe,
  onAppear: qe,
  onAfterAppear: qe,
  onAppearCancelled: qe
};
const Vn = e => !!e.type.__asyncLoader;
const No = e => e.type.__isKeepAlive;
function Li(e, t) {
  Mo(e, "a", t);
}
function Hi(e, t) {
  Mo(e, "da", t);
}
function Mo(e, t, n = Ce) {
  const o = e.__wdc || (e.__wdc = () => {
    let i = n;
    for (; i;) {
      if (i.isDeactivated) {
        return;
      }
      i = i.parent;
    }
    return e();
  });
  t0(t, o, n);
  if (n) {
    let r = n.parent;
    for (; r && r.parent;) {
      if (No(r.parent.vnode)) {
        qi(o, t, n, r);
      }
      r = r.parent;
    }
  }
}
function qi(e, t, n, x) {
  const o = t0(t, e, x, true);
  Bo(() => {
    L0(x[t], o);
  }, n);
}
function t0(e, t, n = Ce, x = false) {
  if (n) {
    const r = n[e] || (n[e] = []);
    const i = t.__weh || (t.__weh = (...a) => {
      if (n.isUnmounted) {
        return;
      }
      Qt();
      Gt(n);
      const p = Ze(t, n, e, a);
      Tt();
      Xt();
      return p;
    });
    if (x) {
      r.unshift(i);
    } else {
      r.push(i);
    }
    return i;
  }
}
const ft = e => (t, n = Ce) => (!bn || e === "sp") && t0(e, (...x) => t(...x), n);
const Di = ft("bm");
const Fo = ft("m");
const $i = ft("bu");
const _i = ft("u");
const Wi = ft("bum");
const Bo = ft("um");
const Ki = ft("sp");
const Ji = ft("rtg");
const Gi = ft("rtc");
function Yi(e, t = Ce) {
  t0("ec", e, t);
}
const I0 = e => {
  if (e) {
    if (Jo(e)) {
      return o0(e) || e.proxy;
    } else {
      return I0(e.parent);
    }
  } else {
    return null;
  }
};
const ln = Re(Object.create(null), {
  $: e => e,
  $el: e => e.vnode.el,
  $data: e => e.data,
  $props: e => e.props,
  $attrs: e => e.attrs,
  $slots: e => e.slots,
  $refs: e => e.refs,
  $parent: e => I0(e.parent),
  $root: e => I0(e.root),
  $emit: e => e.emit,
  $options: e => Q0(e),
  $forceUpdate: e => e.f || (e.f = () => Z0(e.update)),
  $nextTick: e => e.n || (e.n = Ci.bind(e.proxy)),
  $watch: e => Vi.bind(e)
});
const m0 = (e, t) => e !== ue && !e.__isScriptSetup && xe(e, t);
const Zi = {
  get({
        _: e
      }, t) {
    const {
      ctx: x,
      setupState: o,
      data: r,
      props: i,
      accessCache: a,
      type: l,
      appContext: p
    } = e;
    let m;
    if (t[0] !== "$") {
      const O = a[t];
      if (O !== undefined) {
        switch (O) {
          case 1:
            return o[t];
          case 2:
            return r[t];
          case 4:
            return x[t];
          case 3:
            return i[t];
        }
      } else {
        if (m0(o, t)) {
          a[t] = 1;
          return o[t];
        }
        if (r !== ue && xe(r, t)) {
          a[t] = 2;
          return r[t];
        }
        if ((m = e.propsOptions[0]) && xe(m, t)) {
          a[t] = 3;
          return i[t];
        }
        if (x !== ue && xe(x, t)) {
          a[t] = 4;
          return x[t];
        }
        if (E0) {
          a[t] = 0;
        }
      }
    }
    const v = ln[t];
    let y;
    let E;
    if (v) {
      if (t === "$attrs") {
        Fe(e, "get", t);
      }
      return v(e);
    }
    if ((y = l.__cssModules) && (y = y[t])) {
      return y;
    }
    if (x !== ue && xe(x, t)) {
      a[t] = 4;
      return x[t];
    }
    E = p.config.globalProperties;
    if (xe(E, t)) {
      return E[t];
    }
  },
  set({
        _: e
      }, t, n) {
    const {
      data: x,
      setupState: o,
      ctx: r
    } = e;
    if (m0(o, t)) {
      o[t] = n;
      return true;
    } else if (x !== ue && xe(x, t)) {
      x[t] = n;
      return true;
    } else if (xe(e.props, t)) {
      return false;
    } else if (t[0] === "$" && t.slice(1) in e) {
      return false;
    } else {
      r[t] = n;
      return true;
    }
  },
  has({
        _: {
          data: e,
          setupState: t,
          accessCache: n,
          ctx: x,
          appContext: o,
          propsOptions: r
        }
      }, i) {
    let l;
    return !!n[i] || e !== ue && xe(e, i) || m0(t, i) || (l = r[0]) && xe(l, i) || xe(x, i) || xe(ln, i) || xe(o.config.globalProperties, i);
  },
  defineProperty(e, t, n) {
    if (n.get != null) {
      e._.accessCache[t] = 0;
    } else if (xe(n, "value")) {
      this.set(e, t, n.value, null);
    }
    return Reflect.defineProperty(e, t, n);
  }
};
function Rx(e) {
  if (Y(e)) {
    return e.reduce((n, x) => {
      n[x] = null;
      return n;
    }, {});
  } else {
    return e;
  }
}
let E0 = true;
function Qi(e) {
  const n = Q0(e);
  const x = e.proxy;
  const o = e.ctx;
  E0 = false;
  if (n.beforeCreate) {
    Ix(n.beforeCreate, e, "bc");
  }
  const {
    data: r,
    computed: i,
    methods: a,
    watch: l,
    provide: p,
    inject: m,
    created: v,
    beforeMount: y,
    mounted: E,
    beforeUpdate: O,
    updated: q,
    activated: N,
    deactivated: D,
    beforeDestroy: G,
    beforeUnmount: oe,
    destroyed: fe,
    unmounted: K,
    render: de,
    renderTracked: je,
    renderTriggered: ve,
    errorCaptured: Te,
    serverPrefetch: De,
    expose: Be,
    inheritAttrs: Qe,
    components: $e,
    directives: rt,
    filters: dt
  } = n;
  if (m) {
    Xi(m, o, null);
  }
  if (a) {
    for (const ie in a) {
      const te = a[ie];
      if (X(te)) {
        o[ie] = te.bind(x);
      }
    }
  }
  if (r) {
    const ie = r.call(x, x);
    if (pe(ie)) {
      e.data = fn(ie);
    }
  }
  E0 = true;
  if (i) {
    for (const ie in i) {
      const te = i[ie];
      const He = X(te) ? te.bind(x, x) : X(te.get) ? te.get.bind(x, x) : Ye;
      const Oe = !X(te) && X(te.set) ? te.set.bind(x) : Ye;
      const V = Pc({
        get: He,
        set: Oe
      });
      Object.defineProperty(o, ie, {
        enumerable: true,
        configurable: true,
        get: () => V.value,
        set: b => V.value = b
      });
    }
  }
  if (l) {
    for (const ie in l) {
      Vo(l[ie], o, x, ie);
    }
  }
  if (p) {
    const ie = X(p) ? p.call(x) : p;
    Reflect.ownKeys(ie).forEach(te => {
      rc(te, ie[te]);
    });
  }
  if (v) {
    Ix(v, e, "c");
  }
  function he(ie, te) {
    if (Y(te)) {
      te.forEach(Oe => ie(Oe.bind(x)));
    } else if (te) {
      ie(te.bind(x));
    }
  }
  he(Di, y);
  he(Fo, E);
  he($i, O);
  he(_i, q);
  he(Li, N);
  he(Hi, D);
  he(Yi, Te);
  he(Gi, je);
  he(Ji, ve);
  he(Wi, oe);
  he(Bo, K);
  he(Ki, De);
  if (Y(Be)) {
    if (Be.length) {
      const ie = e.exposed || (e.exposed = {});
      Be.forEach(te => {
        Object.defineProperty(ie, te, {
          get: () => x[te],
          set: Oe => x[te] = Oe
        });
      });
    } else if (!e.exposed) {
      e.exposed = {};
    }
  }
  if (de && e.render === Ye) {
    e.render = de;
  }
  if (Qe != null) {
    e.inheritAttrs = Qe;
  }
  if ($e) {
    e.components = $e;
  }
  if (rt) {
    e.directives = rt;
  }
}
function Xi(e, t, n = Ye) {
  if (Y(e)) {
    e = A0(e);
  }
  for (const o in e) {
    const r = e[o];
    let i;
    if (pe(r)) {
      if ("default" in r) {
        i = zn(r.from || o, r.default, true);
      } else {
        i = zn(r.from || o);
      }
    } else {
      i = zn(r);
    }
    if (Ae(i)) {
      Object.defineProperty(t, o, {
        enumerable: true,
        configurable: true,
        get: () => i.value,
        set: a => i.value = a
      });
    } else {
      t[o] = i;
    }
  }
}
function Ix(e, t, n) {
  Ze(Y(e) ? e.map(o => o.bind(t.proxy)) : e.bind(t.proxy), t, n);
}
function Vo(e, t, n, x) {
  const r = x.includes(".") ? jo(n, x) : () => n[x];
  if (ye(e)) {
    const i = t[e];
    if (X(i)) {
      h0(r, i);
    }
  } else if (X(e)) {
    h0(r, e.bind(n));
  } else if (pe(e)) {
    if (Y(e)) {
      e.forEach(i => Vo(i, t, n, x));
    } else {
      const i = X(e.handler) ? e.handler.bind(n) : t[e.handler];
      if (X(i)) {
        h0(r, i, e);
      }
    }
  }
}
function Q0(e) {
  const n = e.type;
  const {
    mixins: x,
    extends: o
  } = n;
  const {
    mixins: r,
    optionsCache: i,
    config: {
      optionMergeStrategies: a
    }
  } = e.appContext;
  const l = i.get(n);
  let p;
  if (l) {
    p = l;
  } else if (!r.length && !x && !o) {
    p = n;
  } else {
    p = {};
    if (r.length) {
      r.forEach(m => Kn(p, m, a, true));
    }
    Kn(p, n, a);
  }
  if (pe(n)) {
    i.set(n, p);
  }
  return p;
}
function Kn(e, t, n, x = false) {
  const {
    mixins: r,
    extends: i
  } = t;
  if (i) {
    Kn(e, i, n, true);
  }
  if (r) {
    r.forEach(a => Kn(e, a, n, true));
  }
  for (const a in t) {
    if (!(x && a === "expose")) {
      const l = ec[a] || n && n[a];
      e[a] = l ? l(e[a], t[a]) : t[a];
    }
  }
  return e;
}
const ec = {
  data: Ex,
  props: Ax,
  emits: Ax,
  methods: sn,
  computed: sn,
  beforeCreate: Pe,
  created: Pe,
  beforeMount: Pe,
  mounted: Pe,
  beforeUpdate: Pe,
  updated: Pe,
  beforeDestroy: Pe,
  beforeUnmount: Pe,
  destroyed: Pe,
  unmounted: Pe,
  activated: Pe,
  deactivated: Pe,
  errorCaptured: Pe,
  serverPrefetch: Pe,
  components: sn,
  directives: sn,
  watch: nc,
  provide: Ex,
  inject: tc
};
function Ex(e, t) {
  if (t) {
    if (e) {
      return function () {
        return Re(X(e) ? e.call(this, this) : e, X(t) ? t.call(this, this) : t);
      };
    } else {
      return t;
    }
  } else {
    return e;
  }
}
function tc(e, t) {
  return sn(A0(e), A0(t));
}
function A0(e) {
  if (Y(e)) {
    const n = {};
    for (let x = 0; x < e.length; x++) {
      n[e[x]] = e[x];
    }
    return n;
  }
  return e;
}
function Pe(e, t) {
  if (e) {
    return [...new Set([].concat(e, t))];
  } else {
    return t;
  }
}
function sn(e, t) {
  if (e) {
    return Re(Object.create(null), e, t);
  } else {
    return t;
  }
}
function Ax(e, t) {
  if (e) {
    if (Y(e) && Y(t)) {
      return [...new Set([...e, ...t])];
    } else {
      return Re(Object.create(null), Rx(e), Rx(t != null ? t : {}));
    }
  } else {
    return t;
  }
}
function nc(e, t) {
  if (!e) {
    return t;
  }
  if (!t) {
    return e;
  }
  const x = Re(Object.create(null), e);
  for (const o in t) {
    x[o] = Pe(e[o], t[o]);
  }
  return x;
}
function zo() {
  return {
    app: null,
    config: {
      isNativeTag: Nr,
      performance: false,
      globalProperties: {},
      optionMergeStrategies: {},
      errorHandler: undefined,
      warnHandler: undefined,
      compilerOptions: {}
    },
    mixins: [],
    components: {},
    directives: {},
    provides: Object.create(null),
    optionsCache: new WeakMap(),
    propsCache: new WeakMap(),
    emitsCache: new WeakMap()
  };
}
let xc = 0;
function oc(e, t) {
  return function (x, o = null) {
    if (!X(x)) {
      x = Re({}, x);
    }
    if (o != null && !pe(o)) {
      o = null;
    }
    const i = zo();
    const a = new WeakSet();
    let l = false;
    const p = i.app = {
      _uid: xc++,
      _component: x,
      _props: o,
      _container: null,
      _context: i,
      _instance: null,
      version: Mc,
      get config() {
        return i.config;
      },
      set config(m) {},
      use(m, ...v) {
        if (!a.has(m)) {
          if (m && X(m.install)) {
            a.add(m);
            m.install(p, ...v);
          } else if (X(m)) {
            a.add(m);
            m(p, ...v);
          }
        }
        return p;
      },
      mixin(m) {
        if (!i.mixins.includes(m)) {
          i.mixins.push(m);
        }
        return p;
      },
      component(m, v) {
        if (v) {
          i.components[m] = v;
          return p;
        } else {
          return i.components[m];
        }
      },
      directive(m, v) {
        if (v) {
          i.directives[m] = v;
          return p;
        } else {
          return i.directives[m];
        }
      },
      mount(m, v, y) {
        if (!l) {
          const O = ot(x, o);
          O.appContext = i;
          if (v && t) {
            t(O, m);
          } else {
            e(O, m, y);
          }
          l = true;
          p._container = m;
          m.__vue_app__ = p;
          return o0(O.component) || O.component.proxy;
        }
      },
      unmount() {
        if (l) {
          e(null, p._container);
          delete p._container.__vue_app__;
        }
      },
      provide(m, v) {
        i.provides[m] = v;
        return p;
      },
      runWithContext(m) {
        Jn = p;
        try {
          return m();
        } finally {
          Jn = null;
        }
      }
    };
    return p;
  };
}
let Jn = null;
function rc(e, t) {
  if (Ce) {
    let x = Ce.provides;
    const o = Ce.parent && Ce.parent.provides;
    if (o === x) {
      x = Ce.provides = Object.create(o);
    }
    x[e] = t;
  }
}
function zn(e, t, n = false) {
  const o = Ce || Je;
  if (o || Jn) {
    const r = o ? o.parent == null ? o.vnode.appContext && o.vnode.appContext.provides : o.parent.provides : Jn._context.provides;
    if (r && e in r) {
      return r[e];
    }
    if (arguments.length > 1) {
      if (n && X(t)) {
        return t.call(o && o.proxy);
      } else {
        return t;
      }
    }
  }
}
function ic(e, t, n, x = false) {
  const r = {};
  const i = {};
  Dn(i, x0, 1);
  e.propsDefaults = Object.create(null);
  Lo(e, t, r, i);
  for (const a in e.propsOptions[0]) {
    if (!(a in r)) {
      r[a] = undefined;
    }
  }
  if (n) {
    e.props = x ? r : hi(r);
  } else if (e.type.props) {
    e.props = r;
  } else {
    e.props = i;
  }
  e.attrs = i;
}
function cc(e, t, n, x) {
  const {
    props: r,
    attrs: i,
    vnode: {
      patchFlag: a
    }
  } = e;
  const l = re(r);
  const [p] = e.propsOptions;
  let m = false;
  if ((x || a > 0) && !(a & 16)) {
    if (a & 8) {
      const v = e.vnode.dynamicProps;
      for (let y = 0; y < v.length; y++) {
        let E = v[y];
        if (e0(e.emitsOptions, E)) {
          continue;
        }
        const O = t[E];
        if (p) {
          if (xe(i, E)) {
            if (O !== i[E]) {
              i[E] = O;
              m = true;
            }
          } else {
            const q = Wt(E);
            r[q] = T0(p, l, q, O, e, false);
          }
        } else if (O !== i[E]) {
          i[E] = O;
          m = true;
        }
      }
    }
  } else {
    if (Lo(e, t, r, i)) {
      m = true;
    }
    let v;
    for (const y in l) {
      if (!t || !xe(t, y) && ((v = Zt(y)) === y || !xe(t, v))) {
        if (p) {
          if (n && (n[y] !== undefined || n[v] !== undefined)) {
            r[y] = T0(p, l, y, undefined, e, true);
          }
        } else {
          delete r[y];
        }
      }
    }
    if (i !== l) {
      for (const y in i) {
        if (!t || !xe(t, y) && true) {
          delete i[y];
          m = true;
        }
      }
    }
  }
  if (m) {
    ut(e, "set", "$attrs");
  }
}
function Lo(e, t, n, x) {
  const [r, i] = e.propsOptions;
  let a = false;
  let l;
  if (t) {
    for (let p in t) {
      if (Bn(p)) {
        continue;
      }
      const m = t[p];
      let v;
      if (r && xe(r, v = Wt(p))) {
        if (!i || !i.includes(v)) {
          n[v] = m;
        } else {
          (l || (l = {}))[v] = m;
        }
      } else if (!e0(e.emitsOptions, p) && (!(p in x) || m !== x[p])) {
        x[p] = m;
        a = true;
      }
    }
  }
  if (i) {
    const p = re(n);
    const m = l || ue;
    for (let v = 0; v < i.length; v++) {
      const y = i[v];
      n[y] = T0(r, p, y, m[y], e, !xe(m, y));
    }
  }
  return a;
}
function T0(e, t, n, x, o, r) {
  const a = e[n];
  if (a != null) {
    const l = xe(a, "default");
    if (l && x === undefined) {
      const p = a.default;
      if (a.type !== Function && !a.skipFactory && X(p)) {
        const {
          propsDefaults: m
        } = o;
        if (n in m) {
          x = m[n];
        } else {
          Gt(o);
          x = m[n] = p.call(null, t);
          Tt();
        }
      } else {
        x = p;
      }
    }
    if (a[0]) {
      if (r && !l) {
        x = false;
      } else if (a[1] && (x === "" || x === Zt(n))) {
        x = true;
      }
    }
  }
  return x;
}
function Ho(e, t, n = false) {
  const o = t.propsCache;
  const r = o.get(e);
  if (r) {
    return r;
  }
  const i = e.props;
  const a = {};
  const l = [];
  let p = false;
  if (!X(e)) {
    const v = y => {
      p = true;
      const [O, q] = Ho(y, t, true);
      Re(a, O);
      if (q) {
        l.push(...q);
      }
    };
    if (!n && t.mixins.length) {
      t.mixins.forEach(v);
    }
    if (e.extends) {
      v(e.extends);
    }
    if (e.mixins) {
      e.mixins.forEach(v);
    }
  }
  if (!i && !p) {
    if (pe(e)) {
      o.set(e, Ht);
    }
    return Ht;
  }
  if (Y(i)) {
    for (let v = 0; v < i.length; v++) {
      const y = Wt(i[v]);
      if (Tx(y)) {
        a[y] = ue;
      }
    }
  } else if (i) {
    for (const v in i) {
      const y = Wt(v);
      if (Tx(y)) {
        const E = i[v];
        const O = a[y] = Y(E) || X(E) ? {
          type: E
        } : Re({}, E);
        if (O) {
          const q = Px(Boolean, O.type);
          const N = Px(String, O.type);
          O[0] = q > -1;
          O[1] = N < 0 || q < N;
          if (q > -1 || xe(O, "default")) {
            l.push(y);
          }
        }
      }
    }
  }
  const m = [a, l];
  if (pe(e)) {
    o.set(e, m);
  }
  return m;
}
function Tx(e) {
  if (e[0] !== "$") {
    return true;
  } else {
    return false;
  }
}
function Ox(e) {
  const n = e && e.toString().match(/^\s*(function|class) (\w+)/);
  if (n) {
    return n[2];
  } else if (e === null) {
    return "null";
  } else {
    return "";
  }
}
function Ux(e, t) {
  return Ox(e) === Ox(t);
}
function Px(e, t) {
  if (Y(t)) {
    return t.findIndex(n => Ux(n, e));
  } else if (X(t) && Ux(t, e)) {
    return 0;
  } else {
    return -1;
  }
}
const qo = e => e[0] === "_" || e === "$stable";
const X0 = e => Y(e) ? e.map(nt) : [nt(e)];
const sc = (e, t, n) => {
  if (t._n) {
    return t;
  }
  const x = Oi((...o) => {
    "production";
    "production";

    return X0(t(...o));
  }, n);
  x._c = false;
  return x;
};
const Do = (e, t, n) => {
  const o = e._ctx;
  for (const r in e) {
    if (qo(r)) {
      continue;
    }
    const i = e[r];
    if (X(i)) {
      t[r] = sc(r, i, o);
    } else if (i != null) {
      const a = X0(i);
      t[r] = () => a;
    }
  }
};
const $o = (e, t) => {
  const x = X0(t);
  e.slots.default = () => x;
};
const ac = (e, t) => {
  if (e.vnode.shapeFlag & 32) {
    const x = t._;
    if (x) {
      e.slots = re(t);
      Dn(t, "_", x);
    } else {
      Do(t, e.slots = {});
    }
  } else {
    e.slots = {};
    if (t) {
      $o(e, t);
    }
  }
  Dn(e.slots, x0, 1);
};
const lc = (e, t, n) => {
  const {
    vnode: o,
    slots: r
  } = e;
  let i = true;
  let a = ue;
  if (o.shapeFlag & 32) {
    const l = t._;
    if (l) {
      if (n && l === 1) {
        i = false;
      } else {
        Re(r, t);
        if (!n && l === 1) {
          delete r._;
        }
      }
    } else {
      i = !t.$stable;
      Do(t, r);
    }
    a = t;
  } else if (t) {
    $o(e, t);
    a = {
      default: 1
    };
  }
  if (i) {
    for (const l in r) {
      if (!qo(l) && a[l] == null) {
        delete r[l];
      }
    }
  }
};
function O0(e, t, n, x, o = false) {
  if (Y(e)) {
    e.forEach((E, O) => O0(E, t && (Y(t) ? t[O] : t), n, x, o));
    return;
  }
  if (Vn(x) && !o) {
    return;
  }
  const i = x.shapeFlag & 4 ? o0(x.component) || x.component.proxy : x.el;
  const a = o ? null : i;
  const {
    i: l,
    r: p
  } = e;
  const m = t && t.r;
  const v = l.refs === ue ? l.refs = {} : l.refs;
  const y = l.setupState;
  if (m != null && m !== p) {
    if (ye(m)) {
      v[m] = null;
      if (xe(y, m)) {
        y[m] = null;
      }
    } else if (Ae(m)) {
      m.value = null;
    }
  }
  if (X(p)) {
    yt(p, l, 12, [a, v]);
  } else {
    const E = ye(p);
    const O = Ae(p);
    if (E || O) {
      const q = () => {
        if (e.f) {
          const D = E ? xe(y, p) ? y[p] : v[p] : p.value;
          if (o) {
            if (Y(D)) {
              L0(D, i);
            }
          } else if (Y(D)) {
            if (!D.includes(i)) {
              D.push(i);
            }
          } else if (E) {
            v[p] = [i];
            if (xe(y, p)) {
              y[p] = v[p];
            }
          } else {
            p.value = [i];
            if (e.k) {
              v[e.k] = p.value;
            }
          }
        } else if (E) {
          v[p] = a;
          if (xe(y, p)) {
            y[p] = a;
          }
        } else if (O) {
          p.value = a;
          if (e.k) {
            v[e.k] = a;
          }
        }
      };
      if (a) {
        q.id = -1;
        Me(q, n);
      } else {
        q();
      }
    }
  }
}
const Me = Bi;
function uc(e) {
  return fc(e);
}
function fc(e, t) {
  const x = y0();
  x.__VUE__ = true;
  const {
    insert: o,
    remove: r,
    patchProp: i,
    createElement: a,
    createText: l,
    createComment: p,
    setText: m,
    setElementText: v,
    parentNode: y,
    nextSibling: E,
    setScopeId: O = Ye,
    insertStaticContent: q
  } = e;
  const N = (d, h, g, w = null, k = null, S = null, A = false, C = null, R = !!h.dynamicChildren) => {
    if (d === h) {
      return;
    }
    if (d && !on(d, h)) {
      w = H(d);
      b(d, k, S, true);
      d = null;
    }
    if (h.patchFlag === -2) {
      R = false;
      h.dynamicChildren = null;
    }
    const {
      type: I,
      ref: z,
      shapeFlag: U
    } = h;
    switch (I) {
      case n0:
        D(d, h, g, w);
        break;
      case Ut:
        G(d, h, g, w);
        break;
      case Ln:
        if (d == null) {
          oe(h, g, w, A);
        }
        break;
      case tt:
        $e(d, h, g, w, k, S, A, C, R);
        break;
      default:
        if (U & 1) {
          de(d, h, g, w, k, S, A, C, R);
        } else if (U & 6) {
          rt(d, h, g, w, k, S, A, C, R);
        } else if (U & 64) {
          I.process(d, h, g, w, k, S, A, C, R, be);
        } else if (U & 128) {
          I.process(d, h, g, w, k, S, A, C, R, be);
        }
    }
    if (z != null && k) {
      O0(z, d && d.ref, S, h || d, !h);
    }
  };
  const D = (d, h, g, w) => {
    if (d == null) {
      o(h.el = l(h.children), g, w);
    } else {
      const S = h.el = d.el;
      if (h.children !== d.children) {
        m(S, h.children);
      }
    }
  };
  const G = (d, h, g, w) => {
    if (d == null) {
      o(h.el = p(h.children || ""), g, w);
    } else {
      h.el = d.el;
    }
  };
  const oe = (d, h, g, w) => {
    [d.el, d.anchor] = q(d.children, h, g, w, d.el, d.anchor);
  };
  const fe = ({
                el: d,
                anchor: h
              }, g, w) => {
    let k;
    for (; d && d !== h;) {
      k = E(d);
      o(d, g, w);
      d = k;
    }
    o(h, g, w);
  };
  const K = ({
               el: d,
               anchor: h
             }) => {
    let g;
    for (; d && d !== h;) {
      g = E(d);
      r(d);
      d = g;
    }
    r(h);
  };
  const de = (d, h, g, w, k, S, A, C, R) => {
    A = A || h.type === "svg";
    if (d == null) {
      je(h, g, w, k, S, A, C, R);
    } else {
      De(d, h, k, S, A, C, R);
    }
  };
  const je = (d, h, g, w, k, S, A, C) => {
    let T;
    let I;
    const {
      type: z,
      props: U,
      shapeFlag: $,
      transition: _,
      dirs: ee
    } = d;
    T = d.el = a(d.type, S, U && U.is, U);
    if ($ & 8) {
      v(T, d.children);
    } else if ($ & 16) {
      Te(d.children, T, null, w, k, S && z !== "foreignObject", A, C);
    }
    if (ee) {
      St(d, null, w, "created");
    }
    ve(T, d, d.scopeId, A, w);
    if (U) {
      for (const ne in U) {
        if (ne !== "value" && !Bn(ne)) {
          i(T, ne, null, U[ne], S, d.children, w, k, B);
        }
      }
      if ("value" in U) {
        i(T, "value", null, U.value);
      }
      if (I = U.onVnodeBeforeMount) {
        Xe(I, w, d);
      }
    }
    if (ee) {
      St(d, null, w, "beforeMount");
    }
    const ce = dc(k, _);
    if (ce) {
      _.beforeEnter(T);
    }
    o(T, h, g);
    if ((I = U && U.onVnodeMounted) || ce || ee) {
      Me(() => {
        if (I) {
          Xe(I, w, d);
        }
        if (ce) {
          _.enter(T);
        }
        if (ee) {
          St(d, null, w, "mounted");
        }
      }, k);
    }
  };
  const ve = (d, h, g, w, k) => {
    if (g) {
      O(d, g);
    }
    if (w) {
      for (let A = 0; A < w.length; A++) {
        O(d, w[A]);
      }
    }
    if (k) {
      let A = k.subTree;
      if (h === A) {
        const C = k.vnode;
        ve(d, C, C.scopeId, C.slotScopeIds, k.parent);
      }
    }
  };
  const Te = (d, h, g, w, k, S, A, C, R = 0) => {
    for (let I = R; I < d.length; I++) {
      const z = d[I] = C ? bt(d[I]) : nt(d[I]);
      N(null, z, h, g, w, k, S, A, C);
    }
  };
  const De = (d, h, g, w, k, S, A) => {
    const R = h.el = d.el;
    let {
      patchFlag: T,
      dynamicChildren: I,
      dirs: z
    } = h;
    T |= d.patchFlag & 16;
    const U = d.props || ue;
    const $ = h.props || ue;
    let _;
    if (g) {
      Ct(g, false);
    }
    if (_ = $.onVnodeBeforeUpdate) {
      Xe(_, g, h, d);
    }
    if (z) {
      St(h, d, g, "beforeUpdate");
    }
    if (g) {
      Ct(g, true);
    }
    const ee = k && h.type !== "foreignObject";
    if (I) {
      Be(d.dynamicChildren, I, R, g, w, ee, S);
    } else if (!A) {
      te(d, h, R, null, g, w, ee, S, false);
    }
    if (T > 0) {
      if (T & 16) {
        Qe(R, h, U, $, g, w, k);
      } else {
        if (T & 2 && U.class !== $.class) {
          i(R, "class", null, $.class, k);
        }
        if (T & 4) {
          i(R, "style", U.style, $.style, k);
        }
        if (T & 8) {
          const ce = h.dynamicProps;
          for (let ne = 0; ne < ce.length; ne++) {
            const me = ce[ne];
            const W = U[me];
            const we = $[me];
            if (we !== W || me === "value") {
              i(R, me, W, we, k, d.children, g, w, B);
            }
          }
        }
      }
      if (T & 1 && d.children !== h.children) {
        v(R, h.children);
      }
    } else if (!A && I == null) {
      Qe(R, h, U, $, g, w, k);
    }
    if ((_ = $.onVnodeUpdated) || z) {
      Me(() => {
        if (_) {
          Xe(_, g, h, d);
        }
        if (z) {
          St(h, d, g, "updated");
        }
      }, w);
    }
  };
  const Be = (d, h, g, w, k, S, A) => {
    for (let R = 0; R < h.length; R++) {
      const T = d[R];
      const I = h[R];
      const z = T.el && (T.type === tt || !on(T, I) || T.shapeFlag & 70) ? y(T.el) : g;
      N(T, I, z, null, w, k, S, A, true);
    }
  };
  const Qe = (d, h, g, w, k, S, A) => {
    if (g !== w) {
      if (g !== ue) {
        for (const R in g) {
          if (!Bn(R) && !(R in w)) {
            i(d, R, g[R], null, A, h.children, k, S, B);
          }
        }
      }
      for (const R in w) {
        if (Bn(R)) {
          continue;
        }
        const T = w[R];
        const I = g[R];
        if (T !== I && R !== "value") {
          i(d, R, I, T, A, h.children, k, S, B);
        }
      }
      if ("value" in w) {
        i(d, "value", g.value, w.value);
      }
    }
  };
  const $e = (d, h, g, w, k, S, A, C, R) => {
    const I = h.el = d ? d.el : l("");
    const z = h.anchor = d ? d.anchor : l("");
    let {
      patchFlag: U,
      dynamicChildren: $,
      slotScopeIds: _
    } = h;
    if (_) {
      C = C ? C.concat(_) : _;
    }
    if (d == null) {
      o(I, g, w);
      o(z, g, w);
      Te(h.children, g, z, k, S, A, C, R);
    } else if (U > 0 && U & 64 && $ && d.dynamicChildren) {
      Be(d.dynamicChildren, $, g, k, S, A, C);
      if (h.key != null || k && h === k.subTree) {
        _o(d, h, true);
      }
    } else {
      te(d, h, g, z, k, S, A, C, R);
    }
  };
  const rt = (d, h, g, w, k, S, A, C, R) => {
    h.slotScopeIds = C;
    if (d == null) {
      if (h.shapeFlag & 512) {
        k.ctx.activate(h, g, w, A, R);
      } else {
        dt(h, g, w, k, S, A, R);
      }
    } else {
      pt(d, h, R);
    }
  };
  const dt = (d, h, g, w, k, S, A) => {
    const R = d.component = Ic(d, w, k);
    if (No(d)) {
      R.ctx.renderer = be;
    }
    Ec(R);
    if (R.asyncDep) {
      if (k) {
        k.registerDep(R, he);
      }
      if (!d.el) {
        const T = R.subTree = ot(Ut);
        G(null, T, h, g);
      }
      return;
    }
    he(R, d, h, g, k, S, A);
  };
  const pt = (d, h, g) => {
    const k = h.component = d.component;
    if (ji(d, h, g)) {
      if (k.asyncDep && !k.asyncResolved) {
        ie(k, h, g);
        return;
      } else {
        k.next = h;
        Ii(k.update);
        k.update();
      }
    } else {
      h.el = d.el;
      k.vnode = h;
    }
  };
  const he = (d, h, g, w, k, S, A) => {
    const R = () => {
      if (d.isMounted) {
        let {
          next: U,
          bu: $,
          u: _,
          parent: ee,
          vnode: ce
        } = d;
        let ne = U;
        let me;
        Ct(d, false);
        if (U) {
          U.el = ce.el;
          ie(d, U, A);
        } else {
          U = ce;
        }
        if ($) {
          f0($);
        }
        if (me = U.props && U.props.onVnodeBeforeUpdate) {
          Xe(me, ee, U, ce);
        }
        Ct(d, true);
        const W = p0(d);
        const we = d.subTree;
        d.subTree = W;
        N(we, W, y(we.el), H(we), d, k, S);
        U.el = W.el;
        if (ne === null) {
          Ni(d, W.el);
        }
        if (_) {
          Me(_, k);
        }
        if (me = U.props && U.props.onVnodeUpdated) {
          Me(() => Xe(me, ee, U, ce), k);
        }
      } else {
        let U;
        const {
          el: $,
          props: _
        } = h;
        const {
          bm: ee,
          m: ce,
          parent: ne
        } = d;
        const me = Vn(h);
        Ct(d, false);
        if (ee) {
          f0(ee);
        }
        if (!me && (U = _ && _.onVnodeBeforeMount)) {
          Xe(U, ne, h);
        }
        Ct(d, true);
        if ($ && it) {
          const W = () => {
            d.subTree = p0(d);
            it($, d.subTree, d, k, null);
          };
          if (me) {
            h.type.__asyncLoader().then(() => !d.isUnmounted && W());
          } else {
            W();
          }
        } else {
          const W = d.subTree = p0(d);
          N(null, W, g, w, d, k, S);
          h.el = W.el;
        }
        if (ce) {
          Me(ce, k);
        }
        if (!me && (U = _ && _.onVnodeMounted)) {
          const W = h;
          Me(() => Xe(U, ne, W), k);
        }
        if ((h.shapeFlag & 256 || ne && Vn(ne.vnode) && ne.vnode.shapeFlag & 256) && d.a) {
          Me(d.a, k);
        }
        d.isMounted = true;
        h = g = w = null;
      }
    };
    const T = d.effect = new _0(R, () => Z0(I), d.scope);
    const I = d.update = () => T.run();
    I.id = d.uid;
    Ct(d, true);
    I();
  };
  const ie = (d, h, g) => {
    h.component = d;
    const k = d.vnode.props;
    d.vnode = h;
    d.next = null;
    cc(d, h.props, k, g);
    lc(d, h.children, g);
    Qt();
    Sx(d);
    Xt();
  };
  const te = (d, h, g, w, k, S, A, C, R = false) => {
    const I = d && d.children;
    const z = d ? d.shapeFlag : 0;
    const U = h.children;
    const {
      patchFlag: $,
      shapeFlag: _
    } = h;
    if ($ > 0) {
      if ($ & 128) {
        Oe(I, U, g, w, k, S, A, C, R);
        return;
      } else if ($ & 256) {
        He(I, U, g, w, k, S, A, C, R);
        return;
      }
    }
    if (_ & 8) {
      if (z & 16) {
        B(I, k, S);
      }
      if (U !== I) {
        v(g, U);
      }
    } else if (z & 16) {
      if (_ & 16) {
        Oe(I, U, g, w, k, S, A, C, R);
      } else {
        B(I, k, S, true);
      }
    } else {
      if (z & 8) {
        v(g, "");
      }
      if (_ & 16) {
        Te(U, g, w, k, S, A, C, R);
      }
    }
  };
  const He = (d, h, g, w, k, S, A, C, R) => {
    d = d || Ht;
    h = h || Ht;
    const I = d.length;
    const z = h.length;
    const U = Math.min(I, z);
    let $;
    for ($ = 0; $ < U; $++) {
      const _ = h[$] = R ? bt(h[$]) : nt(h[$]);
      N(d[$], _, g, null, k, S, A, C, R);
    }
    if (I > z) {
      B(d, k, S, true, false, U);
    } else {
      Te(h, g, w, k, S, A, C, R, U);
    }
  };
  const Oe = (d, h, g, w, k, S, A, C, R) => {
    let I = 0;
    const z = h.length;
    let U = d.length - 1;
    let $ = z - 1;
    for (; I <= U && I <= $;) {
      const _ = d[I];
      const ee = h[I] = R ? bt(h[I]) : nt(h[I]);
      if (on(_, ee)) {
        N(_, ee, g, null, k, S, A, C, R);
      } else {
        break;
      }
      I++;
    }
    for (; I <= U && I <= $;) {
      const _ = d[U];
      const ee = h[$] = R ? bt(h[$]) : nt(h[$]);
      if (on(_, ee)) {
        N(_, ee, g, null, k, S, A, C, R);
      } else {
        break;
      }
      U--;
      $--;
    }
    if (I > U) {
      if (I <= $) {
        const _ = $ + 1;
        const ee = _ < z ? h[_].el : w;
        for (; I <= $;) {
          N(null, h[I] = R ? bt(h[I]) : nt(h[I]), g, ee, k, S, A, C, R);
          I++;
        }
      }
    } else if (I > $) {
      for (; I <= U;) {
        b(d[I], k, S, true);
        I++;
      }
    } else {
      const _ = I;
      const ee = I;
      const ce = new Map();
      for (I = ee; I <= $; I++) {
        const Ue = h[I] = R ? bt(h[I]) : nt(h[I]);
        if (Ue.key != null) {
          ce.set(Ue.key, I);
        }
      }
      let ne;
      let me = 0;
      const W = $ - ee + 1;
      let we = false;
      let Pt = 0;
      const _e = new Array(W);
      for (I = 0; I < W; I++) {
        _e[I] = 0;
      }
      for (I = _; I <= U; I++) {
        const Ue = d[I];
        if (me >= W) {
          b(Ue, k, S, true);
          continue;
        }
        let Ne;
        if (Ue.key != null) {
          Ne = ce.get(Ue.key);
        } else {
          for (ne = ee; ne <= $; ne++) {
            if (_e[ne - ee] === 0 && on(Ue, h[ne])) {
              Ne = ne;
              break;
            }
          }
        }
        if (Ne === undefined) {
          b(Ue, k, S, true);
        } else {
          _e[Ne - ee] = I + 1;
          if (Ne >= Pt) {
            Pt = Ne;
          } else {
            we = true;
          }
          N(Ue, h[Ne], g, null, k, S, A, C, R);
          me++;
        }
      }
      const jt = we ? pc(_e) : Ht;
      ne = jt.length - 1;
      I = W - 1;
      for (; I >= 0; I--) {
        const Ue = ee + I;
        const Ne = h[Ue];
        const Nt = Ue + 1 < z ? h[Ue + 1].el : w;
        if (_e[I] === 0) {
          N(null, Ne, g, Nt, k, S, A, C, R);
        } else if (we) {
          if (ne < 0 || I !== jt[ne]) {
            V(Ne, g, Nt, 2);
          } else {
            ne--;
          }
        }
      }
    }
  };
  const V = (d, h, g, w, k = null) => {
    const {
      el: A,
      type: C,
      transition: R,
      children: T,
      shapeFlag: I
    } = d;
    if (I & 6) {
      V(d.component.subTree, h, g, w);
      return;
    }
    if (I & 128) {
      d.suspense.move(h, g, w);
      return;
    }
    if (I & 64) {
      C.move(d, h, g, be);
      return;
    }
    if (C === tt) {
      o(A, h, g);
      for (let U = 0; U < T.length; U++) {
        V(T[U], h, g, w);
      }
      o(d.anchor, h, g);
      return;
    }
    if (C === Ln) {
      fe(d, h, g);
      return;
    }
    if (w !== 2 && I & 1 && R) {
      if (w === 0) {
        R.beforeEnter(A);
        o(A, h, g);
        Me(() => R.enter(A), k);
      } else {
        const {
          leave: U,
          delayLeave: $,
          afterLeave: _
        } = R;
        const ee = () => o(A, h, g);
        const ce = () => {
          U(A, () => {
            ee();
            if (_) {
              _();
            }
          });
        };
        if ($) {
          $(A, ee, ce);
        } else {
          ce();
        }
      }
    } else {
      o(A, h, g);
    }
  };
  const b = (d, h, g, w = false, k = false) => {
    const {
      type: A,
      props: C,
      ref: R,
      children: T,
      dynamicChildren: I,
      shapeFlag: z,
      patchFlag: U,
      dirs: $
    } = d;
    if (R != null) {
      O0(R, null, g, d, true);
    }
    if (z & 256) {
      h.ctx.deactivate(d);
      return;
    }
    const _ = z & 1 && $;
    const ee = !Vn(d);
    let ce;
    if (ee && (ce = C && C.onVnodeBeforeUnmount)) {
      Xe(ce, h, d);
    }
    if (z & 6) {
      L(d.component, g, w);
    } else {
      if (z & 128) {
        d.suspense.unmount(g, w);
        return;
      }
      if (_) {
        St(d, null, h, "beforeUnmount");
      }
      if (z & 64) {
        d.type.remove(d, h, g, k, be, w);
      } else if (I && (A !== tt || U > 0 && U & 64)) {
        B(I, h, g, false, true);
      } else if (A === tt && U & 384 || !k && z & 16) {
        B(T, h, g);
      }
      if (w) {
        j(d);
      }
    }
    if (ee && (ce = C && C.onVnodeUnmounted) || _) {
      Me(() => {
        if (ce) {
          Xe(ce, h, d);
        }
        if (_) {
          St(d, null, h, "unmounted");
        }
      }, g);
    }
  };
  const j = d => {
    const {
      type: g,
      el: w,
      anchor: k,
      transition: S
    } = d;
    if (g === tt) {
      F(w, k);
      return;
    }
    if (g === Ln) {
      K(d);
      return;
    }
    const A = () => {
      r(w);
      if (S && !S.persisted && S.afterLeave) {
        S.afterLeave();
      }
    };
    if (d.shapeFlag & 1 && S && !S.persisted) {
      const {
        leave: C,
        delayLeave: R
      } = S;
      const T = () => C(w, A);
      if (R) {
        R(d.el, A, T);
      } else {
        T();
      }
    } else {
      A();
    }
  };
  const F = (d, h) => {
    let g;
    for (; d !== h;) {
      g = E(d);
      r(d);
      d = g;
    }
    r(h);
  };
  const L = (d, h, g) => {
    const {
      bum: k,
      scope: S,
      update: A,
      subTree: C,
      um: R
    } = d;
    if (k) {
      f0(k);
    }
    S.stop();
    if (A) {
      A.active = false;
      b(C, d, h, g);
    }
    if (R) {
      Me(R, h);
    }
    Me(() => {
      d.isUnmounted = true;
    }, h);
    if (h && h.pendingBranch && !h.isUnmounted && d.asyncDep && !d.asyncResolved && d.suspenseId === h.pendingId) {
      h.deps--;
      if (h.deps === 0) {
        h.resolve();
      }
    }
  };
  const B = (d, h, g, w = false, k = false, S = 0) => {
    for (let C = S; C < d.length; C++) {
      b(d[C], h, g, w, k);
    }
  };
  const H = d => {
    if (d.shapeFlag & 6) {
      return H(d.component.subTree);
    } else if (d.shapeFlag & 128) {
      return d.suspense.next();
    } else {
      return E(d.anchor || d.el);
    }
  };
  const se = (d, h, g) => {
    if (d == null) {
      if (h._vnode) {
        b(h._vnode, null, null, true);
      }
    } else {
      N(h._vnode || null, d, h, null, null, null, g);
    }
    Sx();
    Ao();
    h._vnode = d;
  };
  const be = {
    p: N,
    um: b,
    m: V,
    r: j,
    mt: dt,
    mc: Te,
    pc: te,
    pbc: Be,
    n: H,
    o: e
  };
  let Ve;
  let it;
  if (t) {
    [Ve, it] = t(be);
  }
  return {
    render: se,
    hydrate: Ve,
    createApp: oc(se, Ve)
  };
}
function Ct({
              effect: e,
              update: t
            }, n) {
  e.allowRecurse = t.allowRecurse = n;
}
function dc(e, t) {
  return (!e || e && !e.pendingBranch) && t && !t.persisted;
}
function _o(e, t, n = false) {
  const o = e.children;
  const r = t.children;
  if (Y(o) && Y(r)) {
    for (let i = 0; i < o.length; i++) {
      const a = o[i];
      let l = r[i];
      if (l.shapeFlag & 1 && !l.dynamicChildren) {
        if (l.patchFlag <= 0 || l.patchFlag === 32) {
          l = r[i] = bt(r[i]);
          l.el = a.el;
        }
        if (!n) {
          _o(a, l);
        }
      }
      if (l.type === n0) {
        l.el = a.el;
      }
    }
  }
}
function pc(e) {
  const n = e.slice();
  const x = [0];
  let o;
  let r;
  let i;
  let a;
  let l;
  const p = e.length;
  for (o = 0; o < p; o++) {
    const m = e[o];
    if (m !== 0) {
      r = x[x.length - 1];
      if (e[r] < m) {
        n[o] = r;
        x.push(o);
        continue;
      }
      i = 0;
      a = x.length - 1;
      for (; i < a;) {
        l = i + a >> 1;
        if (e[x[l]] < m) {
          i = l + 1;
        } else {
          a = l;
        }
      }
      if (m < e[x[i]]) {
        if (i > 0) {
          n[o] = x[i - 1];
        }
        x[i] = o;
      }
    }
  }
  i = x.length;
  a = x[i - 1];
  for (; i-- > 0;) {
    x[i] = a;
    a = n[a];
  }
  return x;
}
const hc = e => e.__isTeleport;
const tt = Symbol.for("v-fgt");
const n0 = Symbol.for("v-txt");
const Ut = Symbol.for("v-cmt");
const Ln = Symbol.for("v-stc");
const un = [];
let Ge = null;
function et(e = false) {
  un.push(Ge = e ? null : []);
}
function mc() {
  un.pop();
  Ge = un[un.length - 1] || null;
}
let mn = 1;
function jx(e) {
  mn += e;
}
function Wo(e) {
  e.dynamicChildren = mn > 0 ? Ge || Ht : null;
  mc();
  if (mn > 0 && Ge) {
    Ge.push(e);
  }
  return e;
}
function at(e, t, n, x, o, r) {
  return Wo(J(e, t, n, x, o, r, true));
}
function bc(e, t, n, x, o) {
  return Wo(ot(e, t, n, x, o, true));
}
function gc(e) {
  if (e) {
    return e.__v_isVNode === true;
  } else {
    return false;
  }
}
function on(e, t) {
  return e.type === t.type && e.key === t.key;
}
const x0 = "__vInternal";
const Ko = ({
              key: e
            }) => e != null ? e : null;
const Hn = ({
              ref: e,
              ref_key: t,
              ref_for: n
            }) => {
  if (typeof e === "number") {
    e = "" + e;
  }
  if (e != null) {
    if (ye(e) || Ae(e) || X(e)) {
      return {
        i: Je,
        r: e,
        k: t,
        f: !!n
      };
    } else {
      return e;
    }
  } else {
    return null;
  }
};
function J(e, t = null, n = null, x = 0, o = null, r = e === tt ? 0 : 1, i = false, a = false) {
  const p = {
    __v_isVNode: true,
    __v_skip: true,
    type: e,
    props: t,
    key: t && Ko(t),
    ref: t && Hn(t),
    scopeId: Uo,
    slotScopeIds: null,
    children: n,
    component: null,
    suspense: null,
    ssContent: null,
    ssFallback: null,
    dirs: null,
    transition: null,
    el: null,
    anchor: null,
    target: null,
    targetAnchor: null,
    staticCount: 0,
    shapeFlag: r,
    patchFlag: x,
    dynamicProps: o,
    dynamicChildren: null,
    appContext: null,
    ctx: Je
  };
  if (a) {
    ex(p, n);
    if (r & 128) {
      e.normalize(p);
    }
  } else if (n) {
    p.shapeFlag |= ye(n) ? 8 : 16;
  }
  if (mn > 0 && !i && Ge && (p.patchFlag > 0 || r & 6) && p.patchFlag !== 32) {
    Ge.push(p);
  }
  return p;
}
const ot = vc;
function vc(e, t = null, n = null, x = 0, o = null, r = false) {
  if (!e || e === Mi) {
    e = Ut;
  }
  if (gc(e)) {
    const l = Jt(e, t, true);
    if (n) {
      ex(l, n);
    }
    if (mn > 0 && !r && Ge) {
      if (l.shapeFlag & 6) {
        Ge[Ge.indexOf(e)] = l;
      } else {
        Ge.push(l);
      }
    }
    l.patchFlag |= -2;
    return l;
  }
  if (Uc(e)) {
    e = e.__vccOpts;
  }
  if (t) {
    t = yc(t);
    let {
      class: l,
      style: p
    } = t;
    if (l && !ye(l)) {
      t.class = D0(l);
    }
    if (pe(p)) {
      if (wo(p) && !Y(p)) {
        p = Re({}, p);
      }
      t.style = q0(p);
    }
  }
  const a = ye(e) ? 1 : Fi(e) ? 128 : hc(e) ? 64 : pe(e) ? 4 : X(e) ? 2 : 0;
  return J(e, t, n, x, o, a, r, true);
}
function yc(e) {
  if (e) {
    if (wo(e) || x0 in e) {
      return Re({}, e);
    } else {
      return e;
    }
  } else {
    return null;
  }
}
function Jt(e, t, n = false) {
  const {
    props: o,
    ref: r,
    patchFlag: i,
    children: a
  } = e;
  const l = t ? Sc(o || {}, t) : o;
  return {
    __v_isVNode: true,
    __v_skip: true,
    type: e.type,
    props: l,
    key: l && Ko(l),
    ref: t && t.ref ? n && r ? Y(r) ? r.concat(Hn(t)) : [r, Hn(t)] : Hn(t) : r,
    scopeId: e.scopeId,
    slotScopeIds: e.slotScopeIds,
    children: a,
    target: e.target,
    targetAnchor: e.targetAnchor,
    staticCount: e.staticCount,
    shapeFlag: e.shapeFlag,
    patchFlag: t && e.type !== tt ? i === -1 ? 16 : i | 16 : i,
    dynamicProps: e.dynamicProps,
    dynamicChildren: e.dynamicChildren,
    appContext: e.appContext,
    dirs: e.dirs,
    transition: e.transition,
    component: e.component,
    suspense: e.suspense,
    ssContent: e.ssContent && Jt(e.ssContent),
    ssFallback: e.ssFallback && Jt(e.ssFallback),
    el: e.el,
    anchor: e.anchor,
    ctx: e.ctx,
    ce: e.ce
  };
}
function wc(e = " ", t = 0) {
  return ot(n0, null, e, t);
}
function kc(e, t) {
  const x = ot(Ln, null, e);
  x.staticCount = t;
  return x;
}
function Bt(e = "", t = false) {
  if (t) {
    et();
    return bc(Ut, null, e);
  } else {
    return ot(Ut, null, e);
  }
}
function nt(e) {
  if (e == null || typeof e === "boolean") {
    return ot(Ut);
  } else if (Y(e)) {
    return ot(tt, null, e.slice());
  } else if (typeof e === "object") {
    return bt(e);
  } else {
    return ot(n0, null, String(e));
  }
}
function bt(e) {
  if (e.el === null && e.patchFlag !== -1 || e.memo) {
    return e;
  } else {
    return Jt(e);
  }
}
function ex(e, t) {
  let x = 0;
  const {
    shapeFlag: o
  } = e;
  if (t == null) {
    t = null;
  } else if (Y(t)) {
    x = 16;
  } else if (typeof t === "object") {
    if (o & 65) {
      const r = t.default;
      if (r) {
        if (r._c) {
          r._d = false;
        }
        ex(e, r());
        if (r._c) {
          r._d = true;
        }
      }
      return;
    } else {
      x = 32;
      const r = t._;
      if (!r && !(x0 in t)) {
        t._ctx = Je;
      } else if (r === 3 && Je) {
        if (Je.slots._ === 1) {
          t._ = 1;
        } else {
          t._ = 2;
          e.patchFlag |= 1024;
        }
      }
    }
  } else if (X(t)) {
    t = {
      default: t,
      _ctx: Je
    };
    x = 32;
  } else {
    t = String(t);
    if (o & 64) {
      x = 16;
      t = [wc(t)];
    } else {
      x = 8;
    }
  }
  e.children = t;
  e.shapeFlag |= x;
}
function Sc(...e) {
  const n = {};
  for (let x = 0; x < e.length; x++) {
    const o = e[x];
    for (const r in o) {
      if (r === "class") {
        if (n.class !== o.class) {
          n.class = D0([n.class, o.class]);
        }
      } else if (r === "style") {
        n.style = q0([n.style, o.style]);
      } else if (Gn(r)) {
        const i = n[r];
        const a = o[r];
        if (a && i !== a && !(Y(i) && i.includes(a))) {
          n[r] = i ? [].concat(i, a) : a;
        }
      } else if (r !== "") {
        n[r] = o[r];
      }
    }
  }
  return n;
}
function Xe(e, t, n, x = null) {
  Ze(e, t, 7, [n, x]);
}
const Cc = zo();
let Rc = 0;
function Ic(e, t, n) {
  const o = e.type;
  const r = (t ? t.appContext : e.appContext) || Cc;
  const i = {
    uid: Rc++,
    vnode: e,
    type: o,
    parent: t,
    appContext: r,
    root: null,
    next: null,
    subTree: null,
    effect: null,
    update: null,
    scope: new Wr(true),
    render: null,
    proxy: null,
    exposed: null,
    exposeProxy: null,
    withProxy: null,
    provides: t ? t.provides : Object.create(r.provides),
    accessCache: null,
    renderCache: [],
    components: null,
    directives: null,
    propsOptions: Ho(o, r),
    emitsOptions: Oo(o, r),
    emit: null,
    emitted: null,
    propsDefaults: ue,
    inheritAttrs: o.inheritAttrs,
    ctx: ue,
    data: ue,
    props: ue,
    attrs: ue,
    slots: ue,
    refs: ue,
    setupState: ue,
    setupContext: null,
    attrsProxy: null,
    slotsProxy: null,
    suspense: n,
    suspenseId: n ? n.pendingId : 0,
    asyncDep: null,
    asyncResolved: false,
    isMounted: false,
    isUnmounted: false,
    isDeactivated: false,
    bc: null,
    c: null,
    bm: null,
    m: null,
    bu: null,
    u: null,
    um: null,
    bum: null,
    da: null,
    a: null,
    rtg: null,
    rtc: null,
    ec: null,
    sp: null
  };
  i.ctx = {
    _: i
  };
  i.root = t ? t.root : i;
  i.emit = Ti.bind(null, i);
  if (e.ce) {
    e.ce(i);
  }
  return i;
}
let Ce = null;
let tx;
let Vt;
let Nx = "__VUE_INSTANCE_SETTERS__";
if (!(Vt = y0()[Nx])) {
  Vt = y0()[Nx] = [];
}
Vt.push(e => Ce = e);
tx = e => {
  if (Vt.length > 1) {
    Vt.forEach(n => n(e));
  } else {
    Vt[0](e);
  }
};
const Gt = e => {
  tx(e);
  e.scope.on();
};
const Tt = () => {
  if (Ce) {
    Ce.scope.off();
  }
  tx(null);
};
function Jo(e) {
  return e.vnode.shapeFlag & 4;
}
let bn = false;
function Ec(e, t = false) {
  bn = t;
  const {
    props: x,
    children: o
  } = e.vnode;
  const r = Jo(e);
  ic(e, x, r, t);
  ac(e, o);
  const i = r ? Ac(e, t) : undefined;
  bn = false;
  return i;
}
function Ac(e, t) {
  const x = e.type;
  e.accessCache = Object.create(null);
  e.proxy = ko(new Proxy(e.ctx, Zi));
  const {
    setup: o
  } = x;
  if (o) {
    const r = e.setupContext = o.length > 1 ? Oc(e) : null;
    Gt(e);
    Qt();
    const i = yt(o, e, 0, [e.props, r]);
    Xt();
    Tt();
    if (xo(i)) {
      i.then(Tt, Tt);
      if (t) {
        return i.then(a => {
          Mx(e, a, t);
        }).catch(a => {
          Xn(a, e, 0);
        });
      }
      e.asyncDep = i;
    } else {
      Mx(e, i, t);
    }
  } else {
    Go(e, t);
  }
}
function Mx(e, t, n) {
  if (X(t)) {
    if (e.type.__ssrInlineRender) {
      e.ssrRender = t;
    } else {
      e.render = t;
    }
  } else if (pe(t)) {
    e.setupState = Ro(t);
  }
  Go(e, n);
}
let Fx;
function Go(e, t, n) {
  const o = e.type;
  if (!e.render) {
    if (!t && Fx && !o.render) {
      const r = o.template || Q0(e).template;
      if (r) {
        const {
          isCustomElement: i,
          compilerOptions: a
        } = e.appContext.config;
        const {
          delimiters: l,
          compilerOptions: p
        } = o;
        const m = Re(Re({
          isCustomElement: i,
          delimiters: l
        }, a), p);
        o.render = Fx(r, m);
      }
    }
    e.render = o.render || Ye;
  }
  {
    Gt(e);
    Qt();
    try {
      Qi(e);
    } finally {
      Xt();
      Tt();
    }
  }
}
function Tc(e) {
  return e.attrsProxy || (e.attrsProxy = new Proxy(e.attrs, {
    get(n, x) {
      Fe(e, "get", "$attrs");
      return n[x];
    }
  }));
}
function Oc(e) {
  const n = x => {
    e.exposed = x || {};
  };
  return {
    get attrs() {
      return Tc(e);
    },
    slots: e.slots,
    emit: e.emit,
    expose: n
  };
}
function o0(e) {
  if (e.exposed) {
    return e.exposeProxy || (e.exposeProxy = new Proxy(Ro(ko(e.exposed)), {
      get(n, x) {
        if (x in n) {
          return n[x];
        }
        if (x in ln) {
          return ln[x](e);
        }
      },
      has(n, x) {
        return x in n || x in ln;
      }
    }));
  }
}
function Uc(e) {
  return X(e) && "__vccOpts" in e;
}
const Pc = (e, t) => wi(e, t, bn);
const jc = Symbol.for("v-scx");
const Nc = () => zn(jc);
const Mc = "3.3.11";
const Fc = "http://www.w3.org/2000/svg";
const It = typeof document !== "undefined" ? document : null;
const Bx = It && It.createElement("template");
const Bc = {
  insert: (e, t, n) => {
    t.insertBefore(e, n || null);
  },
  remove: e => {
    const n = e.parentNode;
    if (n) {
      n.removeChild(e);
    }
  },
  createElement: (e, t, n, x) => {
    const r = t ? It.createElementNS(Fc, e) : It.createElement(e, n ? {
      is: n
    } : undefined);
    if (e === "select" && x && x.multiple != null) {
      r.setAttribute("multiple", x.multiple);
    }
    return r;
  },
  createText: e => It.createTextNode(e),
  createComment: e => It.createComment(e),
  setText: (e, t) => {
    e.nodeValue = t;
  },
  setElementText: (e, t) => {
    e.textContent = t;
  },
  parentNode: e => e.parentNode,
  nextSibling: e => e.nextSibling,
  querySelector: e => It.querySelector(e),
  setScopeId(e, t) {
    e.setAttribute(t, "");
  },
  insertStaticContent(e, t, n, x, o, r) {
    const a = n ? n.previousSibling : t.lastChild;
    if (o && (o === r || o.nextSibling)) {
      for (; [] && (t.insertBefore(o.cloneNode(true), n), !(o === r || !(o = o.nextSibling))););
    } else {
      Bx.innerHTML = x ? "<svg>" + e + "</svg>" : e;
      const l = Bx.content;
      if (x) {
        const p = l.firstChild;
        for (; p.firstChild;) {
          l.appendChild(p.firstChild);
        }
        l.removeChild(p);
      }
      t.insertBefore(l, n);
    }
    return [a ? a.nextSibling : t.firstChild, n ? n.previousSibling : t.lastChild];
  }
};
const Vc = Symbol("_vtc");
const zc = {
  name: String,
  type: String,
  css: {
    type: Boolean,
    default: true
  },
  duration: [String, Number, Object],
  enterFromClass: String,
  enterActiveClass: String,
  enterToClass: String,
  appearFromClass: String,
  appearActiveClass: String,
  appearToClass: String,
  leaveFromClass: String,
  leaveActiveClass: String,
  leaveToClass: String
};
Re({}, zi, zc);
function Lc(e, t, n) {
  const o = e[Vc];
  if (o) {
    t = (t ? [t, ...o] : [...o]).join(" ");
  }
  if (t == null) {
    e.removeAttribute("class");
  } else if (n) {
    e.setAttribute("class", t);
  } else {
    e.className = t;
  }
}
const nx = Symbol("_vod");
const jn = {
  beforeMount(e, {
    value: t
  }, {
                transition: n
              }) {
    e[nx] = e.style.display === "none" ? "" : e.style.display;
    if (n && t) {
      n.beforeEnter(e);
    } else {
      rn(e, t);
    }
  },
  mounted(e, {
    value: t
  }, {
            transition: n
          }) {
    if (n && t) {
      n.enter(e);
    }
  },
  updated(e, {
    value: t,
    oldValue: n
  }, {
            transition: x
          }) {
    if (!t != !n) {
      if (x) {
        if (t) {
          x.beforeEnter(e);
          rn(e, true);
          x.enter(e);
        } else {
          x.leave(e, () => {
            rn(e, false);
          });
        }
      } else {
        rn(e, t);
      }
    }
  },
  beforeUnmount(e, {
    value: t
  }) {
    rn(e, t);
  }
};
function rn(e, t) {
  e.style.display = t ? e[nx] : "none";
}
function Hc(e, t, n) {
  const o = e.style;
  const r = ye(n);
  if (n && !r) {
    if (t && !ye(t)) {
      for (const i in t) {
        if (n[i] == null) {
          U0(o, i, "");
        }
      }
    }
    for (const i in n) {
      U0(o, i, n[i]);
    }
  } else {
    const i = o.display;
    if (r) {
      if (t !== n) {
        o.cssText = n;
      }
    } else if (t) {
      e.removeAttribute("style");
    }
    if (nx in e) {
      o.display = i;
    }
  }
}
const Vx = /\s*!important$/;
function U0(e, t, n) {
  if (Y(n)) {
    n.forEach(o => U0(e, t, o));
  } else {
    if (n == null) {
      n = "";
    }
    if (t.startsWith("--")) {
      e.setProperty(t, n);
    } else {
      const o = qc(e, t);
      if (Vx.test(n)) {
        e.setProperty(Zt(o), n.replace(Vx, ""), "important");
      } else {
        e[o] = n;
      }
    }
  }
}
const zx = ["Webkit", "Moz", "ms"];
const b0 = {};
function qc(e, t) {
  const x = b0[t];
  if (x) {
    return x;
  }
  let o = Wt(t);
  if (o !== "filter" && o in e) {
    return b0[t] = o;
  }
  o = io(o);
  for (let r = 0; r < zx.length; r++) {
    const i = zx[r] + o;
    if (i in e) {
      return b0[t] = i;
    }
  }
  return t;
}
const Lx = "http://www.w3.org/1999/xlink";
function Dc(e, t, n, x, o) {
  if (x && t.startsWith("xlink:")) {
    if (n == null) {
      e.removeAttributeNS(Lx, t.slice(6, t.length));
    } else {
      e.setAttributeNS(Lx, t, n);
    }
  } else {
    const i = _r(t);
    if (n == null || i && !co(n)) {
      e.removeAttribute(t);
    } else {
      e.setAttribute(t, i ? "" : n);
    }
  }
}
function $c(e, t, n, x, o, r, i) {
  if (t === "innerHTML" || t === "textContent") {
    if (x) {
      i(x, o, r);
    }
    e[t] = n == null ? "" : n;
    return;
  }
  const l = e.tagName;
  if (t === "value" && l !== "PROGRESS" && !l.includes("-")) {
    e._value = n;
    const m = l === "OPTION" ? e.getAttribute("value") : e.value;
    const v = n == null ? "" : n;
    if (m !== v) {
      e.value = v;
    }
    if (n == null) {
      e.removeAttribute(t);
    }
    return;
  }
  let p = false;
  if (n === "" || n == null) {
    const m = typeof e[t];
    if (m === "boolean") {
      n = co(n);
    } else if (n == null && m === "string") {
      n = "";
      p = true;
    } else if (m === "number") {
      n = 0;
      p = true;
    }
  }
  try {
    e[t] = n;
  } catch {}
  if (p) {
    e.removeAttribute(t);
  }
}
function _c(e, t, n, x) {
  e.addEventListener(t, n, x);
}
function Wc(e, t, n, x) {
  e.removeEventListener(t, n, x);
}
const Hx = Symbol("_vei");
function Kc(e, t, n, x, o = null) {
  const r = e[Hx] || (e[Hx] = {});
  const i = r[t];
  if (x && i) {
    i.value = x;
  } else {
    const [a, l] = Jc(t);
    if (x) {
      const p = r[t] = Zc(x, o);
      _c(e, a, p, l);
    } else if (i) {
      Wc(e, a, i, l);
      r[t] = undefined;
    }
  }
}
const qx = /(?:Once|Passive|Capture)$/;
function Jc(e) {
  let n;
  if (qx.test(e)) {
    n = {};
    let o;
    for (; o = e.match(qx);) {
      e = e.slice(0, e.length - o[0].length);
      n[o[0].toLowerCase()] = true;
    }
  }
  return [e[2] === ":" ? e.slice(3) : Zt(e.slice(2)), n];
}
let g0 = 0;
const Gc = Promise.resolve();
const Yc = () => g0 || (Gc.then(() => g0 = 0), g0 = Date.now());
function Zc(e, t) {
  const x = o => {
    if (!o._vts) {
      o._vts = Date.now();
    } else if (o._vts <= x.attached) {
      return;
    }
    Ze(Qc(o, x.value), t, 5, [o]);
  };
  x.value = e;
  x.attached = Yc();
  return x;
}
function Qc(e, t) {
  if (Y(t)) {
    const x = e.stopImmediatePropagation;
    e.stopImmediatePropagation = () => {
      x.call(e);
      e._stopped = true;
    };
    return t.map(o => r => !r._stopped && o && o(r));
  } else {
    return t;
  }
}
const Dx = e => e.charCodeAt(0) === 111 && e.charCodeAt(1) === 110 && e.charCodeAt(2) > 96 && e.charCodeAt(2) < 123;
const Xc = (e, t, n, x, o = false, r, i, a, l) => {
  if (t === "class") {
    Lc(e, x, o);
  } else if (t === "style") {
    Hc(e, n, x);
  } else if (Gn(t)) {
    if (!z0(t)) {
      Kc(e, t, n, x, i);
    }
  } else if (t[0] === "." ? (t = t.slice(1), true) : t[0] === "^" ? (t = t.slice(1), false) : es(e, t, x, o)) {
    $c(e, t, x, r, i, a, l);
  } else {
    if (t === "true-value") {
      e._trueValue = x;
    } else if (t === "false-value") {
      e._falseValue = x;
    }
    Dc(e, t, x, o);
  }
};
function es(e, t, n, x) {
  if (x) {
    if (t === "innerHTML" || t === "textContent") {
      return true;
    } else if (t in e && Dx(t) && X(n)) {
      return true;
    } else {
      return false;
    }
  }
  if (t === "spellcheck" || t === "draggable" || t === "translate") {
    return false;
  }
  if (t === "form") {
    return false;
  }
  if (t === "list" && e.tagName === "INPUT") {
    return false;
  }
  if (t === "type" && e.tagName === "TEXTAREA") {
    return false;
  }
  if (t === "width" || t === "height") {
    const r = e.tagName;
    if (r === "IMG" || r === "VIDEO" || r === "CANVAS" || r === "SOURCE") {
      return false;
    }
  }
  if (Dx(t) && ye(n)) {
    return false;
  } else {
    return t in e;
  }
}
const ts = Re({
  patchProp: Xc
}, Bc);
let $x;
function ns() {
  return $x || ($x = uc(ts));
}
const xs = (...e) => {
  const n = ns().createApp(...e);
  const {
    mount: x
  } = n;
  n.mount = o => {
    const i = os(o);
    if (!i) {
      return;
    }
    const a = n._component;
    if (!X(a) && !a.render && !a.template) {
      a.template = i.innerHTML;
    }
    i.innerHTML = "";
    const l = x(i, false, i instanceof SVGElement);
    if (i instanceof Element) {
      i.removeAttribute("v-cloak");
      i.setAttribute("data-v-app", "");
    }
    return l;
  };
  return n;
};
function os(e) {
  if (ye(e)) {
    return document.querySelector(e);
  } else {
    return e;
  }
}
var P0 = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
function rs(e) {
  if (e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default")) {
    return e.default;
  } else {
    return e;
  }
}
var gt = {};
var j0 = P0 && P0.__assign || function () {
  j0 = Object.assign || function (t) {
    for (var x, o = 1, r = arguments.length; o < r; o++) {
      x = arguments[o];
      for (var i in x) {
        if (Object.prototype.hasOwnProperty.call(x, i)) {
          t[i] = x[i];
        }
      }
    }
    return t;
  };
  return j0.apply(this, arguments);
};
Object.defineProperty(gt, "__esModule", {
  value: true
});
gt.join = gt.subst = gt.query = undefined;
function is(e, t, n) {
  if (n === undefined) {
    n = {};
  }
  if (typeof t === "string") {
    var o = e;
    var r = t;
    var i = n;
    return _x(r, i, o);
  } else {
    var a = e;
    var i = t;
    return _x(a, i);
  }
}
var zt = gt.default = is;
function _x(e, t, n) {
  var o = Zo(e, t);
  var r = o.renderedPath;
  var i = o.remainingParams;
  var a = ss(i);
  var l = Yo(a);
  var p = N0(r, "?", l);
  if (n) {
    return N0(n, "/", p);
  } else {
    return p;
  }
}
function Yo(e) {
  return new URLSearchParams(e).toString();
}
gt.query = Yo;
function cs(e, t) {
  var x = Zo(e, t).renderedPath;
  return x;
}
gt.subst = cs;
function Zo(e, t) {
  var x = j0({}, t);
  var o = ["boolean", "string", "number"];
  var r = e.replace(/:\w+/g, function (i) {
    var l = i.slice(1);
    if (/^\d+$/.test(l)) {
      return i;
    }
    if (!t.hasOwnProperty(l)) {
      throw new Error("Missing value for path parameter " + l + ".");
    }
    if (!o.includes(typeof t[l])) {
      throw new TypeError("Path parameter " + l + " cannot be of type " + typeof t[l] + ". " + ("Allowed types are: " + o.join(", ") + "."));
    }
    if (typeof t[l] == "string" && t[l].trim() === "") {
      throw new Error("Path parameter " + l + " cannot be an empty string.");
    }
    delete x[l];
    return encodeURIComponent(t[l]);
  });
  return {
    renderedPath: r,
    remainingParams: x
  };
}
function N0(e, t, n) {
  var o = e.endsWith(t) ? e.slice(0, -t.length) : e;
  var r = n.startsWith(t) ? n.slice(t.length) : n;
  if (o === "" || r === "") {
    return o + r;
  } else {
    return o + t + r;
  }
}
gt.join = N0;
function ss(e) {
  return Object.keys(e).filter(function (n) {
    return as(e[n]);
  }).reduce(function (n, x) {
    n[x] = e[x];
    return n;
  }, {});
}
function as(e) {
  return e != null;
}
var Nn;
var ls = new Uint8Array(16);
function us() {
  if (!Nn && (Nn = typeof crypto !== "undefined" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== "undefined" && typeof msCrypto.getRandomValues === "function" && msCrypto.getRandomValues.bind(msCrypto), !Nn)) {
    throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
  }
  return Nn(ls);
}
const fs = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;
function ds(e) {
  return typeof e === "string" && fs.test(e);
}
var ke = [];
for (var v0 = 0; v0 < 256; ++v0) {
  ke.push((v0 + 256).toString(16).substr(1));
}
function ps(e) {
  var n = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
  var x = (ke[e[n + 0]] + ke[e[n + 1]] + ke[e[n + 2]] + ke[e[n + 3]] + "-" + ke[e[n + 4]] + ke[e[n + 5]] + "-" + ke[e[n + 6]] + ke[e[n + 7]] + "-" + ke[e[n + 8]] + ke[e[n + 9]] + "-" + ke[e[n + 10]] + ke[e[n + 11]] + ke[e[n + 12]] + ke[e[n + 13]] + ke[e[n + 14]] + ke[e[n + 15]]).toLowerCase();
  if (!ds(x)) {
    throw TypeError("Stringified UUID is invalid");
  }
  return x;
}
function hs(e, t, n) {
  e = e || {};
  var o = e.random || (e.rng || us)();
  o[6] = o[6] & 15 | 64;
  o[8] = o[8] & 63 | 128;
  if (t) {
    n = n || 0;
    for (var r = 0; r < 16; ++r) {
      t[n + r] = o[r];
    }
    return t;
  }
  return ps(o);
}
function Mn(e) {
  for (var n = 1; n < arguments.length; n++) {
    var x = arguments[n];
    for (var o in x) {
      e[o] = x[o];
    }
  }
  return e;
}
var ms = {
  read: function (e) {
    if (e[0] === "\"") {
      e = e.slice(1, -1);
    }
    return e.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent);
  },
  write: function (e) {
    return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g, decodeURIComponent);
  }
};
function M0(e, t) {
  function x(r, i, a) {
    if (typeof document !== "undefined") {
      a = Mn({}, t, a);
      if (typeof a.expires == "number") {
        a.expires = new Date(Date.now() + a.expires * 86400000);
      }
      if (a.expires) {
        a.expires = a.expires.toUTCString();
      }
      r = encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
      var p = "";
      for (var m in a) {
        if (a[m]) {
          p += "; " + m;
          if (a[m] !== true) {
            p += "=" + a[m].split(";")[0];
          }
        }
      }
      return document.cookie = r + "=" + e.write(i, r) + p;
    }
  }
  function o(r) {
    if (!(typeof document === "undefined" || arguments.length && !r)) {
      for (var a = document.cookie ? document.cookie.split("; ") : [], l = {}, p = 0; p < a.length; p++) {
        var m = a[p].split("=");
        var v = m.slice(1).join("=");
        try {
          var y = decodeURIComponent(m[0]);
          l[y] = e.read(v, y);
          if (r === y) {
            break;
          }
        } catch {}
      }
      if (r) {
        return l[r];
      } else {
        return l;
      }
    }
  }
  return Object.create({
    set: x,
    get: o,
    remove: function (r, i) {
      x(r, "", Mn({}, i, {
        expires: -1
      }));
    },
    withAttributes: function (r) {
      return M0(this.converter, Mn({}, this.attributes, r));
    },
    withConverter: function (r) {
      return M0(Mn({}, this.converter, r), this.attributes);
    }
  }, {
    attributes: {
      value: Object.freeze(t)
    },
    converter: {
      value: Object.freeze(e)
    }
  });
}
var Qo = M0(ms, {
  path: "/"
});
const bs = () => {
  const t = localStorage.getItem("uuid");
  if (t) {
    return t;
  }
  {
    const n = hs();
    localStorage.setItem("uuid", n);
    return n;
  }
};
const gs = e => {
  let n = "";
  let x = "";
  try {
    if (typeof document.hidden !== "undefined") {
      n = "hidden";
      x = "visibilitychange";
    } else if (typeof document.mozHidden !== "undefined") {
      n = "mozHidden";
      x = "mozvisibilitychange";
    } else if (typeof document.msHidden !== "undefined") {
      n = "msHidden";
      x = "msvisibilitychange";
    } else if (typeof document.webkitHidden !== "undefined") {
      n = "webkitHidden";
      x = "webkitvisibilitychange";
    }
    document.addEventListener(x, ws(function () {
      if (document[n]) {
        e(true);
      } else {
        console.log("页面显示");
        e(false);
      }
    }, 500));
  } catch (o) {
    console.log(o);
  }
};
const vs = e => {
  try {
    let n = navigator.userAgent.toLowerCase();
    let x = Xo("pid");
    console.log(x, n.includes("newsarticle"));
    if (x.includes("_oc") && n.includes("newsarticle")) {
      if (n.includes("newslite")) {
        Lt("snssdk35://category_feed?category=novel_channel&force_go_main=1");
      } else {
        Lt("snssdk143://category_feed?category=novel_channel");
      }
    }
    if (x.includes("_ks")) {
      const o = e && e.isJumpVideo;
      if (n.includes("nebula")) {
        Lt(
          //decode_error: o is not defined
          M(o ? 815 : 916));
        return;
      }
      if (n.includes("kwai")) {
        Lt(o ? "kwai://liveaggregatesquare" : "kwai://home/<USER>");
      }
    }
  } catch {}
};
const Lt = e => {
  var n = document.createElement("a");
  n.href = e;
  document.body.appendChild(n);
  n.click();
};
const Xo = (e, t) => {
  var x = new RegExp("(^|&)" + e + "=([^&]*)(&|$)");
  var o = window.location.search.substr(1).match(x);
  if (o != null) {
    if (t == 1) {
      return o[2].replace(/\./g, ":").toUpperCase();
    } else {
      return o[2];
    }
  } else {
    return "";
  }
};
const Wx = e => Qo.get(e);
const Kx = (e, t, n) => {
  let o = n;
  let r = new Date(new Date() * 1 + o * 1000);
  return Qo.set(e, t, {
    expires: r
  });
};
const ys = (e, t) => {
  console.log("开始了", t);
  if (t == "vivo") {
    var x = document.createElement("script");
    x.src = "https://h5.vivo.com.cn/qa/ifrstats/router.min.js";
    x.onload = function () {
      e();
    };
    document.head.appendChild(x);
  } else if (t == "oppo") {
    var x = document.createElement("script");
    x.src = "https://jits5.heytapdownload.com/cms-jits-heytapmobi-com/iframe/qa_router.min.js";
    x.onload = function () {
      e();
    };
    document.head.appendChild(x);
  } else {
    var x = document.createElement("script");
    x.src = "https://statres.quickapp.cn/quickapp/js/qa_router.min.js";
    x.onload = function () {
      e();
    };
    document.head.appendChild(x);
  }
};
function ws(e, t) {
  let n = 0;
  return function (...x) {
    const r = Date.now();
    if (r - n >= t) {
      n = r;
      return e.apply(this, x);
    }
  };
}
function ks(e) {
  const n = e.indexOf("clickid=");
  if (n === -1) {
    return null;
  }
  const x = n + 8;
  let o = e.indexOf("&", x);
  if (o === -1) {
    o = e.length;
  }
  return e.substring(x, o);
}
var er = {
  exports: {}
};
(function (e, t) {
  (function (x, o) {
    e.exports = o();
  })(P0, function () {
    return function () {
      var n = {
        686: function (r, i, a) {
          a.d(i, {
            default: function () {
              return Oe;
            }
          });
          var l = a(279);
          var p = a.n(l);
          var m = a(370);
          var v = a.n(m);
          var y = a(817);
          var E = a.n(y);
          function O(V) {
            try {
              return document.execCommand(V);
            } catch {
              return false;
            }
          }
          function q(b) {
            var j = E()(b);
            O("cut");
            return j;
          }
          var N = q;
          function D(V) {
            var j = document.documentElement.getAttribute("dir") === "rtl";
            var F = document.createElement("textarea");
            F.style.fontSize = "12pt";
            F.style.border = "0";
            F.style.padding = "0";
            F.style.margin = "0";
            F.style.position = "absolute";
            F.style[j ? "right" : "left"] = "-9999px";
            var L = window.pageYOffset || document.documentElement.scrollTop;
            F.style.top = `${L}px`;
            F.setAttribute("readonly", "");
            F.value = V;
            return F;
          }
          function G(b, j) {
            var L = D(b);
            j.container.appendChild(L);
            var B = E()(L);
            O("copy");
            L.remove();
            return B;
          }
          function oe(b) {
            var F = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {
              container: document.body
            };
            var L = "";
            if (typeof b === "string") {
              L = G(b, F);
            } else if (b instanceof HTMLInputElement && !["text", "search", "url", "tel", "password"].includes(b == null ? undefined : b.type)) {
              L = G(b.value, F);
            } else {
              L = E()(b);
              O("copy");
            }
            return L;
          }
          var fe = oe;
          function K(V) {
            if (typeof Symbol == "function" && typeof Symbol.iterator === "symbol") {
              K = function (F) {
                return typeof F;
              };
            } else {
              K = function (F) {
                if (F && typeof Symbol == "function" && F.constructor === Symbol && F !== Symbol.prototype) {
                  return "symbol";
                } else {
                  return typeof F;
                }
              };
            }
            return K(V);
          }
          function de() {
            var j = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
            var F = j.action;
            var L = F === undefined ? "copy" : F;
            var B = j.container;
            var H = j.target;
            var se = j.text;
            if (L !== "copy" && L !== "cut") {
              throw new Error("Invalid \"action\" value, use either \"copy\" or \"cut\"");
            }
            if (H !== undefined) {
              if (H && K(H) === "object" && H.nodeType === 1) {
                if (L === "copy" && H.hasAttribute("disabled")) {
                  throw new Error("Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute");
                }
                if (L === "cut" && (H.hasAttribute("readonly") || H.hasAttribute("disabled"))) {
                  throw new Error("Invalid \"target\" attribute. You can't cut text from elements with \"readonly\" or \"disabled\" attributes");
                }
              } else {
                throw new Error("Invalid \"target\" value, use a valid Element");
              }
            }
            if (se) {
              return fe(se, {
                container: B
              });
            }
            if (H) {
              if (L === "cut") {
                return N(H);
              } else {
                return fe(H, {
                  container: B
                });
              }
            }
          }
          var je = de;
          function ve(V) {
            if (typeof Symbol === "function" && typeof Symbol.iterator == "symbol") {
              ve = function (F) {
                return typeof F;
              };
            } else {
              ve = function (F) {
                if (F && typeof Symbol === "function" && F.constructor === Symbol && F !== Symbol.prototype) {
                  return "symbol";
                } else {
                  return typeof F;
                }
              };
            }
            return ve(V);
          }
          function Te(V, b) {
            if (!(V instanceof b)) {
              throw new TypeError("Cannot call a class as a function");
            }
          }
          function De(V, b) {
            for (var F = 0; F < b.length; F++) {
              var L = b[F];
              L.enumerable = L.enumerable || false;
              L.configurable = true;
              if ("value" in L) {
                L.writable = true;
              }
              Object.defineProperty(V, L.key, L);
            }
          }
          function Be(V, b, j) {
            if (b) {
              De(V.prototype, b);
            }
            if (j) {
              De(V, j);
            }
            return V;
          }
          function Qe(V, b) {
            if (typeof b != "function" && b !== null) {
              throw new TypeError("Super expression must either be null or a function");
            }
            V.prototype = Object.create(b && b.prototype, {
              constructor: {
                value: V,
                writable: true,
                configurable: true
              }
            });
            if (b) {
              $e(V, b);
            }
          }
          function $e(V, b) {
            $e = Object.setPrototypeOf || function (L, B) {
              L.__proto__ = B;
              return L;
            };
            return $e(V, b);
          }
          function rt(V) {
            var b = he();
            return function () {
              var L = ie(V);
              var B;
              if (b) {
                var H = ie(this).constructor;
                B = Reflect.construct(L, arguments, H);
              } else {
                B = L.apply(this, arguments);
              }
              return dt(this, B);
            };
          }
          function dt(V, b) {
            if (b && (ve(b) === "object" || typeof b == "function")) {
              return b;
            } else {
              return pt(V);
            }
          }
          function pt(V) {
            if (V === undefined) {
              throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
            }
            return V;
          }
          function he() {
            if (typeof Reflect === "undefined" || !Reflect.construct) {
              return false;
            }
            if (Reflect.construct.sham) {
              return false;
            }
            if (typeof Proxy === "function") {
              return true;
            }
            try {
              Date.prototype.toString.call(Reflect.construct(Date, [], function () {}));
              return true;
            } catch {
              return false;
            }
          }
          function ie(V) {
            ie = Object.setPrototypeOf ? Object.getPrototypeOf : function (F) {
              return F.__proto__ || Object.getPrototypeOf(F);
            };
            return ie(V);
          }
          function te(V, b) {
            var F = `data-clipboard-${V}`;
            if (b.hasAttribute(F)) {
              return b.getAttribute(F);
            }
          }
          var He = function (V) {
            Qe(F, V);
            var j = rt(F);
            function F(L, B) {
              var se;
              Te(this, F);
              se = j.call(this);
              se.resolveOptions(B);
              se.listenClick(L);
              return se;
            }
            Be(F, [{
              key: "resolveOptions",
              value: function () {
                var H = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
                this.action = typeof H.action === "function" ? H.action : this.defaultAction;
                this.target = typeof H.target == "function" ? H.target : this.defaultTarget;
                this.text = typeof H.text == "function" ? H.text : this.defaultText;
                this.container = ve(H.container) === "object" ? H.container : document.body;
              }
            }, {
              key: "listenClick",
              value: function (B) {
                var se = this;
                this.listener = v()(B, "click", function (be) {
                  return se.onClick(be);
                });
              }
            }, {
              key: "onClick",
              value: function (B) {
                var se = B.delegateTarget || B.currentTarget;
                var be = this.action(se) || "copy";
                var Ve = je({
                  action: be,
                  container: this.container,
                  target: this.target(se),
                  text: this.text(se)
                });
                this.emit(
                  //decode_error: Ve is not defined
                  M(Ve ? 277 : 482), {
                    action: be,
                    text: Ve,
                    trigger: se,
                    clearSelection: function () {
                      if (se) {
                        se.focus();
                      }
                      window.getSelection().removeAllRanges();
                    }
                  });
              }
            }, {
              key: "defaultAction",
              value: function (B) {
                return te("action", B);
              }
            }, {
              key: "defaultTarget",
              value: function (B) {
                var H = te("target", B);
                if (H) {
                  return document.querySelector(H);
                }
              }
            }, {
              key: "defaultText",
              value: function (B) {
                return te("text", B);
              }
            }, {
              key: "destroy",
              value: function () {
                this.listener.destroy();
              }
            }], [{
              key: "copy",
              value: function (B) {
                var se = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {
                  container: document.body
                };
                return fe(B, se);
              }
            }, {
              key: "cut",
              value: function (B) {
                return N(B);
              }
            }, {
              key: "isSupported",
              value: function () {
                var H = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ["copy", "cut"];
                var se = typeof H == "string" ? [H] : H;
                var be = !!document.queryCommandSupported;
                se.forEach(function (Ve) {
                  be = be && !!document.queryCommandSupported(Ve);
                });
                return be;
              }
            }]);
            return F;
          }(p());
          var Oe = He;
        },
        828: function (r) {
          var a = 9;
          if (typeof Element !== "undefined" && !Element.prototype.matches) {
            var l = Element.prototype;
            l.matches = l.matchesSelector || l.mozMatchesSelector || l.msMatchesSelector || l.oMatchesSelector || l.webkitMatchesSelector;
          }
          function p(m, v) {
            for (; m && m.nodeType !== a;) {
              if (typeof m.matches === "function" && m.matches(v)) {
                return m;
              }
              m = m.parentNode;
            }
          }
          r.exports = p;
        },
        438: function (r, i, a) {
          var p = a(828);
          function m(E, O, q, N, D) {
            var oe = y.apply(this, arguments);
            E.addEventListener(q, oe, D);
            return {
              destroy: function () {
                E.removeEventListener(q, oe, D);
              }
            };
          }
          function v(E, O, q, N, D) {
            if (typeof E.addEventListener === "function") {
              return m.apply(null, arguments);
            } else if (typeof q == "function") {
              return m.bind(null, document).apply(null, arguments);
            } else {
              if (typeof E === "string") {
                E = document.querySelectorAll(E);
              }
              return Array.prototype.map.call(E, function (oe) {
                return m(oe, O, q, N, D);
              });
            }
          }
          function y(E, O, q, N) {
            return function (D) {
              D.delegateTarget = p(D.target, O);
              if (D.delegateTarget) {
                N.call(E, D);
              }
            };
          }
          r.exports = v;
        },
        879: function (r, i) {
          i.node = function (l) {
            return l !== undefined && l instanceof HTMLElement && l.nodeType === 1;
          };
          i.nodeList = function (l) {
            var m = Object.prototype.toString.call(l);
            return l !== undefined && (m === "[object NodeList]" || m === "[object HTMLCollection]") && "length" in l && (l.length === 0 || i.node(l[0]));
          };
          i.string = function (l) {
            return typeof l == "string" || l instanceof String;
          };
          i.fn = function (l) {
            var m = Object.prototype.toString.call(l);
            return m === "[object Function]";
          };
        },
        370: function (r, i, a) {
          var l = a(879);
          var p = a(438);
          function m(O, q, N) {
            if (!O && !q && !N) {
              throw new Error("Missing required arguments");
            }
            if (!l.string(q)) {
              throw new TypeError("Second argument must be a String");
            }
            if (!l.fn(N)) {
              throw new TypeError("Third argument must be a Function");
            }
            if (l.node(O)) {
              return v(O, q, N);
            }
            if (l.nodeList(O)) {
              return y(O, q, N);
            }
            if (l.string(O)) {
              return E(O, q, N);
            }
            throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList");
          }
          function v(O, q, N) {
            O.addEventListener(q, N);
            return {
              destroy: function () {
                O.removeEventListener(q, N);
              }
            };
          }
          function y(O, q, N) {
            Array.prototype.forEach.call(O, function (G) {
              G.addEventListener(q, N);
            });
            return {
              destroy: function () {
                Array.prototype.forEach.call(O, function (oe) {
                  oe.removeEventListener(q, N);
                });
              }
            };
          }
          function E(O, q, N) {
            return p(document.body, O, q, N);
          }
          r.exports = m;
        },
        817: function (r) {
          function i(a) {
            var p;
            if (a.nodeName === "SELECT") {
              a.focus();
              p = a.value;
            } else if (a.nodeName === "INPUT" || a.nodeName === "TEXTAREA") {
              var m = a.hasAttribute("readonly");
              if (!m) {
                a.setAttribute("readonly", "");
              }
              a.select();
              a.setSelectionRange(0, a.value.length);
              if (!m) {
                a.removeAttribute("readonly");
              }
              p = a.value;
            } else {
              if (a.hasAttribute("contenteditable")) {
                a.focus();
              }
              var v = window.getSelection();
              var y = document.createRange();
              y.selectNodeContents(a);
              v.removeAllRanges();
              v.addRange(y);
              p = v.toString();
            }
            return p;
          }
          r.exports = i;
        },
        279: function (r) {
          function a() {}
          a.prototype = {
            on: function (l, p, m) {
              var y = this.e || (this.e = {});
              (y[l] || (y[l] = [])).push({
                fn: p,
                ctx: m
              });
              return this;
            },
            once: function (l, p, m) {
              var v = this;
              function y() {
                v.off(l, y);
                p.apply(m, arguments);
              }
              y._ = p;
              return this.on(l, y, m);
            },
            emit: function (l) {
              var m = [].slice.call(arguments, 1);
              var v = ((this.e || (this.e = {}))[l] || []).slice();
              var y = 0;
              var E = v.length;
              for (y; y < E; y++) {
                v[y].fn.apply(v[y].ctx, m);
              }
              return this;
            },
            off: function (l, p) {
              var v = this.e || (this.e = {});
              var y = v[l];
              var E = [];
              if (y && p) {
                for (var O = 0, q = y.length; O < q; O++) {
                  if (y[O].fn !== p && y[O].fn._ !== p) {
                    E.push(y[O]);
                  }
                }
              }
              if (E.length) {
                v[l] = E;
              } else {
                delete v[l];
              }
              return this;
            }
          };
          r.exports = a;
          r.exports.TinyEmitter = a;
        }
      };
      var x = {};
      function o(r) {
        if (x[r]) {
          return x[r].exports;
        }
        var a = x[r] = {
          exports: {}
        };
        n[r](a, a.exports, o);
        return a.exports;
      }
      (function () {
        o.n = function (r) {
          var a = r && r.__esModule ? function () {
            return r.default;
          } : function () {
            return r;
          };
          o.d(a, {
            a
          });
          return a;
        };
      })();
      (function () {
        o.d = function (r, i) {
          for (var l in i) {
            if (o.o(i, l) && !o.o(r, l)) {
              Object.defineProperty(r, l, {
                enumerable: true,
                get: i[l]
              });
            }
          }
        };
      })();
      (function () {
        o.o = function (r, i) {
          return Object.prototype.hasOwnProperty.call(r, i);
        };
      })();
      return o(686);
    }().default;
  });
})(er);
const Ss = rs(er.exports);
const tr = "3.7.5";
const Cs = tr;
const Rs = typeof atob === "function";
const Is = typeof btoa === "function";
const en = typeof Buffer === "function";
const Jx = typeof TextDecoder === "function" ? new TextDecoder() : undefined;
const Gx = typeof TextEncoder == "function" ? new TextEncoder() : undefined;
const Es = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
const an = Array.prototype.slice.call(Es);
const Fn = (e => {
  let n = {};
  e.forEach((x, o) => n[x] = o);
  return n;
})(an);
const As = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;
const Se = String.fromCharCode.bind(String);
const Yx = typeof Uint8Array.from === "function" ? Uint8Array.from.bind(Uint8Array) : e => new Uint8Array(Array.prototype.slice.call(e, 0));
const nr = e => e.replace(/=/g, "").replace(/[+\/]/g, t => t == "+" ? "-" : "_");
const xr = e => e.replace(/[^A-Za-z0-9\+\/]/g, "");
const or = e => {
  let n;
  let x;
  let o;
  let r;
  let i = "";
  const a = e.length % 3;
  for (let l = 0; l < e.length;) {
    if ((x = e.charCodeAt(l++)) > 255 || (o = e.charCodeAt(l++)) > 255 || (r = e.charCodeAt(l++)) > 255) {
      throw new TypeError("invalid character found");
    }
    n = x << 16 | o << 8 | r;
    i += an[n >> 18 & 63] + an[n >> 12 & 63] + an[n >> 6 & 63] + an[n & 63];
  }
  if (a) {
    return i.slice(0, a - 3) + "===".substring(a);
  } else {
    return i;
  }
};
const xx = Is ? e => btoa(e) : en ? e => Buffer.from(e, "binary").toString("base64") : or;
const F0 = en ? e => Buffer.from(e).toString("base64") : e => {
  const n = 4096;
  let x = [];
  for (let o = 0, r = e.length; o < r; o += n) {
    x.push(Se.apply(null, e.subarray(o, o + n)));
  }
  return xx(x.join(""));
};
const qn = (e, t = false) => t ? nr(F0(e)) : F0(e);
const Ts = e => {
  if (e.length < 2) {
    var n = e.charCodeAt(0);
    if (n < 128) {
      return e;
    } else if (n < 2048) {
      return Se(n >>> 6 | 192) + Se(n & 63 | 128);
    } else {
      return Se(n >>> 12 & 15 | 224) + Se(n >>> 6 & 63 | 128) + Se(n & 63 | 128);
    }
  } else {
    var n = 65536 + (e.charCodeAt(0) - 55296) * 1024 + (e.charCodeAt(1) - 56320);
    return Se(n >>> 18 & 7 | 240) + Se(n >>> 12 & 63 | 128) + Se(n >>> 6 & 63 | 128) + Se(n & 63 | 128);
  }
};
const Os = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
const rr = e => e.replace(Os, Ts);
const Zx = en ? e => Buffer.from(e, "utf8").toString("base64") : Gx ? e => F0(Gx.encode(e)) : e => xx(rr(e));
const _t = (e, t = false) => t ? nr(Zx(e)) : Zx(e);
const Qx = e => _t(e, true);
const Us = /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g;
const Ps = e => {
  switch (e.length) {
    case 4:
      var n = (e.charCodeAt(0) & 7) << 18 | (e.charCodeAt(1) & 63) << 12 | (e.charCodeAt(2) & 63) << 6 | e.charCodeAt(3) & 63;
      var x = n - 65536;
      return Se((x >>> 10) + 55296) + Se((x & 1023) + 56320);
    case 3:
      return Se((e.charCodeAt(0) & 15) << 12 | (e.charCodeAt(1) & 63) << 6 | e.charCodeAt(2) & 63);
    default:
      return Se((e.charCodeAt(0) & 31) << 6 | e.charCodeAt(1) & 63);
  }
};
const ir = e => e.replace(Us, Ps);
const cr = e => {
  e = e.replace(/\s+/g, "");
  if (!As.test(e)) {
    throw new TypeError("malformed base64.");
  }
  e += "==".slice(2 - (e.length & 3));
  let n;
  let x = "";
  let o;
  let r;
  for (let i = 0; i < e.length;) {
    n = Fn[e.charAt(i++)] << 18 | Fn[e.charAt(i++)] << 12 | (o = Fn[e.charAt(i++)]) << 6 | (r = Fn[e.charAt(i++)]);
    x += o === 64 ? Se(n >> 16 & 255) : r === 64 ? Se(n >> 16 & 255, n >> 8 & 255) : Se(n >> 16 & 255, n >> 8 & 255, n & 255);
  }
  return x;
};
const ox = Rs ? e => atob(xr(e)) : en ? e => Buffer.from(e, "base64").toString("binary") : cr;
const sr = en ? e => Yx(Buffer.from(e, "base64")) : e => Yx(ox(e).split("").map(t => t.charCodeAt(0)));
const ar = e => sr(lr(e));
const js = en ? e => Buffer.from(e, "base64").toString("utf8") : Jx ? e => Jx.decode(sr(e)) : e => ir(ox(e));
const lr = e => xr(e.replace(/[-_]/g, t => t == "-" ? "+" : "/"));
const B0 = e => js(lr(e));
const Ns = e => {
  if (typeof e != "string") {
    return false;
  }
  const n = e.replace(/\s+/g, "").replace(/={0,2}$/, "");
  return !/[^\s0-9a-zA-Z\+/]/.test(n) || !/[^\s0-9a-zA-Z\-_]/.test(n);
};
const ur = e => ({
  value: e,
  enumerable: false,
  writable: true,
  configurable: true
});
const fr = function () {
  const t = (n, x) => Object.defineProperty(String.prototype, n, ur(x));
  t("fromBase64", function () {
    return B0(this);
  });
  t("toBase64", function (n) {
    return _t(this, n);
  });
  t("toBase64URI", function () {
    return _t(this, true);
  });
  t("toBase64URL", function () {
    return _t(this, true);
  });
  t("toUint8Array", function () {
    return ar(this);
  });
};
const dr = function () {
  const t = (n, x) => Object.defineProperty(Uint8Array.prototype, n, ur(x));
  t("toBase64", function (n) {
    return qn(this, n);
  });
  t("toBase64URI", function () {
    return qn(this, true);
  });
  t("toBase64URL", function () {
    return qn(this, true);
  });
};
const Ms = () => {
  fr();
  dr();
};
const Xx = {
  version: tr,
  VERSION: Cs,
  atob: ox,
  atobPolyfill: cr,
  btoa: xx,
  btoaPolyfill: or,
  fromBase64: B0,
  toBase64: _t,
  encode: _t,
  encodeURI: Qx,
  encodeURL: Qx,
  utob: rr,
  btou: ir,
  decode: B0,
  isValid: Ns,
  fromUint8Array: qn,
  toUint8Array: ar,
  extendString: fr,
  extendUint8Array: dr,
  extendBuiltins: Ms
};
const Fs = {
  class: "m-t[0px]"
};
const Bs = ["src"];
const Vs = {
  class: "m-t[200px]"
};
const zs = ["src"];
const Ls = {
  key: 0,
  class: "container flex flex-col items-center"
};
const Hs = {
  class: "box-border relative max-w-[750px] w-full flex flex-col overflow-hidden"
};
const qs = ["src"];
const Ds = ["src"];
const $s = {
  class: "text-center color-#666666 text-20px pt-10 pb-2"
};
const _s = {
  key: 0,
  class: "color-#666666 text-center mb-4"
};
const Ws = ["src"];
const Ks = {
  key: 0,
  id: "popup",
  class: "z-2 fixed left-0 right-0 bottom-0 w-100% h-100%",
  style: {
    "background-color": "rgba(0, 0, 0, 0.6)"
  }
};
const Js = J("img", {
  class: "w-20px",
  src: "https://cdn.tianjinzhaofa.cn/web/image/Frame.png",
  alt: ""
}, null, -1);
const Gs = [Js];
const Ys = kc("<input class=\"w-93% h-50px bg-#F2F5F9 mt-10px px-12px text-16px border-none\" type=\"text\" id=\"phone\" name=\"phone\" pattern=\"[0-9]{11}\" placeholder=\"请输入手机号\" maxlength=\"11\" required><span id=\"phoneError\" class=\"mt-10px text-13px color-red\"></span><div class=\"flex items-center mt-16px\"><input type=\"radio\" id=\"agree\" name=\"agreement\"><span class=\"text-14px color-#666666 mt-3px\">我已经阅读并同意<a href=\"https://cdn.tianjinzhaofa.cn/web/image/privacy.html\">《个人信息授权及隐私政策》</a></span></div>", 3);
const Zs = {
  key: 1,
  class: "container mx-auto max-w-[750px]"
};
const Qs = {
  class: "relative box-border flex items-center w-full p-4 bg-white text-sm mb-2"
};
const Xs = {
  class: "flex flex-col"
};
const ea = {
  key: 0
};
const ta = {
  class: "flex"
};
const na = ["href"];
const xa = ["href"];
const oa = ["href"];
const ra = J("img", {
  src: "https://cdn.tianjinzhaofa.cn/portal/common/tousu.png",
  class: "w-[3.5rem]",
  alt: "",
  srcset: ""
}, null, -1);
const ia = [ra];
const ca = {
  class: "box-border relative"
};
const sa = ["src"];
const aa = ["src"];
const la = {
  class: "flex flex-col items-center z-2 relative pb-8"
};
const ua = ["src"];
const fa = {
  class: "flex flex-col text-center text-white my-2 leading-9 w-90%"
};
const da = {
  class: "text-[26px] mt-2"
};
const pa = {
  class: "text-[22px]"
};
const ha = {
  class: "text-[18px] mb-2"
};
const ma = ["href"];
const ba = ["src"];
const ga = {
  class: "text-center py-4 text-gray-500 relative z-3 bg-#ffffff"
};
const va = {
  class: "text-xs mb-2 px-12"
};
const ya = {
  class: "text-20px mb-2"
};
const wa = {
  key: 0,
  class: "mb-2"
};
const ka = J("div", {
  id: "qa-web"
}, null, -1);
const eo = "u2";
const to = "v.u2.25.0707-1510";
const Sa = "https://api.tianjinzhaofa.cn/portal-conf";
const Ca = {
  __name: "App",
  setup(e) {
    const n = new URLSearchParams(window.location.search);
    const x = n.get("tfchannel");
    const o = n.get("debug");
    const r = n.get("adid");
    const i = n.get("creativeid");
    const a = n.get("creativetype");
    const l = n.get("pid");
    const p = n.get("bd_vid");
    const m = n.get("pageId");
    const v = n.get("lbid");
    const y = n.get("bookId");
    const E = n.get("chapterId");
    const O = n.get("mark_id");
    const q = O || p || n.get("clickid");
    let N = Xo("tid");
    const D = n.get("rd_type");
    const G = n.get("u_aid");
    const oe = n.get("u_did");
    const fe = n.get("u_cid");
    const K = n.get("u_vid");
    const de = n.get("u_pid");
    const je = n.get("u_referrer");
    const ve = n.get("u_channel");
    const Te = n.get("account_id");
    const De = n.get("channel");
    const Be = n.get("oaidmd5");
    const Qe = n.get("imeimd5");
    const $e = n.get("back_name");
    const rt = n.get("back_url");
    const dt = n.get("back_pkg");
    let pt = n.get("cos_pop");
    const he = n.get("csite");
    const ie = n.get("union_site");
    const te = n.get("mgc_cb");
    const He = n.get("pos");
    const Oe = Z(false);
    const V = Z(false);
    const b = Z(false);
    const j = Z("");
    const F = Z("");
    const L = Z("");
    const B = Z("");
    const H = Z("");
    const se = Z("");
    const be = Z("");
    const Ve = Z("");
    const it = Z("");
    const d = Z("");
    const h = Z("");
    const g = Z("");
    const w = Z("");
    const k = Z("");
    const S = Z("");
    const A = Z("");
    const C = Z([]);
    const R = Z("");
    const T = Z(false);
    const I = Z("");
    const z = Z(true);
    const U = Z("");
    const $ = Z("");
    const _ = Z(false);
    const ee = Z("");
    const ce = Z("");
    const ne = Z("");
    const me = Z("");
    const W = Z("");
    const we = Z("");
    const Pt = Z("");
    const _e = Z("");
    const jt = Z(1);
    const Ue = Z(false);
    const Ne = Z(false);
    const Nt = Z("");
    try {
      if (N) {
        if (N.indexOf("%") == -1) {
          N = encodeURIComponent(N);
        }
        N = Xx.toBase64(N);
      }
    } catch {}
    const rx = bs();
    let Mt = Z(0);
    const ix = Z("");
    const cx = document.visibilityState;
    let Q = {};
    let tn = 0;
    let gn = 8;
    const le = fn({
      QUICK_APP_ST_CHANNEL: l,
      topic_id: "b8a21ad6-ea87-45fa-b289-549e2fa68f16",
      landVersion: eo,
      portalVersion: to,
      hapType: "dp-auto",
      adid: r,
      creativeid: i,
      creativetype: a,
      clickid: q,
      uuid: rx,
      pkg: "",
      pid: l,
      channel: "",
      tid: N,
      lbid: v,
      pageid: m,
      tfchannel: x,
      rd_type: D,
      u_aid: G,
      u_did: oe,
      u_cid: fe,
      u_vid: K,
      u_pid: de,
      bd_vid: p,
      u_referrer: je,
      u_channel: ve,
      account_id: Te,
      oaidmd5: Be,
      imeimd5: Qe,
      landPopIndex: jt,
      csite: he,
      union_site: ie,
      visbState: cx,
      back_name: $e,
      back_url: rt,
      back_pkg: dt,
      cos_pop: pt,
      vid: l,
      mgc_cb: te,
      pos: He
    });
    const Ie = fn({
      pid: l,
      QUICK_APP_ST_CHANNEL: l,
      landVersion: eo,
      portalVersion: to,
      hapType: "dp-auto",
      clickid: q,
      adid: r,
      creativeid: i,
      creativetype: a,
      channel: "",
      uuid: rx,
      tid: N,
      lbid: v,
      pageid: m,
      tfchannel: x,
      rd_type: D,
      u_aid: G,
      u_did: oe,
      u_cid: fe,
      u_vid: K,
      u_pid: de,
      bd_vid: p,
      u_referrer: je,
      u_channel: ve,
      account_id: Te,
      oaidmd5: Be,
      imeimd5: Qe,
      bookId: y,
      chapterId: E,
      landPopIndex: jt,
      csite: he,
      union_site: ie,
      visbState: cx,
      back_name: $e,
      back_url: rt,
      back_pkg: dt,
      vid: l,
      mgc_cb: te,
      pos: He,
      change_pkg_type: ""
    });
    let ht = "";
    let Ft = [];
    Fo(async () => {
      try {
        const f = await fetch(Sa + "?pid=" + l + "&channel=" + De + "&account_id=" + Te + "&oaidmd5=" + Be + "&clickid=" + q);
        const {
          data: u
        } = await f.json();
        Q = u;
        Pt.value = u.clipboard;
        V.value = u.isAutomatic;
        Oe.value = u.isComplained;
        b.value = u.isCookie;
        j.value = u.packageName;
        F.value = u.appName;
        L.value = u.appVersion;
        B.value = u.updatedOn;
        H.value = u.companyName;
        se.value = u.agreementUrl;
        be.value = u.privacyUrl;
        Ve.value = u.introduction;
        it.value = u.logoUrl;
        d.value = u.backgroundTopUrl;
        h.value = u.backgroundBottomUrl;
        g.value = u.screenshotUrl;
        w.value = u.complainButtonUrl;
        W.value = u.brand;
        k.value = zt(u.complainUrl, {
          pid: l,
          package: u.packageName,
          channel: u.channel
        });
        S.value = u.page;
        A.value = u.channel;
        C.value = u.slogan;
        R.value = u.jumpTip;
        T.value = u.isIcpDisplay;
        I.value = u.icpRecord;
        z.value = u.isMetaDataDisplay;
        we.value = u.icpCompanyName;
        Ie.channel = A.value;
        Ie.uuid = le.uuid;
        Ie.change_pkg_type = u.change_pkg_type;
        ix.value = "#";
        U.value = u.clickToOpenUrl;
        $.value = u.b;
        le.pkg = u.packageName;
        le.channel = u.channel;
        _e.value = u.jumpToken;
        le.mode = u.mode;
        pr();
        Ur();
      } catch (f) {
        console.log("error", f);
      }
    });
    const pr = () => {
      const {
        extraConfig: f
      } = Q;
      if (q && q == "http://ad.partner.gifshow.com/track/activate?callback") {
        let P = ks(location.href);
        Ie.clickid = P;
        le.clickid = P;
      }
      if (!D) {
        Pr();
        ct({
          eventName: "webland_init"
        });
      }
      if (l.includes("auto")) {
        S.value = "index";
        le.pkg = Q.packageA;
        j.value = Q.packageA;
        console.log("来了哈");
        return;
      }
      if (f) {
        if (typeof f.closeAttr === "number" && f.closeAttr) {
          S.value = "index";
          le.pkg = null;
          return;
        }
        if (typeof f.backRetryCount === "number") {
          gn = f.backRetryCount;
        }
        if (W.value == "xiaomi") {
          gn = 5;
        }
        if (W.value == "honor") {
          gn = 0;
        }
        try {
          if (f.backGlobal) {
            Ft = JSON.parse(f.backGlobal);
          }
        } catch {}
      }
      if (!V.value) {
        return;
      }
      if (r && (r.includes("_") || r.includes("$") || r.includes("{"))) {
        console.log("__AID__", false);
        S.value = "index";
        le.pkg = Q.packageA;
        j.value = Q.packageA;
        return;
      }
      if (A.value == "oppo" && oe && oe == "$ad$") {
        return;
      }
      if (f && f.specialPop) {
        Nt.value = f.specialPop;
      }
      if (!V.value && !o || pt == 1) {
        _.value = false;
      } else if (!Wx("RedirectPop")) {
        yn();
      }
      Ir();
      let u = navigator.userAgent.toLowerCase();
      if (f && f.toKkPage) {
        const P = window.location.search + "&rd_type=st";
        ht = "https://cdn.wuhanshengwan.cn/portal/core/index.html" + P;
        if (r0()) {
          if (u.includes("nebula")) {
            ct({
              eventName: "webland_p_look"
            });
            window.location.href = "ksnebula://merchantwebview?url=" + encodeURIComponent(ht);
            return;
          }
          if (u.includes("kswebview")) {
            ct({
              eventName: "webland_p_look"
            });
            window.location.href = "kwai://merchantwebview?url=" + encodeURIComponent(ht);
            return;
          }
        }
        if (window.Kwai) {
          try {
            let ge = "cmVwb3J0";
            ge = Xx.decode(ge);
            const st = "https://cdn.wuhanshengwan.cn/portal/kfc/index.html" + P;
            if (u.includes("nebula")) {
              window.location.href = "ksnebula://" + ge + "?url=" + encodeURIComponent(st);
              return;
            }
            if (u.includes("kswebview")) {
              window.location.href = "kwai://" + ge + "?url=" + encodeURIComponent(st);
              return;
            }
          } catch {}
          let ae = "webview";
          ht = "https://cdn.wuhanshengwan.cn/portal/core/index.html" + P;
          if (u.includes("nebula")) {
            window.location.href = "ksnebula://" + ae + "?url=" + encodeURIComponent(ht);
            return;
          }
          if (u.includes("kswebview")) {
            window.location.href = "kwai://" + ae + "?url=" + encodeURIComponent(ht);
            return;
          }
        } else {
          if (u.includes("kswebview") || u.includes("nebula")) {
            window.location.href = "ksnebula://webview?url=" + encodeURIComponent(ht);
          } else {
            window.location.href = ht;
          }
          return;
        }
      }
      if (i0()) {
        ct({
          eventName: "webland_auto_frequency"
        });
        return;
      }
      if (Q.universalAdvertisingEnabled) {
        vn();
      }
      if (!l.includes("vvzn")) {
        br();
      }
      if (f && f.isAllJump) {
        mr();
      }
      ys(() => {
        switch (W.value) {
          case "vivo":
            sx();
            break;
          case "oppo":
            hr();
            wn();
            break;
          case "xiaomi":
            fx();
            wn();
            break;
          case "huawei":
            c0();
            break;
          case "honor":
            c0();
            break;
        }
        if (o) {
          wn();
        }
      }, W.value);
      Ne.value = true;
      s0("dp-auto");
    };
    const r0 = () => {
      try {
        const f = (window.navigator.platform || "").toLowerCase();
        return f.includes("win") || f.includes("intel");
      } catch (f) {
        console.error("Error determining platform:", f);
        return false;
      }
    };
    const hr = () => {
      if (!r0() && Q.extraConfig && typeof Q.extraConfig.opBridge === "number") {
        if (Q.extraConfig.opBridge == 1) {
          const f = encodeURIComponent("https://cdn.wuhanshengwan.cn/portal/rand/index.html" + window.location.search + "&rd_type=st");
          window.location.href = "pictorial://pictorial.com/common/webview_activity?url=" + f;
        } else if (Q.extraConfig.opBridge == 2) {
          const f = encodeURIComponent("https://cdn.wuhanshengwan.cn/portal/rand/index.html" + window.location.search + "&rd_type=st");
          window.location.href = "oaps://mk/web?u=" + f;
        }
      }
    };
    const sx = (c = "hib-auto") => {
      if (!r0() && window.Kwai && Q.extraConfig && typeof Q.extraConfig.isUseVo === "number" && Q.extraConfig.isUseVo == 110) {
        document.title = "限时优惠";
        const u = encodeURIComponent(JSON.stringify({
          ...Ie,
          hapType: c
        }));
        const P = encodeURIComponent("https://qapp-h5-pre.vivo.com.cn/?packageName=" + le.pkg + "&path=" + S.value + "&params=" + u);
        window.location.href = "hiboard://vivo.com/oepration?url=" + P;
      }
    };
    const mr = () => {
      try {
        const u = "https://cdn.wuhanshengwan.cn/portal/core/index.html" + window.location.search + "&rd_type=st";
        window.location.href = "ksnebula://yodaweb?url=" + encodeURIComponent(u);
        if (W.value != "oppo") {
          if (W.value != "vivo") {
            if (W.value == "huawei") {
              var f = ze("gm-auto", true);
              f = encodeURIComponent(f);
              window.location.href = "higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + f + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
            } else if (W.value == "honor") {
              var f = ze("gm-auto", false);
              f = encodeURIComponent(f);
              window.location.href = "higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + f + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
            }
          }
        }
      } catch (u) {
        console.log("error", u);
      }
    };
    let nn = 0;
    const ax = () => {
      try {
        const {
          extraConfig: f
        } = Q;
        if (f && f.soonAbn) {
          return;
        }
        if (Ft && Array.isArray(Ft) && Ft.length > 0) {
          nn = nn < Ft.length ? nn : 0;
          const u = Ft[nn];
          if (u) {
            nn++;
            console.log("per ...", u.package);
            le.pkg = u.package;
            j.value = u.package;
            S.value = u.page;
            _e.value = u.jump_token;
            le.message = "per";
            Ie.message = "per";
          }
        }
      } catch (f) {
        console.error(f);
      }
    };
    const vn = () => {
      if (Q.universalAdvertisingEnabled) {
        let f = Q.huaweiFirst ? "huawei" : W.value;
        let u = ux(f);
        if ((!u || u.length <= 0) && ["huawei", "honor"].includes(W.value)) {
          f = f == "huawei" ? "honor" : "huawei";
          u = ux(f);
          console.log("change-", f);
        }
        if (Array.isArray(u) && u.length > 0) {
          if (tn < u.length) {
            const P = u[tn];
            const ae = P.package.replace(" huawei", "");
            Ie.pid = P.pid;
            le.pid = P.pid;
            le.pkg = ae;
            j.value = ae;
            S.value = P.page;
            _e.value = P.jump_token;
            Ie.change_pkg_type = P.change_pkg_type;
            Q.day_start_num = P.day_start_num;
            tn++;
            return true;
          } else {
            console.log("an is none");
            tn = 0;
            vn();
          }
        }
        return false;
      }
    };
    const yn = () => {
      _.value = true;
      const f = Math.floor(Math.random() * 7) + 1;
      jt.value = f;
    };
    let lx = 0;
    const br = () => {
      try {
        window.history.pushState(null, null, "#");
        window.addEventListener("popstate", function (f) {
          lx++;
          if (lx < 15) {
            yn();
            window.history.pushState(null, null, "#");
          }
        });
      } catch {}
    };
    const ux = c => {
      const u = Q.universalAdvertisingConfig;
      const P = u.find(ae => ae.brand === c);
      if (P) {
        return P.config;
      } else {
        return null;
      }
    };
    const i0 = c => {
      let u = 0;
      if (Q.extraConfig && typeof Q.extraConfig.frequencyTime === "number") {
        u = Q.extraConfig.frequencyTime;
      }
      if (u > 0) {
        const P = Wx("IsFrequency");
        if (P && P == "true") {
          console.log("frequency...", Oe.value);
          return true;
        }
        if (Oe.value) {
          Kx("IsFrequency", true, 172800);
        } else {
          Kx("IsFrequency", true, u);
        }
      }
      return false;
    };
    const wn = c => {
      try {
        Ar(c);
        // TOLOOK
        setTimeout(() => {
          gr(c);
          routeToQuickapp("qa-btn-" + le.pkg + Mt.value);
        }, 500);
      } catch (u) {
        console.log("qaRouter error", u);
      }
    };
    const gr = c => {
      try {
        if (W.value == "oppo") {
          const Rn = dx(c || "tzu-auto");
          let l0 = new URLSearchParams(Rn).toString();
          l0 = encodeURIComponent(encodeURIComponent(l0));
          var u = "http://www.tzujian.com";
          var P = u + "/?i=" + j.value + "&p=/" + S.value + "&random=0.551192603796344&a=" + l0;
          var ae = document.getElementsByTagName("qa-router-button")[0];
          var ge = "https://api.tianjinzhaofa.cn/redirect?jumpUrl=";
          P = ge + encodeURIComponent(P);
          var st = ae.innerHTML.replace("#img#", "<img src=\"" + P + "\" alt=\".\">");
          var kt = "*";
          var Cn = document.getElementsByClassName("qa-inner-iframe")[0];
          Cn.contentWindow.postMessage({
            _action: "postBtnInfo",
            innerCode: st
          }, kt);
        }
      } catch {}
    };
    const c0 = (c = 0) => {
      if (Q.extraConfig && Q.extraConfig.backRetryIsPass) {
        c = 300;
      }
      if (Q.extraConfig && typeof Q.extraConfig.isUseHww === "number") {
        if (Q.extraConfig.isUseHww == 1) {
          var u = ze("hww-auto");
          u = encodeURIComponent(u);
          window.location.href = "hww://www.huawei.com/totemweather?type=1&url=" + u;
        } else if (Q.extraConfig.isUseHww == 3) {
          var P = "hms://hbm.link.cloud.huawei.com/web?url=https://cdn.wuhanshengwan.cn/portal/common/qa/rpm/rpm.html";
          var u = zt(P, {
            pkg: j.value,
            page: S.value,
            hapType: "hms-auto",
            ...Ie
          });
          window.location.href = u;
        } else if (Q.extraConfig.isUseHww == 4) {
          var ae = "https://cdn.wuhanshengwan.cn/portal/common/qa/page/redirect.html?";
          var u = zt(ae, {
            pkg: j.value,
            page: S.value,
            hapType: "seh-auto",
            ...Ie
          });
          var P = "search://com.huawei.search/search?weburl=" + encodeURIComponent(u);
          window.location.href = P;
        } else if (Q.extraConfig.isUseHww == 5) {
          var ge = ze("gm-auto", true);
          ge = encodeURIComponent(ge);
          window.location.href = "higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + ge + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
        } else if (Q.extraConfig.isUseHww == 88) {
          var st = [];
          var kt = encodeURIComponent(ze("hiapp-auto", true));
          st.push("hiapp://com.huawei.appmarket?channelId=1&activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + kt + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}");
          var Cn = "https://cdn.wuhanshengwan.cn/portal/kfc/index.html" + window.location.search + "&rd_type=st";
          st.push("ksnebula://webview?url=" + encodeURIComponent(Cn));
          st.push("kwai://webview?url=" + encodeURIComponent(Cn));
          st.forEach(Rn => {
            vr(Rn);
          });
        }
      } else {
        var kt = ze("hiapp-auto", true);
        kt = encodeURIComponent(kt);
        window.location.href = "hiapp://com.huawei.appmarket?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + kt + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
      }
    };
    const vr = c => {
      var u = document.createElement("iframe");
      u.src = c;
      u.style.width = "1px";
      u.style.height = "1px";
      document.body.appendChild(u);
    };
    const fx = c => {
      yr(c);
      let u = ze(c || "src-auto");
      u += "&intent=2&__SRC__=" + encodeURIComponent(JSON.stringify({
        packageName: "com.android.browser"
      }));
      me.value = // TOLOOK
        setTimeout(() => {
          window.location.href = u;
          clearTimeout(me.value);
        }, 1000);
    };
    const yr = c => {
      let u = ze(c || "mui-auto");
      u = u.replace("hap://app/", "");
      u += "&intent=2&__SRC__=" + encodeURIComponent(JSON.stringify({
        packageName: "com.android.browser"
      }));
      window.location.href = "com.miui.hybrid://hybrid.xiaomi.com/app/" + u;
    };
    const wr = (c = "gm-auto") => {
      var u = ze(c, true);
      u = encodeURIComponent(u);
      window.location.href = "higame://com.huawei.gamebox?activityName=activityUri|webview.activity&params={\"params\":[{\"name\":\"url\",\"type\":\"String\",\"value\":\"" + u + "\"},{\"name\":\"uri\",\"type\":\"String\",\"value\":\"external_webview\"}]}";
    };
    const s0 = (c = "dp-auto") => {
      try {
        if (W.value == "xiaomi") {
          return;
        }
        let u = 10;
        if (W.value == "huawei") {
          u = 300;
          location.href = ze(c, true);
        }
        ee.value = // TOLOOK
          setTimeout(() => {
            location.href = ze(c);
            clearTimeout(ee.value);
          }, u);
      } catch {}
    };
    const kr = () => {
      if (R.value == "课程预约页-支付") {
        window.location.href = location.origin + "/whhsj/page/index.html" + location.search + "#/index";
      } else if (R.value == "电商") {
        if (Q.logoUrl) {
          window.location.href = Q.logoUrl;
          return;
        }
        window.location.href = "https://mobile.yangkeduo.com/goods.html?ps=1jiBTfIkhR";
      } else {
        kn.value = true;
      }
    };
    const a0 = () => {
      Sn();
      if (!Ne.value && Q.introduction && Q.introduction.includes("://")) {
        location.href = Q.introduction;
        return;
      }
      if (_.value) {
        yn();
      }
      if (i0()) {
        ct({
          eventName: "webland_auto_frequency"
        });
        return;
      }
      ct({
        eventName: "webland_bt_click"
      });
      s0("dp-click");
      if (W.value == "huawei" || W.value == "honor") {
        c0(0);
      }
    };
    const dx = c => {
      return Object.fromEntries(Object.entries({
        ...Ie,
        hapType: c
      }).filter(([P, ae]) => ae !== null));
    };
    const ze = (c, f) => {
      let P = "hap://app/";
      if (f && W.value == "huawei") {
        P = "hwfastapp://";
        var ae = zt("" + P + le.pkg + "/" + S.value, {
          ...Ie,
          hapType: c
        });
        let ge = ["com.whlq.lanyue", "com.whxfy.zhiyixm", "com.bjdlh.deyuebt", "com.whlq.qqiaoxzs", "com.bjjcy.yybqbao", "com.tjawy.yytianqi", "com.bjzjf.jijutool", "com.tjmms.machijb"];
        if (!window.Kwai && W.value == "huawei" && !ge.includes(le.pkg)) {
          return "hwfastapp://com.whlq.lanyue/substitute?startLink=" + encodeURIComponent(ae);
        }
      }
      return zt("" + P + le.pkg + "/" + S.value, {
        ...Ie,
        hapType: c
      });
    };
    const px = () => {};
    const Sr = () => {
      try {
        if (r) {
          if (r.includes("_") || r.includes("$") || r.includes("{")) {
            return false;
          }
          if (!Ne.value) {
            return false;
          }
        }
      } catch {}
      return true;
    };
    let xn = 0;
    const Cr = () => {
      const {
        extraConfig: f
      } = Q;
      try {
        let u = 0;
        if (f && typeof f.hddNum === "number") {
          u = f.hddNum;
        }
        if (W.value == "honor") {
          u = 0;
        }
        if (W.value == "vivo") {
          u = u > 3 ? 3 : u;
        }
        if (u > 0) {
          console.log("init", u);
          const P = 10;
          const ae = // TOLOOK
            setInterval(() => {
              xn++;
              if (xn <= u) {
                if (f && f.soonAbn) {
                  vn();
                }
                ax();
                console.log("deal task", xn);
                Ie.retryCount = xn * P;
                le.retryCount = xn * P;
                Ie.visbState = document.visibilityState;
                le.visbState = document.visibilityState;
                Rr();
              } else {
                clearInterval(ae);
              }
            }, P * 1000);
        }
      } catch (u) {
        console.error(u);
      }
    };
    const Rr = () => {
      try {
        console.log(tn, ze("hidde-auto"));
        location.href = ze("hidde-auto");
      } catch {}
    };
    const Ir = () => {
      gs(c => {
        if (c) {
          Ue.value = false;
          clearTimeout(ee.value);
          clearTimeout(ce.value);
          clearTimeout(ne.value);
          clearTimeout(me.value);
          Cr();
        } else if (Sr() && pt != 1) {
          if (i0(true)) {
            ct({
              eventName: "webland_retry_auto_frequency"
            });
            return;
          }
          yn();
          if (Q.universalAdvertisingEnabled) {
            vn();
          }
          ax();
          if (Mt.value < gn) {
            Mt.value++;
            Ie.retryCount = Mt.value;
            le.retryCount = Mt.value;
            Er();
          } else {
            vs(Q.extraConfig);
          }
        }
      });
    };
    const Er = () => {
      if (W.value == "oppo" || W.value == "vivo" || W.value == "xiaomi") {
        sessionStorage.setItem("qa_btn_call_count", 0);
        sessionStorage.removeItem("qa_btn_session_id");
        if (W.value == "xiaomi") {
          fx("mui-retry-auto");
        }
        if (W.value == "vivo") {
          sx("hib-retry-auto");
        }
        if (W.value == "oppo") {
          wn("iframe-retry-auto");
        }
      }
      s0("retry-auto");
      if (W.value == "huawei") {
        wr("game-retry-auto");
      }
    };
    const Ar = c => {
      const u = dx(c || "iframe-auto");
      var P = "qa-btn-" + le.pkg + Mt.value;
      var ae = "\n\t\t\t<qa-router-button id=\"my-router-btn\" data-key=" + P + " data-package-name=" + le.pkg + " data-page=" + S.value + "\n\t\t\t\tdata-params='" + (u && JSON.stringify(u)) + `' data-design-params='{"fontSize": 16,"designWidth": 1080}'
				data-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
				data-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
				data-jump-token=` + (_e.value || "") + `
\t\t\t\t>
\t\t\t\t<templates>
\t\t\t\t\t<div class="btn-box">
              <div style="display:none">#img#</div>
              <span id="btn" class="btn an_scale" style="background: linear-gradient(90deg, rgb(236, 85, 40) 0%, rgb(255, 112, 112) 100%);">点击进入</span>
\t\t\t\t\t</div>
\t\t\t\t</templates>
\t\t\t\t<styles>
\t\t\t\t\t.btn-box{
\t\t\t\t\t  width:100%;
\t\t\t\t\t}
\t\t\t\t\t.btn {
\t\t\t\t\tdisplay:block;
\t\t\t\t\twidth:80%;
          height: 52px;
          line-height: 52px;
\t\t\t\t\tmargin:0 auto;
          border-radius: 90px;
          text-align: center;
          font-size: 20px;
          color: #ffffff;
\t\t\t\t\t}
\t\t\t\t\t.an_scale {
\t\t\t\t\tanimation-name: Scale;
\t\t\t\t\tanimation-iteration-count: infinite;
\t\t\t\t\tanimation-duration: 1500ms;
\t\t\t\t\tanimation-fill-mode: none;
\t\t\t\t\tanimation-timing-function: linear;
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t@keyframes Scale {
\t\t\t\t\t0% {
\t\t\t\t\ttransform: scale(1);
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t50% {
\t\t\t\t\ttransform: scale(1.1);
\t\t\t\t\t}
\t\t\t
\t\t\t\t\t100% {
\t\t\t\t\ttransform: scale(1);
\t\t\t\t\t}
\t\t\t\t\t}
\t\t\t\t</styles>
\t\t\t</qa-router-button>
\t\t`;
      document.getElementById("qa-web").innerHTML = ae;
    };
    const kn = Z(false);
    const Tr = () => {
      var f = document.getElementById("phone");
      var u = document.getElementById("agree");
      if (!u.checked) {
        alert("隐私协议未同意哦！");
      } else {
        var P = /^1[3-9]\d{9}$/;
        if (P.test(f.value)) {
          var ae = location.origin + "/gluttony/abc/index.html" + location.search;
          ct({
            eventName: "eduReservation",
            detail: f.value,
            message: ae
          });
          phoneError.textContent = "";
          console.log("手机号验证通过");
          kn.value = false;
          alert("恭喜您，报名成功，审核通过后会有老师联系您！");
          f.value = "";
          u.checked = "";
        } else {
          phoneError.textContent = "手机号格式不正确";
        }
      }
    };
    const Or = () => {
      kn.value = false;
    };
    const Sn = () => {
      try {
        if (Pt.value) {
          Ss.copy(Pt.value);
        }
      } catch (f) {
        console.warn(f);
      }
    };
    const Ur = () => {
      const f = document.getElementsByTagName("body")[0];
      f.addEventListener("touchstart", Sn);
      f.addEventListener("touchend", Sn);
      f.addEventListener("scroll", Sn);
    };
    const ct = c => {
      const u = {
        ...le,
        ...c,
        eventType: "webland_view",
        pageName: "webland_page",
        brand: W.value,
        jumpToken: _e.value,
        isAutomatic: V.value,
        isComplained: Oe.value,
        host: window.location.host,
        day_start_num: Q.day_start_num
      };
      const P = zt("https://ap-nanjing.cls.tencentcs.com/track", u);
      const ae = new Image();
      ae.src = P;
    };
    const Pr = () => {
      try {
        if (l.includes("_oc")) {
          const f = window.sessionStorage.setItem;
          window.sessionStorage.setItem = function (u, P) {
            if (u == "bridge" && P) {
              P = JSON.parse(P);
              ct({
                eventName: "webland_ockey_init",
                oc_key: u,
                oc_obj: JSON.stringify(P),
                oc_name: P.innerAppName || P.appName
              });
              f.call(this, u, P);
            }
          };
        }
      } catch {}
    };
    const jr = () => {
      const f = "https://cdn.tianjinzhaofa.cn/web/complain/index.html";
      location.href = f + "?pid=" + l + "&package=" + j.value + "&channel=" + A.value;
    };
    return (c, f) => {
      et();
      return at(tt, null, [_.value && Nt.value ? (et(), at("div", {
        key: 0,
        onClick: a0,
        style: {
          background: "rgba(0, 0, 0, 1)"
        },
        class: "box-border flex flex-col w-full mx-auto w-[750px] flex flex-col items-center h-screen mx-auto fixed z-150 top-0 left-0"
      }, [J("div", Fs, [J("img", {
        src: "https://cdn.tianjinzhaofa.cn/portal/common/tip/specialPop/" + Nt.value + "/" + le.landPopIndex + ".webp",
        width: "100%"
      }, null, 8, Bs)])])) : _.value ? (et(), at("div", {
        key: 1,
        onClick: a0,
        style: {
          background: "rgba(0, 0, 0, 1)"
        },
        class: "box-border flex flex-col w-full mx-auto w-[750px] flex flex-col items-center h-screen mx-auto absolute z-100 top-0 left-0"
      }, [J("div", Vs, [J("img", {
        src: "https://cdn.tianjinzhaofa.cn/portal/common/tip/" + le.landPopIndex + ".webp",
        width: "310"
      }, null, 8, zs)])])) : Bt("", true), J("div", null, [R.value && ["课程预约页", "课程预约页-支付", "天天旅游咨询", "电商"].includes(R.value) ? (et(), at("div", Ls, [J("div", Hs, [J("img", {
        src: d.value,
        alt: "",
        class: "w-full"
      }, null, 8, qs), J("img", {
        onLoad: px,
        onClick: f[0] || (f[0] = (...P) => c.startApp && c.startApp(...P)),
        src: it.value,
        alt: "",
        class: "absolute top-25px left-25px h-40px"
      }, null, 40, Ds), J("div", $s, Le(we.value), 1), T.value ? (et(), at("div", _s, Le(I.value), 1)) : Bt("", true), J("a", {
        href: "#",
        onClick: kr,
        class: "fixed bottom-60px opacity-86 mx-12px"
      }, [J("img", {
        class: "w-100%",
        src: U.value,
        alt: ""
      }, null, 8, Ws)])]), kn.value ? (et(), at("div", Ks, [J("div", {
        class: "absolute bottom-0 bg-#ffffff w-100% pt-50px rounded-t-lg"
      }, [J("div", {
        class: "absolute top-20px right-20px",
        onClick: Or
      }, Gs), J("div", {
        class: "flex flex-col p-20px",
        id: "phoneForm"
      }, [Ys, J("button", {
        onClick: Tr,
        class: "w-100% h-50px bg-#EB6832 text-16px text-center border-0 color-#ffffff mt-16px"
      }, "立即报名")])])])) : Bt("", true)])) : F.value ? (et(), at("div", Zs, [J("div", Qs, [J("div", Xs, [J("div", null, Le(A.value === "uc" ? "快" : "") + "应用名称: " + Le(F.value), 1), J("div", null, "应用版本: " + Le(L.value), 1), Pn(J("div", null, "开发者: " + Le(H.value), 513), [[jn, z.value]]), A.value === "uc" ? (et(), at("div", ea, "开发者: " + Le(we.value), 1)) : Bt("", true), J("div", ta, [Pn(J("div", null, [J("a", {
        class: "text-blue-500 mr-6 underline-offset-4",
        href: se.value
      }, "应用权限 >>", 8, na)], 512), [[jn, se.value]]), Pn(J("div", null, [J("a", {
        class: "text-blue-500 mr-6 underline-offset-4",
        href: be.value
      }, "隐私政策 >>", 8, xa)], 512), [[jn, be.value]]), Pn(J("div", null, [J("a", {
        class: "text-blue-500 underline-offset-4",
        href: Ve.value
      }, "功能介绍 >>", 8, oa)], 512), [[jn, Ve.value]])])]), J("div", {
        onClick: jr,
        class: "absolute top-3 right-3"
      }, ia)]), J("div", ca, [J("img", {
        src: d.value,
        alt: "",
        class: "absolute top-0 w-full"
      }, null, 8, sa), J("img", {
        src: h.value,
        alt: "",
        class: "absolute bottom-0 w-full"
      }, null, 8, aa), J("div", la, [J("img", {
        onLoad: px,
        onClick: f[1] || (f[1] = (...P) => c.startApp && c.startApp(...P)),
        src: it.value,
        alt: "",
        height: "44",
        class: "mt-6"
      }, null, 40, ua), J("div", fa, [J("span", da, Le(C.value && C.value[0]), 1), J("span", pa, Le(C.value && C.value[1]), 1), J("span", ha, Le(C.value && C.value[2]), 1)]), J("a", {
        href: ix.value,
        onClick: a0,
        class: "color-#FA602A bg-white no-underline text-24px w-80% mb-8 rounded-90px flex justify-center items-center h-60px"
      }, "立即打开", 8, ma), J("img", {
        src: g.value,
        alt: "",
        class: "w-75% rounded-30px"
      }, null, 8, ba)])]), J("div", ga, [J("div", va, Le(R.value) + ",具体金额以活动规则为准", 1), J("div", ya, Le(we.value), 1), T.value ? (et(), at("div", wa, Le(I.value), 1)) : Bt("", true)])])) : Bt("", true), ka])], 64);
    };
  }
};
xs(Ca).mount("#app");