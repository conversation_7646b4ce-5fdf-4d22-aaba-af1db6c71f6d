<!doctype html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>权限</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
        }

        .table-container {
            padding: 30px;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }

        thead {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        thead th {
            color: white;
            font-weight: 600;
            padding: 18px 16px;
            text-align: left;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        tbody tr {
            transition: all 0.3s ease;
        }

        tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tbody tr:hover {
            background-color: #e3f2fd;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        tbody td {
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
            font-size: 14px;
            line-height: 1.5;
        }

        tbody tr:last-child td {
            border-bottom: none;
        }

        /* 空行样式 */
        tbody tr:last-child {
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                border-radius: 8px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 24px;
            }

            .table-container {
                padding: 15px;
            }

            table {
                font-size: 12px;
            }

            thead th,
            tbody td {
                padding: 12px 8px;
            }

            /* 在小屏幕上使表格可以水平滚动 */
            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            table {
                min-width: 600px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 20px;
            }

            table {
                font-size: 11px;
                min-width: 500px;
            }

            thead th,
            tbody td {
                padding: 10px 6px;
            }
        }

        /* 滚动条样式 */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>应用权限说明</h1>
        </div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>设备权限</th>
                        <th>对应的业务功能</th>
                        <th>调用权限的目的</th>
                        <th>何时询问授权</th>
                        <th>用户可否关闭权限</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>位置权限</td>
                        <td>天气预报、空气质量</td>
                        <td>为您提供所在城市和区域的天气信息</td>
                        <td>首次安装时弹窗询问；如未获取，将在点击具体业务功能时询问</td>
                        <td>是</td>
                    </tr>
                    <tr>
                        <td>电话权限（包括设备型号、设备MAC地址、设备唯一标识符IMEI、Android ID、OAID、IDFA、OpenUDID、GUID、IMSI、CCID，设备序列号）</td>
                        <td>基本信息服务、登录及语音播报</td>
                        <td>用于统计分析、网络安全、一键登录以及获取通话状态，帮助您在语音播报时正常接听电话</td>
                        <td>首次安装时弹窗询问；如未获取将在点击具体业务功能时询问</td>
                        <td>是</td>
                    </tr>
                    <tr>
                        <td>存储权限</td>
                        <td>用户反馈/留言板</td>
                        <td>上传图片，辅助反馈使用中遇到的问题</td>
                        <td>在用户反馈页面，上传图片时弹窗询问</td>
                        <td>是</td>
                    </tr>
                    <tr>
                        <td>传感器数据（获取设备传感器/请求传感器服务，包括：陀螺仪传感器、加速度传感器、重力传感器）</td>
                        <td>获取传感器数据（包括：陀螺仪传感器、加速度传感器、重力传感器）</td>
                        <td>天气动态皮肤雨雪动画、广告摇一摇功能</td>
                        <td>使用时获取</td>
                        <td>是</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>