{"name": "quick-landing", "version": "1.0.0", "description": "快应用落地页", "main": "views/quickapp-test.html", "scripts": {"build": "vite build", "serve": "vite --host 0.0.0.0 --open views/quickapp-test.html", "preview": "vite preview --host 0.0.0.0 --port 8080"}, "repository": {"type": "git", "url": "*********************:muchun/quick-landing.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "^5.2.4", "eslint": "^9.25.1", "fast-glob": "^3.3.3", "globals": "^16.0.0", "vite": "6.2.6"}, "dependencies": {"crypto.js": "^3.3.4", "js-confuser": "^2.0.0", "vue": "^3.5.15"}}