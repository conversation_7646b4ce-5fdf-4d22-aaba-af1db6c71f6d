import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// 导入应用列表
const appListPath = path.join(rootDir, 'js/appList.js');
const appListContent = fs.readFileSync(appListPath, 'utf-8');
// 提取应用列表数据
const appListMatch = appListContent.match(/export default (\[[\s\S]*\])/);
if (!appListMatch) {
  throw new Error('无法解析应用列表');
}
const appList = JSON.parse(appListMatch[1]);

// 隐私政策HTML模板
const privacyPolicyTemplate = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
</head>

<body>
<script type="module" src="/js/privacy-policy.js"></script>
</body>

</html>`;

// 用户协议HTML模板
const userPolicyTemplate = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
</head>

<body>
<script type="module" src="/js/user-policy.js"></script>
</body>

</html>`;

// 生成隐私政策和用户协议文件
function generatePrivacyFiles() {
  const privacyDir = path.join(rootDir, 'privacy');
  
  // 确保privacy目录存在
  if (!fs.existsSync(privacyDir)) {
    fs.mkdirSync(privacyDir, { recursive: true });
  }

  console.log('开始生成隐私政策和用户协议文件...');
  
  appList.forEach(app => {
    const appDir = path.join(privacyDir, app.id);
    
    // 创建应用目录
    if (!fs.existsSync(appDir)) {
      fs.mkdirSync(appDir, { recursive: true });
    }
    
    // 生成隐私政策文件
    const privacyPolicyPath = path.join(appDir, 'PrivacyPolicy.html');
    fs.writeFileSync(privacyPolicyPath, privacyPolicyTemplate);
    
    // 生成用户协议文件
    const userPolicyPath = path.join(appDir, 'UserPolicy.html');
    fs.writeFileSync(userPolicyPath, userPolicyTemplate);
    
    console.log(`✓ 生成应用 ${app.id} (${app.name}) 的隐私政策和用户协议文件`);
  });
  
  console.log(`✅ 完成！共生成 ${appList.length} 个应用的隐私政策和用户协议文件`);
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generatePrivacyFiles();
}

export { generatePrivacyFiles };
