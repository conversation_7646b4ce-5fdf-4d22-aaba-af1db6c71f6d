# 隐私政策自动生成系统

## 概述

本系统实现了隐私政策和用户协议文件的编译时自动生成，基于 `js/appList.js` 中的应用数据自动创建对应的HTML文件。

## 文件结构

```
scripts/
├── generate-privacy.js    # 生成隐私政策文件的脚本
├── clean-privacy.js       # 清理生成文件的脚本
└── README.md             # 本文档

privacy/
├── permission.html       # 手动维护的权限说明页面
└── 10xxx/               # 自动生成的应用文件夹（已加入.gitignore）
    ├── PrivacyPolicy.html
    └── UserPolicy.html
```

## 工作原理

### 1. 数据源
- 从 `js/appList.js` 读取应用列表
- 每个应用包含 `id`、`name`、`package`、`company` 等信息

### 2. 生成过程
- 为每个应用创建独立的文件夹 `privacy/{appId}/`
- 生成两个HTML文件：`PrivacyPolicy.html` 和 `UserPolicy.html`
- HTML文件使用模板结构，通过JavaScript动态加载内容

### 3. 内容生成
- 隐私政策内容由 `js/privacy-policy.js` 动态生成
- 用户协议内容由 `js/user-policy.js` 动态生成
- 根据URL路径自动识别应用ID并显示对应内容

## 使用方法

### 自动生成（推荐）
构建时会自动生成所有文件：
```bash
npm run build
```

### 手动生成
```bash
node scripts/generate-privacy.js
```

### 清理生成的文件
```bash
node scripts/clean-privacy.js
```

## 版本管理

- ✅ **应该提交**：`scripts/` 目录下的脚本文件
- ✅ **应该提交**：`js/privacy-policy.js` 和 `js/user-policy.js`
- ✅ **应该提交**：`privacy/permission.html`
- ❌ **不应提交**：`privacy/10*/` 自动生成的文件夹（已在.gitignore中）

## 添加新应用

1. 在 `js/appList.js` 中添加新的应用条目
2. 运行构建命令，系统会自动为新应用生成隐私政策文件
3. 无需手动创建或维护HTML文件

## 修改隐私政策内容

- 修改 `js/privacy-policy.js` 来更新隐私政策模板
- 修改 `js/user-policy.js` 来更新用户协议模板
- 重新构建后所有应用的文件都会使用新的内容

## 注意事项

1. **不要手动编辑**生成的HTML文件，它们会在下次构建时被覆盖
2. **确保应用ID格式**为数字（如10328），脚本会根据此格式识别
3. **构建前清理**：如果需要完全重新生成，可以先运行清理脚本
4. **开发环境**：生成的文件在本地开发时也会被创建，但不会提交到版本库

## 故障排除

### 生成失败
- 检查 `js/appList.js` 语法是否正确
- 确保应用对象包含必要的字段（id, name, package, company）

### 文件未被忽略
- 确认 `.gitignore` 包含 `privacy/10*/` 规则
- 运行 `git rm --cached privacy/10*` 移除已跟踪的文件

### 构建时找不到文件
- 确保在 `vite.config.js` 中正确调用了 `generatePrivacyFiles()`
- 检查文件路径和权限
