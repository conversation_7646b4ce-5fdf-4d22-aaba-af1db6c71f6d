import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// 导入应用列表
const appListPath = path.join(rootDir, 'js/appList.js');
const appListContent = fs.readFileSync(appListPath, 'utf-8');
// 提取应用列表数据
const appListMatch = appListContent.match(/export default (\[[\s\S]*\])/);
if (!appListMatch) {
  throw new Error('无法解析应用列表');
}
const appList = JSON.parse(appListMatch[1]);

// 落地页模板
const landingTemplate = `<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>快应用</title>
    <script crossorigin src="/js/landing.js" type="module"></script>
    <script crossorigin src="/js/landing-index.js" type="module"></script>
    <style>
body {
  margin: 0;
  padding: 0;
}
    </style>
</head>
<body>
</body>
</html>
`;


// 生成隐私政策和用户协议文件
function generateLandingFiles() {
  const landingDir = path.join(rootDir, 'landing');
  
  // 确保privacy目录存在
  if (!fs.existsSync(landingDir)) {
    fs.mkdirSync(landingDir, { recursive: true });
  }

  console.log('开始生成落地页文件...');
  
  appList.forEach(app => {
    const appDir = path.join(landingDir, app.id);
    
    // 创建应用目录
    if (!fs.existsSync(appDir)) {
      fs.mkdirSync(appDir, { recursive: true });
    }
    
    // 生成落地页文件
    const landingPath = path.join(appDir, 'index.html');
    fs.writeFileSync(landingPath, landingTemplate);
    
    console.log(`✓ 生成应用 ${app.id} (${app.name}) 的落地页文件`);
  });
  
  console.log(`✅ 完成！共生成 ${appList.length} 个应用的落地页文件`);
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generateLandingFiles();
}

export { generateLandingFiles };
